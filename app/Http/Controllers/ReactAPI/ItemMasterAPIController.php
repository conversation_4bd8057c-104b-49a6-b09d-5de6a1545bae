<?php

namespace App\Http\Controllers\ReactAPI;

use App\Actions\AuditTrail\CreateAuditTrailEvent;
use App\Actions\CommonAction\GetFinancialYearOpeningBalanceDetails;
use App\Actions\CustomFieldsItemMaster\GetItemCustomFields;
use App\Actions\v1\ItemMaster\GetItemMasterConfigurationAction;
use App\Actions\v1\ItemMaster\GetReOrderItemsAction;
use App\Actions\v1\ItemMaster\StoreOrUpdateItemAction;
use App\Actions\v1\ItemMaster\StoreOrUpdateItemConfiguration;
use App\Exports\ItemWiseLowStockReportExport;
use App\Http\Controllers\API\v1\AppBaseAPIController;
use App\Http\Requests\ReactAPI\ItemMasterAPIRequest;
use App\Jobs\CreateItemMasterInOtherCompaniesJob;
use App\Models\Company;
use App\Models\CompanyGroup;
use App\Models\Ledger;
use App\Models\Master\Expense;
use App\Models\Master\Income;
use App\Models\Master\ItemMaster;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ItemMasterAPIController extends AppBaseAPIController
{
    public function getDetails()
    {
        $response = [];
        $currentCompanyId = getCurrentCompany()->id;
        $response['companyItemMasterGroups'] = getParentGroupsValue(Ledger::ITEM_DEFAULT_GROUP);
        $response['defaultSelectedGroup'] = CompanyGroup::whereName(Ledger::ITEM_DEFAULT_GROUP)
            ->whereName('Default Group')
            ->whereCompanyId($currentCompanyId)
            ->value('id');
        $response['unitOfMeasurement'] = getItemMasterUnit();
        $response['gstCessRate'] = getGstCessRate();
        $response['gstTaxes'] = getGstTaxes();
        $response['incomeLedgers'] = getAllParentGroupLedgerList(Ledger::INCOME);
        $response['incomeType'] = Ledger::whereModelType(Income::class)
            ->whereIsDefault(true)
            ->whereName(Ledger::SALE)
            ->first();
        $response['expenseLedgers'] = getAllParentGroupLedgerList(Ledger::EXPENSE);
        $response['expenseType'] = Ledger::whereModelType(Expense::class)
            ->whereIsDefault(true)
            ->whereName(Ledger::PURCHASE)
            ->first();
        $response['incomeGroupId'] = CompanyGroup::whereCompanyId($currentCompanyId)->whereName(Ledger::INCOME)->select('id')->toBase()->first()?->id;
        $response['expenseGroupId'] = CompanyGroup::whereCompanyId($currentCompanyId)->whereName(Ledger::EXPENSE)->select('id')->toBase()->first()?->id;

        $response['methodOfStockValuation'] = ItemMaster::METHOD_OF_STOCK_VALUATION;

        return $this->sendResponse($response, 'Item Master Details Fetched Successfully.');
    }

    public function create(ItemMasterAPIRequest $request)
    {
        $input = $request->all();

        $itemExist = ItemMaster::whereRaw('LOWER(`item_name`) = ? ', [trim(strtolower($input['item_name']))])->exists();

        if ($itemExist) {
            return $this->sendError('The item name has already been taken.');
        }

        if (! empty($input['sku'])) {
            $itemExist = ItemMaster::where('sku', $input['sku'])->exists();
            if ($itemExist) {
                return $this->sendError('The item sku has already been taken.');
            }
        }

        $response = StoreOrUpdateItemAction::run($input, StoreOrUpdateItemAction::STORE);

        if (isset($input['is_use_in_other_companies']) && ($input['is_use_in_other_companies'] === true || $input['is_use_in_other_companies'] == 1)) {
            try {
                unset($input['item_image']);
                CreateItemMasterInOtherCompaniesJob::dispatch($input, getCurrentCompany(), $response);
            } catch (\Exception $e) {
                Log::error('Failed to dispatch CreateItemMasterInOtherCompaniesJob on create: '.$e->getMessage());
            }
        }

        return $this->sendResponse($response, 'Item Master Created Successfully.');
    }

    public function edit($id)
    {
        $itemMaster = ItemMaster::with('model.unitOfMeasurement')->findOrFail($id);
        // TO_COMMENT =>
        $response['itemMaster'] = $itemMaster;
        $response['item_master'] = $itemMaster;

        $response['custom_fields_value'] = GetItemCustomFields::run($id);

        if ($itemMaster->item_type == ItemMaster::ITEM_MASTER_GOODS) {
            $openingBalanceDetails = GetFinancialYearOpeningBalanceDetails::run($itemMaster->id, true);
            // TO_COMMENT =>
            $response['openingBalanceDetails'] = $openingBalanceDetails;
            $response['opening_balance_details'] = $openingBalanceDetails;
        }

        return $this->sendResponse($response, 'Item Master Fetched Successfully.');
    }

    public function update(ItemMasterAPIRequest $request, $id)
    {
        $input = $request->all();

        $itemMaster = ItemMaster::with('model')->findOrFail($id);

        $itemExist = ItemMaster::whereRaw('LOWER(`item_name`) = ? ', [trim(strtolower($input['item_name']))])
            ->whereNot('id', $itemMaster->id)
            ->exists();

        if ($itemExist) {
            return $this->sendError('The item name has already been taken.');
        }

        if (! empty($input['sku'])) {
            $itemExist = ItemMaster::where('sku', $input['sku'])
                ->whereNot('id', $itemMaster->id)
                ->exists();
            if ($itemExist) {
                return $this->sendError('The item sku has already been taken.');
            }
        }

        $response = StoreOrUpdateItemAction::run($input, StoreOrUpdateItemAction::UPDATE, $itemMaster);

        if (isset($input['is_use_in_other_companies']) && ($input['is_use_in_other_companies'] === true || $input['is_use_in_other_companies'] == 1)) {
            try {
                unset($input['item_image']);
                CreateItemMasterInOtherCompaniesJob::dispatch($input, getCurrentCompany(), $response);
            } catch (\Exception $e) {
                Log::error('Failed to dispatch CreateItemMasterInOtherCompaniesJob on update: '.$e->getMessage());
            }
        }

        return $this->sendResponse($response, 'Item Master Updated Successfully.');
    }

    public function getReOrderItems(Request $request)
    {
        $input = $request->all();

        $response = GetReOrderItemsAction::run($input);

        return $this->sendResponse($response, 'Reorder items fetched successfully.');
    }

    public function export(Request $request)
    {
        $input = $request->all();

        if (! isset($input['file_type'])) {
            return $this->sendError('Please select file type.');
        }

        $type = strtolower($input['file_type']);

        $data = [];
        $company = getCurrentCompany();

        $itemWiseLowStockReportCacheKey = generateCacheKey('item_wise_low_stock_report');
        if ($itemWiseLowStockReportCacheKey) {
            $data['data'] = Cache::get($itemWiseLowStockReportCacheKey);
        } else {
            $data['data'] = GetReOrderItemsAction::run($input);
        }

        $data['company'] = $company;
        $data['companyAddress'] = $company?->addresses->where('address_type', Company::BILLING_ADDRESS)->first();

        $user = getLoginUser();
        $pdfOrExcel = $type === 'pdf' ? 'PDF' : 'Excel';
        CreateAuditTrailEvent::run($user, 'Export', '<b>Item Wise Low Stock</b> '.$pdfOrExcel.' was exported.');

        if ($type === 'excel') {
            $fileName = getAPIExcelFilePath().'item-wise-low-stock-report-'.strtotime('now').'.xlsx';
            (new ItemWiseLowStockReportExport($data))->store($fileName, config('app.media_disc'));

            $url = Storage::disk(config('app.media_disc'))->url($fileName);

            return $this->sendResponse($url, 'Item Wise Low Stock Excel Retrieved Successfully.', 'url');
        }

        $fileName = getAPIPdfFilePath().'item-wise-low-stock-report-'.strtotime('now').'.pdf';

        $customPaperSize = [0, 0, 700, 950];
        $pdf = PDF::loadView('pdf.item-wise-low-stock-report', $data)->setPaper($customPaperSize);

        $pdf->save($fileName, config('app.media_disc'));

        $url = Storage::disk(config('app.media_disc'))->url($fileName);

        return $this->sendResponse($url, 'Item Wise Low Stock PDF Retrieved Successfully.', 'url');
    }

    public function getItemMasterConfiguration()
    {
        $response = GetItemMasterConfigurationAction::run();

        return $this->sendResponse($response, 'Item Master Configuration Fetched Successfully.');
    }

    public function storeUpdateItemConfiguration(Request $request)
    {
        $request->validate([
            'primary_uom_id' => 'nullable|exists:unit_of_measurements,id',
            'secondary_uom_id' => 'nullable|exists:unit_of_measurements,id|different:primary_uom_id',
            'conversion_rate' => 'nullable|numeric',
        ]);

        $input = $request->all();

        StoreOrUpdateItemConfiguration::run($input);

        return $this->sendSuccess('Item Master Configuration Updated Successfully.');
    }
}
