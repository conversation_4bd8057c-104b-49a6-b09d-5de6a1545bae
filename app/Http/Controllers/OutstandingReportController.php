<?php

namespace App\Http\Controllers;

use App\Actions\AuditTrail\CreateAuditTrailEvent;
use App\Actions\Income\DebitNote\GetInvoicePDFDataFoeIncomeDebitNote;
use App\Actions\Income\GstCalculationForPdf;
use App\Actions\Income\Sale\GetInvoicePDFDataForSale;
use App\Exports\OutstandingPayableReportExport;
use App\Exports\OutstandingReceivableReportExport;
use App\Jobs\InvoiceMailJob;
use App\Mail\SendIncomeDebitNoteReminderMail;
use App\Mail\SendSaleTransactionReminderMail;
use App\Models\BillWiseOpeningBalance;
use App\Models\Company;
use App\Models\ExpenseCreditNoteTransaction;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\IncomeCreditNoteTransaction;
use App\Models\IncomeDebitNoteTransaction;
use App\Models\Master\Customer;
use App\Models\PaymentTransaction;
use App\Models\PurchaseReturnTransaction;
use App\Models\PurchaseTransaction;
use App\Models\ReceiptTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\SaleTransaction;
use Barryvdh\DomPDF\Facade\Pdf as PDF;
use Carbon\Carbon;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Laracasts\Flash\Flash;
use Maatwebsite\Excel\Facades\Excel;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist;
use Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class OutstandingReportController extends AppBaseController
{
    /**
     * @return View
     */
    public function index(): \Illuminate\View\View
    {
        return view('company.outstanding-report.index');
    }

    public function prepareReceivableReportData($input): array
    {
        $data = [];

        $data['saleTransactions'] = SaleTransaction::with([
            'customer.model', 'receiptTransactionItem', 'journalTransactionItem', 'saleReturn',
            'creditNote', 'saleItems', 'saleLedgers', 'customer.group',
        ])
            ->whereBetween('date', [$input['start_date'], $input['end_date']])
            ->where(function (Builder $q) use ($input) {
                if (isset($input['party_name']) && ! empty($input['party_name'])) {
                    $q->whereIn('customer_ledger_id', $input['party_name']);
                }
                if (isset($input['group_name']) && ! empty($input['group_name'])) {
                    $q->whereHas('customer', function ($query) use ($input) {
                        $query->whereIn('group_id', $input['group_name']);
                    });
                }
            })
            ->get();

        $data['saleReturnTransactions'] = SaleReturnTransaction::with([
            'customer.model', 'saleReturnItems', 'saleReturnLedgers',
            'journalTransactionItem', 'paymentTransactionItem', 'sale', 'customer.group',
        ])
            ->whereBetween('date', [$input['start_date'], $input['end_date']])
            ->whereNull('original_inv_no')
            ->where(function (Builder $q) use ($input) {
                if (isset($input['party_name']) && ! empty($input['party_name'])) {
                    $q->whereIn('customer_ledger_id', $input['party_name']);
                }
                if (isset($input['group_name']) && ! empty($input['group_name'])) {
                    $q->whereHas('customer', function ($query) use ($input) {
                        $query->whereIn('group_id', $input['group_name']);
                    });
                }
            })
            ->get();

        $data['incomeDebitNoteTransactions'] = IncomeDebitNoteTransaction::with([
            'incomeDebitNoteItems', 'incomeDebitNoteLedgers',
            'customer.model', 'sale', 'receiptTransactionItem', 'journalTransactionItem', 'customer.group',
        ])
            ->whereBetween('date', [$input['start_date'], $input['end_date']])
            ->where(function (Builder $q) use ($input) {
                if (isset($input['party_name']) && ! empty($input['party_name'])) {
                    $q->whereIn('customer_ledger_id', $input['party_name']);
                }
                if (isset($input['group_name']) && ! empty($input['group_name'])) {
                    $q->whereHas('customer', function ($query) use ($input) {
                        $query->whereIn('group_id', $input['group_name']);
                    });
                }
            })
            ->get();

        $data['incomeCreditNoteTransactions'] = IncomeCreditNoteTransaction::with([
            'customer.model', 'incomeCreditNoteItems', 'sale',
            'incomeCreditNoteLedgers', 'paymentTransactionItem', 'journalTransactionItem', 'customer.group',
        ])
            ->whereBetween('date', [$input['start_date'], $input['end_date']])
            ->whereNull('original_inv_no')
            ->where(function (Builder $q) use ($input) {
                if (isset($input['party_name']) && ! empty($input['party_name'])) {
                    $q->whereIn('customer_ledger_id', $input['party_name']);
                }
                if (isset($input['group_name']) && ! empty($input['group_name'])) {
                    $q->whereHas('customer', function ($query) use ($input) {
                        $query->whereIn('group_id', $input['group_name']);
                    });
                }
            })
            ->get();

        $data['receiptTransactions'] = ReceiptTransaction::with(['receiptTransactionItem', 'ledgers', 'ledgers.model'])->whereDoesntHave('receiptTransactionItem')
            ->whereBetween('date', [$input['start_date'], $input['end_date']])
            ->where(function (Builder $q) use ($input) {
                if (isset($input['party_name']) && ! empty($input['party_name'])) {
                    $q->whereIn('ledger_id', $input['party_name']);
                }
                if (isset($input['group_name']) && ! empty($input['group_name'])) {
                    $q->whereHas('ledgers', function ($query) use ($input) {
                        $query->whereIn('group_id', $input['group_name']);
                    });
                }
            })
            ->get();

        $data['billWiseOpeningBalanceTransactions'] = BillWiseOpeningBalance::with('financialYearOpeningBalance.ledger.model')
            ->whereHas('financialYearOpeningBalance', function ($query) {
                $query->where('company_id', getCurrentCompany()->id);
            })
            ->whereDate('voucher_date', '<=', $input['end_date'])
            ->whereIn('transaction_type', [BillWiseOpeningBalance::SALE, BillWiseOpeningBalance::SALE_RETURN, BillWiseOpeningBalance::INCOME_DEBIT_NOTE, BillWiseOpeningBalance::INCOME_CREDIT_NOTE])
            ->when(isset($input['party_name']) && ! empty($input['party_name']), function ($q) use ($input) {
                $q->whereHas('financialYearOpeningBalance', function ($query) use ($input) {
                    $query->whereIn('ledger_id', $input['party_name']);
                });
            })
            ->when(isset($input['group_name']) && ! empty($input['group_name']), function ($q) use ($input) {
                $q->whereHas('financialYearOpeningBalance', function ($query) use ($input) {
                    $query->whereHas('ledger', function ($query) use ($input) {
                        $query->whereIn('group_id', $input['group_name']);
                    });
                });
            })
            ->get();

        $dataItem = [];

        /** @var SaleTransaction $item */
        foreach ($data['saleTransactions'] as $item) {
            $value = getOutstandingReceivedCreditPeriodData($item);
            if ($item->due_amount != 0) {
                $dataItem[] = [
                    'id' => $item->id,
                    'ledger_id' => $item->customer_ledger_id,
                    'party_name' => $item->customer->name,
                    'group_name' => $item->customer->group->name,
                    'mobile_number' => ! empty($item->customer->model->phone_1) ? (! empty($item->customer->model->phone_2) ?
                        ($item->customer->model->phone_1.' / '.$item->customer->model->phone_2) : $item->customer->model->phone_1) : (! empty($item->customer->model->phone_2) ? $item->customer->model->phone_2 : ''),
                    'transaction_type' => SaleTransaction::SALE_TRANSACTION,
                    'invoice_number' => $item->full_invoice_number,
                    'invoice_date' => Carbon::parse($item->date)->format('d-m-Y') ?? '',
                    'narration' => $item->narration ?? null,
                    'invoice_amount' => $item->grand_total ?? 0,
                    'received_amount' => $item->paid_amount,
                    'pending_amount' => $item->due_amount ?? 0,
                    'credit_period' => $value['creditPeriodTime'],
                    'due_date' => Carbon::parse($value['dueDate'])->format('d-m-Y') ?? '',
                    'overdue_days' => $value['overdueDays'],
                    'rate_of_interest' => $value['rateOfInterest'],
                    'interest_of_amount' => $value['interestAmt'],
                    'customer_id' => $item->customer->model->id ?? '',
                    'phone' => empty($item->customer->model->phone_1) ? (! empty($item->customer->model->phone_2) ?
                        $item->customer->model->phone_2 : '') : $item->customer->model->phone_1,
                ];
            }
        }

        /** @var SaleReturnTransaction $item */
        foreach ($data['saleReturnTransactions'] as $item) {
            if ($item->due_amount != 0) {
                $value = getOutstandingReceivedCreditPeriodData($item);
                $dataItem[] = [
                    'id' => $item->id,
                    'ledger_id' => $item->customer_ledger_id,
                    'party_name' => $item->customer->name,
                    'group_name' => $item->customer->group->name,
                    'mobile_number' => ! empty($item->customer->model->phone_1) ? (! empty($item->customer->model->phone_2) ?
                        ($item->customer->model->phone_1.' / '.$item->customer->model->phone_2) : $item->customer->model->phone_1) : (! empty($item->customer->model->phone_2) ? $item->customer->model->phone_2 : ''),
                    'transaction_type' => SaleTransaction::SALE_RETURN_TRANSACTION,
                    'invoice_number' => $item->full_invoice_number,
                    'invoice_date' => Carbon::parse($item->date)->format('d-m-Y'),
                    'narration' => $item->narration ?? null,
                    'invoice_amount' => $input['report_type'] == 1 ? ($item->grand_total ?? 0) : (-$item->grand_total ?? 0),
                    'received_amount' => $input['report_type'] == 1 ? ($item->paid_amount ?? 0) : (-$item->paid_amount ?? 0),
                    'pending_amount' => $input['report_type'] == 1 ? ($item->due_amount ?? 0) : (-$item->due_amount ?? 0),
                    'credit_period' => $value['creditPeriodTime'],
                    'due_date' => Carbon::parse($value['dueDate'])->format('d-m-Y'),
                    'overdue_days' => $value['overdueDays'],
                    'rate_of_interest' => $value['rateOfInterest'],
                    'interest_of_amount' => null,
                    'customer_id' => $item->customer->model->id ?? null,
                    'phone' => empty($item->customer->model->phone_1) ? (! empty($item->customer->model->phone_2) ?
                        $item->customer->model->phone_2 : '') : $item->customer->model->phone_1,
                ];
            }
        }

        /** @var IncomeCreditNoteTransaction $item */
        foreach ($data['incomeCreditNoteTransactions'] as $item) {
            if ($item->due_amount != 0) {
                $value = getOutstandingReceivedCreditPeriodData($item);
                $dataItem[] = [
                    'id' => $item->id,
                    'ledger_id' => $item->customer_ledger_id,
                    'party_name' => $item->customer->name,
                    'group_name' => $item->customer->group->name,
                    'mobile_number' => ! empty($item->customer->model->phone_1) ? (! empty($item->customer->model->phone_2) ?
                        ($item->customer->model->phone_1.' / '.$item->customer->model->phone_2) : $item->customer->model->phone_1) : (! empty($item->customer->model->phone_2) ? $item->customer->model->phone_2 : ''),
                    'transaction_type' => SaleTransaction::INCOME_CREDIT_NOTE_TRANSACTION,
                    'invoice_number' => $item->full_invoice_number,
                    'invoice_date' => Carbon::parse($item->date)->format('d-m-Y'),
                    'narration' => $item->narration ?? null,
                    'invoice_amount' => $input['report_type'] == 1 ? ($item->grand_total ?? 0) : (-$item->grand_total ?? 0),
                    'received_amount' => $input['report_type'] == 1 ? ($item->paid_amount ?? 0) : (-$item->paid_amount ?? 0),
                    'pending_amount' => $input['report_type'] == 1 ? ($item->due_amount ?? 0) : (-$item->due_amount ?? 0),
                    'credit_period' => $value['creditPeriodTime'],
                    'due_date' => Carbon::parse($value['dueDate'])->format('d-m-Y'),
                    'overdue_days' => $value['overdueDays'],
                    'rate_of_interest' => $value['rateOfInterest'],
                    'interest_of_amount' => null,
                    'customer_id' => $item->customer->model->id ?? '',
                    'phone' => empty($item->customer->model->phone_1) ? (! empty($item->customer->model->phone_2) ?
                        $item->customer->model->phone_2 : '') : $item->customer->model->phone_1,
                ];
            }
        }

        /** @var IncomeDebitNoteTransaction $item */
        foreach ($data['incomeDebitNoteTransactions'] as $item) {
            if ($item->due_amount != 0) {
                $value = getOutstandingReceivedCreditPeriodData($item);

                $dataItem[] = [
                    'id' => $item->id,
                    'ledger_id' => $item->customer_ledger_id,
                    'party_name' => $item->customer->name,
                    'group_name' => $item->customer->group->name,
                    'mobile_number' => ! empty($item->customer->model->phone_1) ? (! empty($item->customer->model->phone_2) ?
                        ($item->customer->model->phone_1.' / '.$item->customer->model->phone_2) : $item->customer->model->phone_1) : (! empty($item->customer->model->phone_2) ? $item->customer->model->phone_2 : ''),
                    'transaction_type' => SaleTransaction::INCOME_DEBIT_NOTE_TRANSACTION,
                    'invoice_number' => $item->full_invoice_number,
                    'invoice_date' => Carbon::parse($item->date)->format('d-m-Y'),
                    'narration' => $item->narration ?? null,
                    'invoice_amount' => $item->grand_total ?? 0,
                    'received_amount' => $item->paid_amount,
                    'pending_amount' => $item->due_amount ?? 0,
                    'credit_period' => $value['creditPeriodTime'],
                    'due_date' => Carbon::parse($value['dueDate'])->format('d-m-Y'),
                    'overdue_days' => $value['overdueDays'],
                    'rate_of_interest' => $value['rateOfInterest'],
                    'interest_of_amount' => $value['interestAmt'],
                    'customer_id' => $item->customer->model->id ?? null,
                    'phone' => empty($item->customer->model->phone_1) ? (! empty($item->customer->model->phone_2) ?
                        $item->customer->model->phone_2 : '') : $item->customer->model->phone_1,
                ];
            }
        }

        /** @var BillWiseOpeningBalance $item */
        foreach ($data['billWiseOpeningBalanceTransactions'] as $item) {
            if ($item->total_paid_amount == $item->total_amount) {
                continue;
            }
            $overdueDays = 0;
            if (Carbon::now() > Carbon::parse($item->due_date)) {
                $overdueDays = Carbon::parse($item->due_date)->diffInDays(Carbon::now());
            }

            $rateOfInterest = ! empty($item->financialYearOpeningBalance->ledger->model->rate_of_interest) ? $item->financialYearOpeningBalance->ledger->model->rate_of_interest : '0';
            $interestAmt = ($item->pending_amount * $rateOfInterest * $overdueDays / 365);

            $invoiceAmount = 0;
            $receivedAmount = 0;
            $pendingAmount = 0;

            if ($item->transaction_type == BillWiseOpeningBalance::SALE || $item->transaction_type == BillWiseOpeningBalance::INCOME_DEBIT_NOTE || $input['report_type'] == 1) {
                $invoiceAmount = $item->total_amount;
                $receivedAmount = $item->total_paid_amount;
                $pendingAmount = $item->total_amount - $item->total_paid_amount;
            } elseif ($item->transaction_type == BillWiseOpeningBalance::SALE_RETURN || $item->transaction_type == BillWiseOpeningBalance::INCOME_CREDIT_NOTE) {
                $invoiceAmount = -$item->total_amount;
                $receivedAmount = -$item->total_paid_amount;
                $pendingAmount = -($item->total_amount - $item->total_paid_amount);
            }

            $dataItem[] = [
                'id' => $item->id,
                'ledger_id' => $item->financialYearOpeningBalance->ledger_id,
                'party_name' => $item->financialYearOpeningBalance->ledger->name,
                'group_name' => $item->financialYearOpeningBalance->ledger->group->name,
                'mobile_number' => ! empty($item->financialYearOpeningBalance->ledger->model->phone_1) ? (! empty($item->financialYearOpeningBalance->ledger->model->phone_2) ?
                    ($item->financialYearOpeningBalance->ledger->model->phone_1.' / '.$item->financialYearOpeningBalance->ledger->model->phone_2) : $item->financialYearOpeningBalance->ledger->model->phone_1) : (! empty($item->financialYearOpeningBalance->ledger->model->phone_2) ? $item->financialYearOpeningBalance->ledger->model->phone_2 : ''),
                'transaction_type' => BillWiseOpeningBalance::TRANSACTION_TYPE[$item->transaction_type],
                'invoice_number' => $item->voucher_number,
                'invoice_date' => Carbon::parse($item->voucher_date)->format('d-m-Y'),
                'narration' => null,
                'invoice_amount' => $invoiceAmount,
                'received_amount' => $receivedAmount,
                'pending_amount' => $pendingAmount,
                'credit_period' => 0,
                'due_date' => Carbon::parse($item->due_date)->format('d-m-Y'),
                'overdue_days' => $overdueDays,
                'rate_of_interest' => $rateOfInterest,
                'interest_of_amount' => $interestAmt,
                'customer_id' => $item->financialYearOpeningBalance->ledger_id ?? null,
                'phone' => empty($item->financialYearOpeningBalance->ledger->model->phone_1) ? (! empty($item->financialYearOpeningBalance->ledger->model->phone_2) ?
                    $item->financialYearOpeningBalance->ledger->model->phone_2 : '') : $item->financialYearOpeningBalance->ledger->model->phone_1,
            ];
        }

        /** @var ReceiptTransaction $item */
        foreach ($data['receiptTransactions'] as $item) {
            $dataItem[] = [
                'id' => $item->id,
                'ledger_id' => $item->ledger_id,
                'party_name' => $item->ledgers->name,
                'group_name' => $item->ledgers->group->name,
                'mobile_number' => ! empty($item->ledgers->model->phone_1) ? (! empty($item->ledgers->model->phone_2) ?
                    ($item->ledgers->model->phone_1.' / '.$item->ledgers->model->phone_2) : $item->ledgers->model->phone_1) : (! empty($item->ledgers->model->phone_2) ? $item->ledgers->model->phone_2 : ''),
                'transaction_type' => BillWiseOpeningBalance::TRANSACTION_TYPE[BillWiseOpeningBalance::RECEIPT],
                'invoice_number' => $item->receipt_number,
                'invoice_date' => Carbon::parse($item->date)->format('d-m-Y'),
                'narration' => $item->narration ?? null,
                'invoice_amount' => 0,
                'received_amount' => $item->total_received_amount ?? 0,
                'pending_amount' => -($item->total_received_amount ?? 0),
                'credit_period' => 0,
                'due_date' => null,
                'overdue_days' => 0,
                'rate_of_interest' => null,
                'interest_of_amount' => null,
                'customer_id' => $item->ledger_id ?? null,
                'phone' => empty($item->ledgers->model->phone_1) ? (! empty($item->ledgers->model->phone_2) ?
                    $item->ledgers->model->phone_2 : '') : $item->ledgers->model->phone_1,
            ];
        }

        if ($input['report_type'] == 1) {
            $dataItem = $this->prepareReceivableSummaryReportData($dataItem);
        }

        if ($input['report_type'] == 3) {
            $dataItem = collect($dataItem)->groupBy('ledger_id')->toArray();
        }

        return $dataItem;
    }

    public function prepareReceivableSummaryReportData($dataItem): array
    {
        $data = [];
        foreach ($dataItem as $item) {
            if ($item['transaction_type'] == SaleTransaction::SALE_TRANSACTION || $item['transaction_type'] == SaleTransaction::INCOME_DEBIT_NOTE_TRANSACTION || $item['transaction_type'] == BillWiseOpeningBalance::TRANSACTION_TYPE[BillWiseOpeningBalance::RECEIPT]) {
                if (array_key_exists($item['ledger_id'], $data)) {
                    $data[$item['ledger_id']]['invoice_amount'] += $item['invoice_amount'] ?? 0;
                    $data[$item['ledger_id']]['received_amount'] += $item['received_amount'] ?? 0;
                    $data[$item['ledger_id']]['pending_amount'] += $item['pending_amount'] ?? 0;
                } else {
                    $data[$item['ledger_id']]['group_name'] = $item['group_name'];
                    $data[$item['ledger_id']]['party_name'] = $item['party_name'];
                    $data[$item['ledger_id']]['mobile_number'] = $item['mobile_number'];
                    $data[$item['ledger_id']]['ledger_id'] = $item['ledger_id'];
                    $data[$item['ledger_id']]['invoice_amount'] = $item['invoice_amount'] ?? 0;
                    $data[$item['ledger_id']]['received_amount'] = $item['received_amount'] ?? 0;
                    $data[$item['ledger_id']]['pending_amount'] = $item['pending_amount'] ?? 0;
                }
            }

            if ($item['transaction_type'] == SaleTransaction::SALE_RETURN_TRANSACTION || $item['transaction_type'] == SaleTransaction::INCOME_CREDIT_NOTE_TRANSACTION) {
                if (array_key_exists($item['ledger_id'], $data)) {
                    $data[$item['ledger_id']]['invoice_amount'] -= $item['invoice_amount'] ?? 0;
                    $data[$item['ledger_id']]['received_amount'] -= $item['received_amount'] ?? 0;
                    $data[$item['ledger_id']]['pending_amount'] -= $item['pending_amount'] ?? 0;
                } else {
                    $data[$item['ledger_id']]['group_name'] = $item['group_name'];
                    $data[$item['ledger_id']]['party_name'] = $item['party_name'];
                    $data[$item['ledger_id']]['mobile_number'] = $item['mobile_number'];
                    $data[$item['ledger_id']]['ledger_id'] = $item['ledger_id'];
                    $data[$item['ledger_id']]['invoice_amount'] = -abs($item['invoice_amount']) ?? 0;
                    $data[$item['ledger_id']]['received_amount'] = -abs($item['received_amount']) ?? 0;
                    $data[$item['ledger_id']]['pending_amount'] = -abs($item['pending_amount']) ?? 0;
                }
            }
        }

        return array_values($data);
    }

    public function preparePayableReportData($input): array
    {
        $data = [];

        $data['purchaseTransactions'] = PurchaseTransaction::with([
            'supplier.model', 'purchaseTransactionItems', 'saleTransaction',
            'purchaseTransactionLedger', 'purchaseReturn', 'debitNote', 'paymentTransactionItem', 'journalTransactionItem', 'supplier.group',
        ])
            ->whereBetween('voucher_date', [$input['start_date'], $input['end_date']])
            ->where(function (Builder $q) use ($input) {
                if (isset($input['party_name']) && ! empty($input['party_name'])) {
                    $q->whereIn('supplier_ledger_id', $input['party_name']);
                }
                if (isset($input['group_name']) && ! empty($input['group_name'])) {
                    $q->whereHas('supplier', function ($query) use ($input) {
                        $query->whereIn('group_id', $input['group_name']);
                    });
                }
            })->get();

        $data['purchaseReturnTransactions'] = PurchaseReturnTransaction::with([
            'supplier.model', 'purchaseReturnItems', 'purchaseReturnLedgers',
            'purchase', 'receiptTransactionItem', 'journalTransactionItem', 'supplier.group',
        ])
            ->whereNull('original_inv_no')
            ->whereBetween('voucher_date', [$input['start_date'], $input['end_date']])
            ->where(function (Builder $q) use ($input) {
                if (isset($input['party_name']) && ! empty($input['party_name'])) {
                    $q->whereIn('supplier_id', $input['party_name']);
                }
                if (isset($input['group_name']) && ! empty($input['group_name'])) {
                    $q->whereHas('supplier', function ($query) use ($input) {
                        $query->whereIn('group_id', $input['group_name']);
                    });
                }
            })->get();

        $data['expenseDebitNoteTransactions'] = ExpenseDebitNoteTransaction::with([
            'supplier.model', 'debitNoteItems', 'debitNoteLedgers', 'purchase',
            'receiptTransactionItem', 'journalTransactionItem', 'supplier.group',
        ])
            ->whereBetween('voucher_date', [$input['start_date'], $input['end_date']])
            ->where(function (Builder $q) use ($input) {
                if (isset($input['party_name']) && ! empty($input['party_name'])) {
                    $q->whereIn('supplier_id', $input['party_name']);
                }
                if (isset($input['group_name']) && ! empty($input['group_name'])) {
                    $q->whereHas('supplier', function ($query) use ($input) {
                        $query->whereIn('group_id', $input['group_name']);
                    });
                }
            })->get();

        $data['expenseCreditNoteTransactions'] = ExpenseCreditNoteTransaction::with([
            'supplier.model', 'expenseCreditNoteItems', 'expenseCreditNoteLedgers',
            'purchase', 'receiptTransactionItem', 'paymentTransactionItem', 'journalTransactionItem', 'supplier.group',
        ])
            ->whereNull('original_inv_no')
            ->whereBetween('voucher_date', [$input['start_date'], $input['end_date']])
            ->where(function (Builder $q) use ($input) {
                if (isset($input['party_name']) && ! empty($input['party_name'])) {
                    $q->whereIn('supplier_id', $input['party_name']);
                }
                if (isset($input['group_name']) && ! empty($input['group_name'])) {
                    $q->whereHas('supplier', function ($query) use ($input) {
                        $query->whereIn('group_id', $input['group_name']);
                    });
                }
            })->get();

        $data['billWiseOpeningBalanceTransactions'] = BillWiseOpeningBalance::with('financialYearOpeningBalance.ledger.model')
            ->whereHas('financialYearOpeningBalance', function ($query) {
                $query->where('company_id', getCurrentCompany()->id);
            })
            ->whereIn('transaction_type', [BillWiseOpeningBalance::PURCHASE, BillWiseOpeningBalance::PURCHASE_RETURN, BillWiseOpeningBalance::EXPENSE_DEBIT_NOTE, BillWiseOpeningBalance::EXPENSE_CREDIT_NOTE])
            ->whereDate('voucher_date', '<=', $input['end_date'])
            ->when(isset($input['party_name']) && ! empty($input['party_name']), function ($q) use ($input) {
                $q->whereHas('financialYearOpeningBalance', function ($query) use ($input) {
                    $query->whereIn('ledger_id', $input['party_name']);
                });
            })
            ->when(isset($input['group_name']) && ! empty($input['group_name']), function ($q) use ($input) {
                $q->whereHas('financialYearOpeningBalance', function ($query) use ($input) {
                    $query->whereHas('ledger', function ($query) use ($input) {
                        $query->whereIn('group_id', $input['group_name']);
                    });
                });
            })
            ->get();

        $data['paymentTransactions'] = PaymentTransaction::with(['paymentTransactionItem', 'ledgers', 'ledgers.model'])->whereDoesntHave('paymentTransactionItem')
            ->whereBetween('date', [$input['start_date'], $input['end_date']])
            ->where(function (Builder $q) use ($input) {
                if (isset($input['party_name']) && ! empty($input['party_name'])) {
                    $q->whereIn('ledger_id', $input['party_name']);
                }
            })
            ->when(isset($input['group_name']) && ! empty($input['group_name']), function ($q) use ($input) {
                $q->whereHas('ledgers', function ($query) use ($input) {
                    $query->whereIn('group_id', $input['group_name']);
                });
            })
            ->get();

        $dataItem = [];

        /** @var PurchaseTransaction $item */
        foreach ($data['purchaseTransactions'] as $item) {
            $value = getOutstandingPayableCreditPeriodData($item, true); // outstanding  payable payment due date not show properly
            if ($item->due_amount != 0) {
                $dataItem[] = [
                    'id' => $item->id,
                    'ledger_id' => $item->supplier_ledger_id,
                    'party_name' => $item->supplier->name,
                    'group_name' => $item->supplier->group->name,
                    'mobile_number' => ! empty($item->supplier->model->phone_1) ? (! empty($item->supplier->model->phone_2) ?
                        ($item->supplier->model->phone_1.' / '.$item->supplier->model->phone_2) : $item->supplier->model->phone_1) : (! empty($item->supplier->model->phone_2) ? $item->supplier->model->phone_2 : ''),
                    'transaction_type' => PurchaseTransaction::PURCHASE_TRANSACTION,
                    'voucher_number' => $item->voucher_number,
                    'invoice_number' => $item->sale_number ?? '',
                    'invoice_date' => Carbon::parse($item->date_of_invoice)->format('d-m-Y') ?? '',
                    'narration' => $item->narration ?? null,
                    'invoice_amount' => $item->grand_total ?? 0,
                    'received_amount' => $item->grand_total - $item->due_amount ?? 0,
                    'pending_amount' => $item->due_amount,
                    'credit_period' => $value['creditPeriodTime'],
                    'due_date' => Carbon::parse($value['dueDate'])->format('d-m-Y') ?? '',
                    'overdue_days' => $value['overdueDays'],
                    'phone' => empty($item->supplier->model->phone_1) ? (! empty($item->supplier->model->phone_2) ?
                        $item->supplier->model->phone_2 : '') : $item->supplier->model->phone_1,
                ];
            }
        }

        /** @var PurchaseReturnTransaction $item */
        foreach ($data['purchaseReturnTransactions'] as $item) {
            if ($item->due_amount != 0) {
                $value = getOutstandingPayableCreditPeriodData($item);
                $dataItem[] = [
                    'id' => $item->id,
                    'ledger_id' => $item->supplier_id,
                    'party_name' => $item->supplier->name,
                    'group_name' => $item->supplier->group->name,
                    'mobile_number' => ! empty($item->supplier->model->phone_1) ? (! empty($item->supplier->model->phone_2) ?
                        ($item->supplier->model->phone_1.' / '.$item->supplier->model->phone_2) : $item->supplier->model->phone_1) : (! empty($item->supplier->model->phone_2) ? $item->supplier->model->phone_2 : ''),
                    'transaction_type' => PurchaseTransaction::PURCHASE_RETURN_TRANSACTION,
                    'voucher_number' => $item->voucher_number,
                    'invoice_number' => $item->supplier_purchase_return_number ?? '',
                    'invoice_date' => Carbon::parse($item->original_inv_date)->format('d-m-Y') ?? '',
                    'narration' => $item->narration ?? null,
                    'invoice_amount' => $input['report_type'] == 1 ? ($item->grand_total ?? 0) : (-$item->grand_total ?? 0),
                    'received_amount' => $input['report_type'] == 1 ? ($item->grand_total - $item->due_amount ?? 0) : -($item->grand_total - $item->due_amount ?? 0),
                    'pending_amount' => $input['report_type'] == 1 ? $item->due_amount : -$item->due_amount,
                    'credit_period' => $value['creditPeriodTime'],
                    'due_date' => Carbon::parse($value['dueDate'])->format('d-m-Y') ?? '',
                    'overdue_days' => $value['overdueDays'],
                    'phone' => empty($item->supplier->model->phone_1) ? (! empty($item->supplier->model->phone_2) ?
                        $item->supplier->model->phone_2 : '') : $item->supplier->model->phone_1,
                ];
            }
        }

        /** @var ExpenseDebitNoteTransaction $item */
        foreach ($data['expenseDebitNoteTransactions'] as $item) {
            if ($item->due_amount != 0) {
                $value = getOutstandingPayableCreditPeriodData($item);
                $dataItem[] = [
                    'id' => $item->id,
                    'ledger_id' => $item->supplier_id,
                    'party_name' => $item->supplier->name,
                    'group_name' => $item->supplier->group->name,
                    'mobile_number' => ! empty($item->supplier->model->phone_1) ? (! empty($item->supplier->model->phone_2) ?
                        ($item->supplier->model->phone_1.' / '.$item->supplier->model->phone_2) : $item->supplier->model->phone_1) : (! empty($item->supplier->model->phone_2) ? $item->supplier->model->phone_2 : ''),
                    'transaction_type' => PurchaseTransaction::EXPENSE_DEBIT_NOTE_TRANSACTION,
                    'voucher_number' => $item->voucher_number,
                    'invoice_number' => $item->supplier_purchase_return_number ?? '',
                    'invoice_date' => Carbon::parse($item->original_inv_date)->format('d-m-Y'),
                    'narration' => $item->narration ?? null,
                    'invoice_amount' => $input['report_type'] == 1 ? ($item->grand_total ?? 0) : (-$item->grand_total ?? 0),
                    'received_amount' => $input['report_type'] == 1 ? ($item->grand_total - $item->due_amount ?? 0) : -($item->grand_total - $item->due_amount ?? 0),
                    'pending_amount' => $input['report_type'] == 1 ? $item->due_amount : -$item->due_amount,
                    'credit_period' => $value['creditPeriodTime'],
                    'due_date' => Carbon::parse($value['dueDate'])->format('d-m-Y'),
                    'overdue_days' => $value['overdueDays'],
                    'phone' => empty($item->supplier->model->phone_1) ? (! empty($item->supplier->model->phone_2) ?
                        $item->supplier->model->phone_2 : '') : $item->supplier->model->phone_1,
                ];
            }
        }

        /** @var ExpenseCreditNoteTransaction $item */
        foreach ($data['expenseCreditNoteTransactions'] as $item) {
            if ($item->due_amount != 0) {
                $value = getOutstandingPayableCreditPeriodData($item, true);
                $dataItem[] = [
                    'id' => $item->id,
                    'ledger_id' => $item->supplier_id,
                    'party_name' => $item->supplier->name,
                    'group_name' => $item->supplier->group->name,
                    'mobile_number' => ! empty($item->supplier->model->phone_1) ? (! empty($item->supplier->model->phone_2) ?
                        ($item->supplier->model->phone_1.' / '.$item->supplier->model->phone_2) : $item->supplier->model->phone_1) : (! empty($item->supplier->model->phone_2) ? $item->supplier->model->phone_2 : ''),
                    'transaction_type' => PurchaseTransaction::EXPENSE_CREDIT_NOTE_TRANSACTION,
                    'voucher_number' => $item->voucher_number,
                    'invoice_number' => $item->supplier_purchase_return_number ?? '',
                    'invoice_date' => Carbon::parse($item->original_inv_date)->format('d-m-Y'),
                    'narration' => $item->narration ?? null,
                    'invoice_amount' => $item->grand_total ?? 0,
                    'received_amount' => $item->grand_total - $item->due_amount ?? 0,
                    'pending_amount' => $item->due_amount,
                    'credit_period' => $value['creditPeriodTime'],
                    'due_date' => Carbon::parse($value['dueDate'])->format('d-m-Y'),
                    'overdue_days' => $value['overdueDays'],
                    'phone' => empty($item->supplier->model->phone_1) ? (! empty($item->supplier->model->phone_2) ?
                        $item->supplier->model->phone_2 : '') : $item->supplier->model->phone_1,
                ];
            }
        }

        /** @var BillWiseOpeningBalance $item */
        foreach ($data['billWiseOpeningBalanceTransactions'] as $item) {
            if ($item->total_paid_amount == $item->total_amount) {
                continue;
            }
            $overdueDays = 0;
            if (Carbon::now() > Carbon::parse($item->due_date)) {
                $overdueDays = Carbon::parse($item->due_date)->diffInDays(Carbon::now());
            }

            $invoiceAmount = 0;
            $receivedAmount = 0;
            $pendingAmount = 0;

            if ($item->transaction_type == BillWiseOpeningBalance::PURCHASE || $item->transaction_type == BillWiseOpeningBalance::EXPENSE_CREDIT_NOTE || $input['report_type'] == 1) {
                $invoiceAmount = $item->total_amount;
                $receivedAmount = $item->total_paid_amount;
                $pendingAmount = $item->total_amount - $item->total_paid_amount;
            } elseif ($item->transaction_type == BillWiseOpeningBalance::PURCHASE_RETURN || $item->transaction_type == BillWiseOpeningBalance::EXPENSE_DEBIT_NOTE) {
                $invoiceAmount = -$item->total_amount;
                $receivedAmount = -$item->total_paid_amount;
                $pendingAmount = -($item->total_amount - $item->total_paid_amount);
            }

            $dataItem[] = [
                'id' => $item->id,
                'ledger_id' => $item->financialYearOpeningBalance->ledger_id,
                'party_name' => $item->financialYearOpeningBalance->ledger->name,
                'group_name' => $item->financialYearOpeningBalance->ledger->group->name,
                'mobile_number' => ! empty($item->financialYearOpeningBalance->ledger->model->phone_1) ? (! empty($item->financialYearOpeningBalance->ledger->model->phone_2) ?
                    ($item->financialYearOpeningBalance->ledger->model->phone_1.' / '.$item->financialYearOpeningBalance->ledger->model->phone_2) : $item->financialYearOpeningBalance->ledger->model->phone_1) : (! empty($item->financialYearOpeningBalance->ledger->model->phone_2) ? $item->financialYearOpeningBalance->ledger->model->phone_2 : ''),
                'transaction_type' => BillWiseOpeningBalance::TRANSACTION_TYPE[$item->transaction_type],
                'voucher_number' => $item->voucher_number,
                'invoice_number' => '',
                'invoice_date' => Carbon::parse($item->voucher_date)->format('d-m-Y'),
                'narration' => null,
                'invoice_amount' => $invoiceAmount,
                'received_amount' => $receivedAmount,
                'pending_amount' => $pendingAmount,
                'credit_period' => 0,
                'due_date' => Carbon::parse($item->due_date)->format('d-m-Y'),
                'overdue_days' => $overdueDays,
                'phone' => empty($item->financialYearOpeningBalance->ledger->model->phone_1) ? (! empty($item->financialYearOpeningBalance->ledger->model->phone_2) ?
                    $item->financialYearOpeningBalance->ledger->model->phone_2 : '') : $item->financialYearOpeningBalance->ledger->model->phone_1,
            ];
        }

        /** @var PaymentTransaction $item */
        foreach ($data['paymentTransactions'] as $item) {
            $dataItem[] = [
                'id' => $item->id,
                'ledger_id' => $item->ledger_id,
                'party_name' => $item->ledgers->name,
                'group_name' => $item->ledgers->group->name,
                'mobile_number' => ! empty($item->ledgers->model->phone_1) ? (! empty($item->ledgers->model->phone_2) ?
                    ($item->ledgers->model->phone_1.' / '.$item->ledgers->model->phone_2) : $item->ledgers->model->phone_1) : (! empty($item->ledgers->model->phone_2) ? $item->ledgers->model->phone_2 : ''),
                'transaction_type' => BillWiseOpeningBalance::TRANSACTION_TYPE[BillWiseOpeningBalance::PAYMENT],
                'voucher_number' => $item->payment_voucher_number,
                'invoice_number' => null,
                'invoice_date' => Carbon::parse($item->date)->format('d-m-Y'),
                'narration' => $item->narration ?? null,
                'invoice_amount' => 0,
                'received_amount' => $item->total_paid_amount ?? 0,
                'pending_amount' => -($item->total_paid_amount ?? 0),
                'credit_period' => 0,
                'due_date' => null,
                'overdue_days' => 0,
                'phone' => empty($item->ledgers->model->phone_1) ? (! empty($item->ledgers->model->phone_2) ?
                    $item->ledgers->model->phone_2 : '') : $item->ledgers->model->phone_1,
            ];
        }

        if ($input['report_type'] == 1) {
            $dataItem = $this->preparePayableSummaryReportData($dataItem);
        }

        if ($input['report_type'] == 3) {
            $dataItem = collect($dataItem)->groupBy('ledger_id')->toArray();
        }

        return $dataItem;
    }

    public function preparePayableSummaryReportData($dataItem): array
    {
        $data = [];

        foreach ($dataItem as $item) {
            if ($item['transaction_type'] == PurchaseTransaction::PURCHASE_TRANSACTION || $item['transaction_type'] == PurchaseTransaction::EXPENSE_CREDIT_NOTE_TRANSACTION || $item['transaction_type'] == BillWiseOpeningBalance::TRANSACTION_TYPE[BillWiseOpeningBalance::PAYMENT]) {
                if (array_key_exists($item['ledger_id'], $data)) {
                    $data[$item['ledger_id']]['invoice_amount'] += $item['invoice_amount'] ?? 0;
                    $data[$item['ledger_id']]['received_amount'] += $item['received_amount'] ?? 0;
                    $data[$item['ledger_id']]['pending_amount'] += $item['pending_amount'] ?? 0;
                } else {
                    $data[$item['ledger_id']]['group_name'] = $item['group_name'];
                    $data[$item['ledger_id']]['party_name'] = $item['party_name'];
                    $data[$item['ledger_id']]['mobile_number'] = $item['mobile_number'];
                    $data[$item['ledger_id']]['ledger_id'] = $item['ledger_id'];
                    $data[$item['ledger_id']]['invoice_amount'] = $item['invoice_amount'] ?? 0;
                    $data[$item['ledger_id']]['received_amount'] = $item['received_amount'] ?? 0;
                    $data[$item['ledger_id']]['pending_amount'] = $item['pending_amount'] ?? 0;
                }
            }

            if ($item['transaction_type'] == PurchaseTransaction::PURCHASE_RETURN_TRANSACTION || $item['transaction_type'] == PurchaseTransaction::EXPENSE_DEBIT_NOTE_TRANSACTION) {
                if (array_key_exists($item['ledger_id'], $data)) {
                    $data[$item['ledger_id']]['invoice_amount'] -= $item['invoice_amount'] ?? 0;
                    $data[$item['ledger_id']]['received_amount'] -= $item['received_amount'] ?? 0;
                    $data[$item['ledger_id']]['pending_amount'] -= $item['pending_amount'] ?? 0;
                } else {
                    $data[$item['ledger_id']]['group_name'] = $item['group_name'];
                    $data[$item['ledger_id']]['party_name'] = $item['party_name'];
                    $data[$item['ledger_id']]['mobile_number'] = $item['mobile_number'];
                    $data[$item['ledger_id']]['ledger_id'] = $item['ledger_id'];
                    $data[$item['ledger_id']]['invoice_amount'] = -abs($item['invoice_amount']) ?? 0;
                    $data[$item['ledger_id']]['received_amount'] = -abs($item['received_amount']) ?? 0;
                    $data[$item['ledger_id']]['pending_amount'] = -abs($item['pending_amount']) ?? 0;
                }
            }
        }

        return array_values($data);
    }

    public function getTransactionType($input): array
    {
        if ($input['config_type'] == 1) {
            return [
                '0' => 'Type',
                '1' => 'Sale',
                '2' => 'Sale Return',
                '3' => 'Income Dr. Note',
                '4' => 'Income Cr. Note',
            ];
        }

        return [
            '0' => 'Type',
            '1' => 'Purchase',
            '2' => 'Purchase Return',
            '3' => 'Expense Dr. Note',
            '4' => 'Expense Cr. Note',
        ];
    }

    public function getOutstandingOption(): array
    {
        return [
            '1' => 'Receivable',
            '2' => 'Payable',
        ];
    }

    public function rateOfInterestUpdate(Request $request): JsonResponse
    {
        $data = Customer::where('id', $request->customer_id)->first();
        $data->rate_of_interest = $request->rate_of_interest;
        $data->update();

        return $this->sendResponse($data->rate_of_interest, 'Rate Of Interest Updated Successfully');
    }

    /**
     * @return Application|Factory|View
     */
    public function sendSaleReminderEmailForm(SaleTransaction $sale)
    {
        $fromName = isset(getCompanySettings()['from_name']) ? getCompanySettings()['from_name'] : '';
        $replyToEmail = isset(getCompanySettings()['replay_to_email']) ? getCompanySettings()['replay_to_email'] : '';

        if (empty($fromName) || empty($replyToEmail)) {
            Flash::error('Please Enter From Name And Reply To Email First.');

            return redirect()->route('company.email-and-whatsapp-configuration-setting.index', ['section' => 'emailConfiguration']);
        }

        $data['sale'] = $sale->load('saleItems', 'saleLedgers', 'addresses', 'customer');

        return view('company.sale.send-sale-reminder-mail')->with($data);
    }

    /**
     * @throws FileDoesNotExist
     * @throws FileIsTooBig
     */
    public function sendSaleTransactionEmail(Request $request, SaleTransaction $sale): RedirectResponse
    {
        $data = GetInvoicePDFDataForSale::run($sale->id);
        $data = GstCalculationForPdf::run($data, SaleTransaction::class);
        $input = $request->all();

        if (isset($input['removed_attachment']) && ! empty($input['removed_attachment'][0])) {
            $cleanedFileArray = explode(',', $input['removed_attachment'][0]);
            $input['attachments'] = array_filter($input['attachments'], function ($file) use ($cleanedFileArray) {
                return ! in_array($file->getClientOriginalName(), $cleanedFileArray);
            });
        }

        if (isset($input['attachments']) && ! empty($input['attachments'])) {
            foreach ($input['attachments'] as $file) {
                $sale->addMedia($file)->toMediaCollection(
                    SaleTransaction::INVOICE_ATTACHMENT,
                    config('app.media_disc')
                );
            }
        }

        // $data['mailSetting'] = $data['currentCompany']->mailConfiguration;
        // if (empty($data['mailSetting'])) {

        //     Flash::error('Please enter mail credentials first.');

        //     return redirect()->route('company.outstanding-report', ['outstandingValue' => 1]);
        // }

        $data['to'] = $input['to'] ?? null;
        $data['cc'] = $input['cc'] ?? null;
        $data['body'] = $input['body'];
        $data['regards'] = $input['regards'];
        $data['customPaperSize'] = [0, 0, 700, 900];
        $data['fromName'] = getCompanySettings()['from_name'];
        $data['replayToEmail'] = getCompanySettings()['replay_to_email'];

        InvoiceMailJob::dispatch($data, SendSaleTransactionReminderMail::class);

        Flash::success('Send Reminder Mail successfully.');

        return redirect()->route('company.outstanding-report', ['outstandingValue' => 1]);
    }

    /**
     * @return Application|Factory|View
     */
    public function incomeDebitNoteReturnEmail(IncomeDebitNoteTransaction $incomeDebitNote)
    {
        $fromName = isset(getCompanySettings()['from_name']) ? getCompanySettings()['from_name'] : '';
        $replyToEmail = isset(getCompanySettings()['replay_to_email']) ? getCompanySettings()['replay_to_email'] : '';

        if (empty($fromName) || empty($replyToEmail)) {
            Flash::error('Please Enter From Name And Reply To Email First.');

            return redirect()->route('company.email-and-whatsapp-configuration-setting.index', ['section' => 'emailConfiguration']);
        }

        $data['incomeDebitNote'] = $incomeDebitNote->load(
            'incomeDebitNoteItems',
            'incomeDebitNoteLedgers',
            'addresses',
            'customer'
        );

        return view('company.income-debit-note.send-debit-note-remaining-email')->with($data);
    }

    /**
     * @throws FileDoesNotExist
     * @throws FileIsTooBig
     */
    public function sendEmail(Request $request, IncomeDebitNoteTransaction $incomeDebitNote): RedirectResponse
    {
        $data = GetInvoicePDFDataFoeIncomeDebitNote::run($incomeDebitNote->id);
        $data = GstCalculationForPdf::run($data, IncomeDebitNoteTransaction::class);
        $input = $request->all();

        if (isset($input['removed_attachment']) && ! empty($input['removed_attachment'][0])) {
            $cleanedFileArray = explode(',', $input['removed_attachment'][0]);
            $input['attachments'] = array_filter($input['attachments'], function ($file) use ($cleanedFileArray) {
                return ! in_array($file->getClientOriginalName(), $cleanedFileArray);
            });
        }

        if (isset($input['attachments']) && ! empty($input['attachments'])) {
            foreach ($input['attachments'] as $file) {
                $incomeDebitNote->addMedia($file)->toMediaCollection(
                    IncomeDebitNoteTransaction::INVOICE_ATTACHMENT,
                    config('app.media_disc')
                );
            }
        }

        // $data['mailSetting'] = $data['currentCompany']->mailConfiguration;
        // if (empty($data['mailSetting'])) {

        //     Flash::error('Please enter mail credentials first.');

        //     return redirect()->route('company.outstanding-report', ['outstandingValue' => 1]);
        // }

        $data['to'] = $input['to'] ?? null;
        $data['cc'] = $input['cc'] ?? null;
        $data['body'] = $input['body'];
        $data['regards'] = $input['regards'];
        $data['customPaperSize'] = [0, 0, 700, 900];
        $data['fromName'] = getCompanySettings()['from_name'];
        $data['replayToEmail'] = getCompanySettings()['replay_to_email'];

        InvoiceMailJob::dispatch($data, SendIncomeDebitNoteReminderMail::class);

        Flash::success('Send Reminder Mail successfully.');

        return redirect()->route('company.outstanding-report', ['outstandingValue' => 1]);
    }

    /**
     * @return Response|BinaryFileResponse
     */
    public function export(Request $request)
    {
        $input = $request->all();
        $outstandingReportCacheKey = generateCacheKey('outstanding_report');
        $company = getCurrentCompany();
        $data = [];
        $data['data'] = Cache::get($outstandingReportCacheKey);
        $data['company'] = $company;
        $data['companyAddress'] = $company?->addresses->where('address_type', Company::BILLING_ADDRESS)->first();
        $data['report_type'] = $input['report_type'];
        $customPaperSize = [0, 0, 700, 900];

        $user = getLoginUser();
        $pdfOrExcel = $input['type'] == 'pdf' ? 'PDF' : 'Excel';
        $reportType = $input['config_type'] == 1 ? 'Receivable' : 'Payable';
        CreateAuditTrailEvent::run($user, 'Export', '<b>'.$reportType.' Outstanding Report</b> '.$pdfOrExcel.' was exported.');

        /* Receivable Outstanding Report */
        if ($input['config_type'] == 1) {

            if ($input['type'] == 'excel') {
                $response = Excel::download(new OutstandingReceivableReportExport($data), 'outstanding-receivable-report.xlsx');
                ob_end_clean();

                return $response;

                // return (new OutstandingReceivableReportExport($data))->download('outstanding-receivable-report.xlsx');
            }

            if ($input['report_type'] == 1) {
                $pdf = PDF::loadView('pdf.outstanding-report-receivable-summary', $data)->setPaper($customPaperSize);
            } elseif ($input['report_type'] == 3) {
                $pdf = PDF::loadView('pdf.outstanding-report-receivable-party-wise-invoice', $data)->setPaper($customPaperSize);
            } else {
                $pdf = PDF::loadView('pdf.outstanding-report-receivable', $data)->setPaper($customPaperSize);
            }

            if (isset($input['is_view'])) {
                return $pdf->stream('outstanding-receivable-report.pdf');
            }

            return $pdf->download('outstanding-receivable-report.pdf');
        }

        /* Payable Outstanding Report */
        if ($input['type'] == 'excel') {
            $response = Excel::download(new OutstandingPayableReportExport($data), 'outstanding-payable-report.xlsx');
            ob_end_clean();

            return $response;

            // return (new OutstandingPayableReportExport($data))->download('outstanding-payable-report.xlsx');
        }

        if ($input['report_type'] == 1) {
            $pdf = PDF::loadView('pdf.outstanding-report-payable-summary', $data)->setPaper($customPaperSize);
        } elseif ($input['report_type'] == 3) {
            $pdf = PDF::loadView('pdf.outstanding-report-payable-party-wise-invoice', $data)->setPaper($customPaperSize);
        } else {
            $pdf = PDF::loadView('pdf.outstanding-report-payable', $data)->setPaper($customPaperSize);
        }

        if (isset($input['is_view'])) {
            return $pdf->stream('outstanding-receivable-report.pdf');
        }

        return $pdf->download('outstanding-payable-report.pdf');
    }
}
