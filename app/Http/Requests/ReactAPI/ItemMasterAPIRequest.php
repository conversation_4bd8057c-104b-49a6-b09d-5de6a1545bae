<?php

namespace App\Http\Requests\ReactAPI;

use App\Models\Master\ItemMaster;
use App\Rules\CheckExpenseLedgerRule;
use App\Rules\CheckIncomeLedgerRule;
use App\Rules\CheckItemGroupRule;
use App\Rules\CheckItemTransactionRule;
use App\Rules\DifferentUOM;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\RequiredIf;

class ItemMasterAPIRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $itemId = $this->route('id');

        // Atleast item type is required
        if (! $this->has('item_type')) {
            return [
                'item_type' => 'required|in:'.ItemMaster::ITEM_MASTER_GOODS.','.ItemMaster::ITEM_MASTER_SERVICE,
            ];
        }

        if (isCompanyGstApplicable()) {
            if ($this->get('item_type') == ItemMaster::ITEM_MASTER_GOODS) {
                return $this->rulesIsCompanyGstApplicableForGoods($itemId);
            }
            if ($this->get('item_type') == ItemMaster::ITEM_MASTER_SERVICE) {
                return $this->rulesIsCompanyGstApplicableForService($itemId);
            }
        } else {
            if ($this->get('item_type') == ItemMaster::ITEM_MASTER_GOODS) {
                return $this->rulesForGoods($itemId);
            }
            if ($this->get('item_type') == ItemMaster::ITEM_MASTER_SERVICE) {
                return $this->rulesForService($itemId);
            }
        }

        return [];
    }

    private function rulesIsCompanyGstApplicableForGoods($itemId)
    {
        return array_merge([
            // Basic Details
            'item_name' => 'required',
            'group_id' => ['required', new CheckItemGroupRule()],
            'item_type' => 'required',
            'primary_unit_of_measurement' => ['required', new CheckItemTransactionRule($itemId, request()->get('secondary_unit_of_measurement'))],
            'secondary_unit_of_measurement' => ['nullable', new CheckItemTransactionRule($itemId), new DifferentUOM],
            'conversion_rate' => ['nullable', 'numeric', 'decimal:0,2', 'min:0.01', new RequiredIf($this->get('secondary_unit_of_measurement') != null)],
            'decimal_places' => 'nullable|max:4|integer',
            'sku' => 'nullable|max:30',
            'description' => 'nullable|max:5000',
            'is_description_same_as_item_name' => 'required',
            'avatar' => 'nullable|mimes:jpg,jpeg,png|max:2048',

            // Re-Order Details
            'is_re_order' => 'nullable|in:0,1',
            're_order_level' => 'nullable|numeric',
            're_order_uom' => 'nullable|integer',

            // GST Details
            'is_gst_applicable' => 'required',
            'gst_tax_id' => 'nullable|required_if:is_gst_applicable,1',
            'hsn_sac_code' => 'nullable|max:8',
            'gst_cess_rate' => 'nullable',
            'is_rcm_applicable' => 'required|in:0,1',

            // Pricing Details for Sale Information
            'mrp' => 'nullable|max:17',
            'sale_price' => 'nullable|max:17',
            'sale_price_type' => 'required|in:1,2', // 2 = without gst , 1 = with gst
            'discount_value' => 'nullable|max:17',
            'discount_type' => 'required',
            'income_ledger_id' => ['nullable', new CheckIncomeLedgerRule()],

            // Pricing Details for Purchase Information
            'purchase_price' => 'nullable|max:17',
            'purchase_price_type' => 'required|in:1,2', // 2 = without gst , 1 = with gst
            'purchase_discount_value' => 'nullable|max:17',
            'purchase_discount_type' => 'required',
            'expense_ledger_id' => ['nullable', new CheckExpenseLedgerRule()],

            // Other Details
            'decimal_places_for_rate' => 'nullable|integer|min:2|max:5',

            // Opening Stock Details
            'quantity_unit' => 'nullable',
            'quantity' => 'nullable|max:17',
            'rate' => 'nullable|max:17',
            'item_image' => 'nullable|mimes:jpg,jpeg,png,svg,webp',
        ], $this->customFieldsRules());
    }

    private function rulesIsCompanyGstApplicableForService($itemId)
    {
        return array_merge([
            // Basic Details
            'item_name' => 'required',
            'group_id' => ['required', new CheckItemGroupRule()],
            'item_type' => 'required',
            'primary_unit_of_measurement' => ['required', new CheckItemTransactionRule($itemId, request()->get('secondary_unit_of_measurement'))],
            'secondary_unit_of_measurement' => ['nullable', new CheckItemTransactionRule($itemId), new DifferentUOM],
            'conversion_rate' => ['nullable', 'numeric', 'decimal:0,2', 'min:0.01', new RequiredIf($this->get('secondary_unit_of_measurement') != null)],
            'decimal_places' => 'nullable|max:4|integer',
            'sku' => 'nullable|max:30',
            'description' => 'nullable|max:5000',
            'is_description_same_as_item_name' => 'required',

            // GST Details
            'is_gst_applicable' => 'required',
            'gst_tax_id' => 'nullable|required_if:is_gst_applicable,1',
            'hsn_sac_code' => 'nullable|max:8',
            'gst_cess_rate' => 'nullable',
            'is_rcm_applicable' => 'required|in:0,1',

            // Pricing Details for Sale Information
            'sale_price' => 'nullable|max:17',
            'sale_price_type' => 'required|in:1,2', // 2 = without gst , 1 = with gst
            'discount_value' => 'nullable|max:17',
            'discount_type' => 'required',
            'income_ledger_id' => ['nullable', new CheckIncomeLedgerRule()],

            // Pricing Details for Purchase Information
            'purchase_price' => 'nullable|max:17',
            'purchase_price_type' => 'required|in:1,2', // 2 = without gst , 1 = with gst
            'purchase_discount_value' => 'nullable|max:17',
            'purchase_discount_type' => 'required',
            'expense_ledger_id' => ['nullable', new CheckExpenseLedgerRule()],

            // Other Details
            'decimal_places_for_rate' => 'nullable|integer|min:2|max:5',
        ], $this->customFieldsRules());
    }

    private function rulesForGoods($itemId)
    {
        return array_merge([
            // Basic Details
            'item_name' => 'required',
            'group_id' => ['required', new CheckItemGroupRule()],
            'item_type' => 'required',
            'primary_unit_of_measurement' => ['required', new CheckItemTransactionRule($itemId, request()->get('secondary_unit_of_measurement'))],
            'secondary_unit_of_measurement' => ['nullable', new CheckItemTransactionRule($itemId), new DifferentUOM],
            'conversion_rate' => ['nullable', 'numeric', 'decimal:0,2', 'min:0.01', new RequiredIf($this->get('secondary_unit_of_measurement') != null)],
            'decimal_places' => 'nullable|max:4|integer',
            'sku' => 'nullable|max:30',
            'description' => 'nullable|max:5000',
            'is_description_same_as_item_name' => 'required',
            'avatar' => 'nullable|mimes:jpg,jpeg,png|max:2048',

            // Re-Order Details
            'is_re_order' => 'nullable|in:0,1',
            're_order_level' => 'nullable|numeric',
            're_order_uom' => 'nullable|integer',

            // Pricing Details for Sale Information
            'mrp' => 'nullable|max:17',
            'sale_price' => 'nullable|max:17',
            'discount_value' => 'nullable|max:17',
            'discount_type' => 'required',
            'income_ledger_id' => ['nullable', new CheckIncomeLedgerRule()],

            // Pricing Details for Purchase Information
            'purchase_price' => 'nullable|max:17',
            'purchase_discount_value' => 'nullable|max:17',
            'purchase_discount_type' => 'required',
            'expense_ledger_id' => ['nullable', new CheckExpenseLedgerRule()],

            // Other Details
            'decimal_places_for_rate' => 'nullable|integer|min:2|max:5',

            // Opening Stock Details
            'quantity_unit' => 'nullable',
            'quantity' => 'nullable|max:17',
            'rate' => 'nullable|max:17',
            'item_image' => 'nullable|mimes:jpg,jpeg,png,svg,webp',
        ], $this->customFieldsRules());
    }

    private function rulesForService($itemId)
    {
        return array_merge([
            // Basic Details
            'item_name' => 'required',
            'group_id' => ['required', new CheckItemGroupRule()],
            'item_type' => 'required',
            'primary_unit_of_measurement' => ['required', new CheckItemTransactionRule($itemId, request()->get('secondary_unit_of_measurement'))],
            'secondary_unit_of_measurement' => ['nullable', new CheckItemTransactionRule($itemId), new DifferentUOM],
            'conversion_rate' => ['nullable', 'numeric', 'decimal:0,2', 'min:0.01', new RequiredIf($this->get('secondary_unit_of_measurement') != null)],
            'decimal_places' => 'nullable|max:4|integer',
            'sku' => 'nullable|max:30',
            'description' => 'nullable|max:5000',
            'is_description_same_as_item_name' => 'required',

            // Pricing Details for Sale Information
            'sale_price' => 'nullable|max:17',
            'sale_price_type' => 'required',
            'discount_value' => 'nullable|max:17',
            'discount_type' => 'required',
            'income_ledger_id' => ['nullable', new CheckIncomeLedgerRule()],

            // Pricing Details for Purchase Information
            'purchase_price' => 'nullable|max:17',
            'purchase_price_type' => 'required',
            'purchase_discount_value' => 'nullable|max:17',
            'purchase_discount_type' => 'required',
            'expense_ledger_id' => ['nullable', new CheckExpenseLedgerRule()],

            // Other Details
            'decimal_places_for_rate' => 'nullable|integer|min:2|max:5',
        ], $this->customFieldsRules());
    }

    private function customFieldsRules(): array
    {
        return [
            'custom_fields' => 'nullable|array|required_with:custom_fields_values',

            'custom_fields_values' => 'nullable|array',
            'custom_fields_values.*.custom_field_id' => ['required', 'integer', 'exists:item_custom_field,id'],
            'custom_fields_values.*.value' => 'required_with:custom_fields_values.*.custom_field_id',

            'custom_fields_formula' => 'nullable|array',
            'custom_fields_formula.*.custom_field_id' => 'required_if:custom_fields_formula.*.is_system_field,0|integer|exists:item_custom_field,id',
            'custom_fields_formula.*.is_system_field' => 'required|in:0,1',
            'custom_fields_formula.*.system_field_name' => 'string|required_if:custom_fields_formula.*.is_system_field,1|in:Quantity,MRP,Amount',
            'custom_fields_formula.*.formula' => 'required|string',
            'custom_fields_formula.*.used_cf_ids_for_formula' => 'nullable|string',
        ];
    }

    public function messages(): array
    {
        return [
            // Basic Details
            'item_name.required' => 'The item name is required.',
            'group_id.required' => 'The item group is required.',
            'item_type.required' => 'The item type is required.',
            'primary_unit_of_measurement.required' => 'The unit of measurement is required.',
            'secondary_unit_of_measurement.different_uom' => 'The secondary unit of measurement must be different from the primary unit.',
            'conversion_rate.numeric' => 'The conversion rate must be a numeric value.',
            'conversion_rate.min' => 'The conversion rate must be at least 0.01.',
            'decimal_places.max' => 'The decimal places cannot exceed 4 digits.',
            'sku.max' => 'The SKU cannot exceed 30 characters.',
            'description.max' => 'The description cannot exceed 5000 characters.',
            'avatar.mimes' => 'The avatar must be a JPG, JPEG, or PNG file.',
            'avatar.max' => 'The avatar size cannot exceed 2 MB.',

            // GST Details
            'is_gst_applicable.required' => 'Please specify if GST is applicable.',
            'gst_tax_id.required_if' => 'GST Tax ID is required when GST is applicable.',
            'hsn_sac_code.max' => 'The HSN/SAC code cannot exceed 8 characters.',
            'is_rcm_applicable.required' => 'Please specify if reverse charge mechanism (RCM) is applicable.',

            // Pricing Details for Sale Information
            'mrp.max' => 'The MRP cannot exceed 17 characters.',
            'sale_price.max' => 'The selling price cannot exceed 17 characters.',
            'sale_price_type.required' => 'The sale price type is required.',
            'discount_value.max' => 'The discount value cannot exceed 17 characters.',
            'discount_type.required' => 'The discount type is required.',
            'income_ledger_id.required' => 'The income ledger ID is required.',

            // Pricing Details for Purchase Information
            'purchase_price.max' => 'The purchase price cannot exceed 17 characters.',
            'purchase_price_type.required' => 'The purchase price type is required.',
            'expense_ledger_id.required' => 'The expense ledger ID is required.',

            // Other Details
            'decimal_places_for_rate.integer' => 'The decimal places for rate must be an integer.',
            'decimal_places_for_rate.max' => 'The decimal places for rate cannot exceed 5 digits.',
            'decimal_places_for_rate.min' => 'The decimal places for rate must be at least 2 digits.',

            // Opening Stock Details
            'opening_balance_type.required' => 'The opening balance type is required.',
            'quantity_unit.required' => 'The quantity unit is required.',
            'quantity.max' => 'The quantity cannot exceed 17 characters.',
            'rate.required' => 'The rate is required.',

            // img validation
            'item_image.mimes' => 'The item image must be a JPG, JPEG, PNG, SVG, or WebP file.',

            // custom fileds related validation messages
            'custom_fields_values.*.custom_field_id.required' => 'Please select custom field id.',
            'custom_fields_values.*.custom_field_id.exists' => 'Please select valid custom field id.',
            'custom_fields_values.*.value.required_with' => 'Please enter value.',

            'custom_fields_formula.*.formula.required' => 'Please enter formula.',
            'custom_fields_formula.*.is_system_field.required' => 'Please select custom field or system key.',
            'custom_fields_formula.*.custom_field_id.required_if' => 'Please select custom field when system key is not selected.',
            'custom_fields_formula.*.custom_field_id.integer' => 'Please select valid custom field id.',
            'custom_fields_formula.*.custom_field_id.exists' => 'Please select valid custom field id.',
            'custom_fields_formula.*.system_field_name.required_if' => 'Please select system key.',
            'custom_fields_formula.*.used_cf_ids_for_formula.required' => 'Please select at least one custom field for used in formula.',
        ];
    }
}
