<?php

namespace App\Http\Requests\ReactAPI;

use App\Models\DeliveryChallanTransaction;
use App\Rules\CheckAddLessLedgerRule;
use App\Rules\CheckBrokerRule;
use App\Rules\CheckCityRule;
use App\Rules\CheckCustomerAndSupplerRule;
use App\Rules\checkDateIsInFinancialYearRule;
use App\Rules\CheckGoodsAndServiceRule;
use App\Rules\CheckGstTaxesRule;
use App\Rules\CheckItemInvoiceLedgerRule;
use App\Rules\CheckLockDateRule;
use App\Rules\CheckQuantityDecimalMinRule;
use App\Rules\CheckStateRule;
use App\Rules\CheckTaxesTCSRule;
use App\Rules\CheckTransportRule;
use App\Rules\CheckUnitOfMeasurementRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

/**
 * Class SaleTransactionRequest
 */
class DeliveryChallanTransactionAPIRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return string[]
     */
    public function rules(): array
    {
        $isCompanyGstApplicable = isCompanyGstApplicable();
        $maxFile = 15;
        $transactionId = $this->route('id');
        if (isset($transactionId)) {
            $transaction = DeliveryChallanTransaction::findOrFail($transactionId);
            if ($transaction->media?->count() !== null) {
                $maxFile = 15 - $transaction->media->count();
            }
        }

        $itemsRules = [
            'challan_number' => 'required',
            'challan_date' => ['required', 'date', 'date_format:d-m-Y', new checkDateIsInFinancialYearRule(), new CheckLockDateRule('income')],
            // 'party_ledger_id' => ['required', new CheckCustomerAndSupplerRule()],
            'is_create_party' => 'nullable|in:0,1',
            'party_ledger_id' => [
                'required',
                $this->get('is_create_party') ? '' : 'integer',
                $this->get('is_create_party') ? '' : new CheckCustomerAndSupplerRule(),
            ],
            'gstin' => 'nullable|min:15|max:15|regex:/[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][0-9][a-zA-Z0-9][a-zA-Z0-9]$/',
            'other_details.po_date' => 'nullable|date|date_format:d-m-Y',
            'invoice_number' => 'nullable',
            'invoice_date' => ['nullable', 'date', 'date_format:d-m-Y'],
            'broker_details.broker_id' => ['nullable', new CheckBrokerRule()],
            'transport_details.transport_id' => ['nullable', new CheckTransportRule()],
            'transport_details.transporter_document_date' => 'nullable|date|date_format:d-m-Y',
            'narration' => 'nullable|string|max:5000',
            'term_and_condition' => 'nullable|string|max:5000',
            'custom_fields' => 'nullable|array',
            'custom_fields.*.custom_field_id' => ['required', 'integer', 'exists:transaction_custom_field,id'],
            'custom_fields.*.value' => 'required_with:custom_fields.*.custom_field_id',
            // 'invoice_type' => 'required|in:1,2', // Pending to be Added in mobile app
        ];

        //For Document Upload
        $itemsRules['delivery_challan_document.*'] = 'nullable|mimes:jpg,jpeg,png,pdf,xlsx,docx|max:2048';
        $itemsRules['delivery_challan_document'] = 'nullable|max:' . $maxFile;

        if ($this->filled('invoice_type') && $this->get('invoice_type') == DeliveryChallanTransaction::WITH_AMOUNT) {
            $deliveryChallanWithAmountRules = [
                // Main Classification Nature Type
                'main_classification_nature_type' => 'required|string',

                // Additional Charges
                'additional_charges' => 'nullable|array',
                'additional_charges.*.ac_ledger_id' => ['required', 'integer', new CheckItemInvoiceLedgerRule()],
                'additional_charges.*.ac_type' => 'integer|required_with:additional_charges.*.ac_ledger_id',
                'additional_charges.*.ac_value' => 'numeric|required_with:additional_charges.*.ac_ledger_id',
                'additional_charges.*.ac_total_without_tax' => 'numeric|required_with:additional_charges.*.ac_ledger_id',

                // TCS Details
                'tcs_details' => 'nullable|array',
                'tcs_details.tcs_tax_id' => ['nullable', 'integer', new CheckTaxesTCSRule()],
                'tcs_details.tcs_rate' => 'nullable|required_with:tcs_details.tcs_tax_id|numeric',
                'tcs_details.tcs_amount' => 'nullable|required_with:tcs_details.tcs_tax_id|numeric',

                // Add/Less
                'add_less' => 'nullable|array',
                'add_less.*.al_ledger_id' => ['required', 'integer', new CheckAddLessLedgerRule()],
                'add_less.*.al_is_show_in_print' => 'required|boolean',
                'add_less.*.al_type' => 'required_with:add_less.*.al_ledger_id|integer',
                'add_less.*.al_value' => 'required_with:add_less.*.al_ledger_id|numeric',
                'add_less.*.al_total' => 'required_with:add_less.*.al_ledger_id|numeric',

                // GST and Taxation
                'is_gst_enabled' => 'required|boolean',
                'is_cgst_sgst_igst_calculated' => 'required|boolean',
                'is_gst_na' => 'required|boolean',
                'is_round_off_not_changed' => 'required|boolean',

                // Financial Details
                'taxable_value' => 'required|numeric',
                'gross_value' => 'required|numeric',
                'grand_total' => 'required|numeric',
                'rounding_amount' => 'nullable|numeric',
            ];
            $deliveryChallanWithAmountRulesForGST = [];
            if ($isCompanyGstApplicable) {
                $deliveryChallanWithAmountRulesForGST = [
                    // Additional Charges
                    'additional_charges.*.ac_gst_rate_id' => 'nullable|integer',

                    // GST and Taxation
                    'is_rcm_applicable' => 'required|integer|in:0,1',
                    // Financial Details
                    'cgst' => 'required|numeric',
                    'sgst' => 'required|numeric',
                    'igst' => 'required|numeric',
                    'cess' => 'required|numeric',
                    'items.*.gst_id' => ['required', 'integer', new CheckGstTaxesRule()],
                ];
            }
            if (! empty($deliveryChallanWithAmountRulesForGST)) {
                $deliveryChallanWithAmountRules = array_merge($deliveryChallanWithAmountRules, $deliveryChallanWithAmountRulesForGST);
            }
            $itemsRules = array_merge($itemsRules, $deliveryChallanWithAmountRules);

            // For Delivery Challan With Amount Items
            if ($this->get('items')) {
                $deliveryChallanItemsRules = [
                    'items' => 'required|array|min:1',
                    'items.*.item_id' => ['required', 'integer', new CheckGoodsAndServiceRule()],
                    'items.*.ledger_id' => ['required', new CheckItemInvoiceLedgerRule()],
                    'items.*.additional_description' => 'nullable|string|max:5000',
                    'items.*.rpu' => 'required|numeric',
                    'items.*.with_tax' => 'required|boolean',
                    'items.*.mrp' => 'nullable|numeric',
                    // 'items.*.quantity' => 'required|numeric|min:1',
                    'items.*.discount_type' => 'required|integer|in:1,2', // Assuming 1 for fixed, 2 for percentage
                    'items.*.discount_value' => 'nullable|numeric',
                    'items.*.discount_type_2' => 'nullable|integer|in:1,2', // Assuming 1 or 2 are valid types
                    'items.*.discount_value_2' => 'nullable|numeric',
                    'items.*.total' => 'required|numeric',
                    'items.*.cess' => 'nullable|numeric',
                ];

                foreach ($this->input('items', []) as $key => $item) {
                    $deliveryChallanItemsRules["items.$key.unit_id"] = ['required', 'integer', new CheckUnitOfMeasurementRule($item['item_id'])];
                }
                foreach ($this->input('items', []) as $key => $item) {
                    $deliveryChallanItemsRules["items.$key.quantity"] = ['required', 'numeric', new CheckQuantityDecimalMinRule($item['item_id'])];
                }

                $itemsRules = array_merge($itemsRules, $deliveryChallanItemsRules);
            }
        } else {
            if ($this->get('items')) {
                $deliveryChallanItemsRules = [
                    'items' => 'required|array|min:1',
                    'items.*.item_id' => ['required', 'integer', new CheckGoodsAndServiceRule()],
                    'items.*.additional_description' => 'nullable|string|max:5000',
                    // 'items.*.quantity' => 'required|numeric|min:1',
                    'items.*.custom_fields' => 'nullable|array',
                    'items.*.custom_fields.*.custom_field_id' => ['required', 'integer', 'exists:item_custom_field,id'],
                    'items.*.custom_fields.*.value' => 'required_with:items.*.custom_fields.*.custom_field_id',
                ];

                foreach ($this->input('items', []) as $key => $item) {
                    $itemsRules["items.$key.unit_id"] = ['required', 'integer', new CheckUnitOfMeasurementRule($item['item_id'])];
                }
                foreach ($this->input('items', []) as $key => $item) {
                    $itemsRules["items.$key.quantity"] = ['required', 'numeric', new CheckQuantityDecimalMinRule($item['item_id'])];
                }

                $itemsRules = array_merge($itemsRules, $deliveryChallanItemsRules);
            }
        }

        if ($isCompanyGstApplicable) {
            $itemsRules['shipping_address_id'] = 'nullable|integer'; // Belongs to the address table for editing time only, selecting from multiple addresses of a party.
            $itemsRules['dispatch_address_id'] = 'nullable|integer';
            $itemsRules['billing_address.address_1'] = 'nullable|string|max:255';
            $itemsRules['billing_address.address_2'] = 'nullable|string|max:255';
            $itemsRules['billing_address.country_id'] = 'required|integer';
            $itemsRules['billing_address.state_id'] = ['required', 'integer', new CheckStateRule()];
            $itemsRules['billing_address.city_id'] = ['nullable', 'integer', new CheckCityRule()];

            $itemsRules['shipping_address'] = 'nullable|array';
            $itemsRules['shipping_address.address_name'] = 'nullable|string';
            $itemsRules['shipping_address.shipping_name'] = 'nullable|string';
            $itemsRules['shipping_address.shipping_gstin'] = 'nullable|min:15|max:15|';
            $itemsRules['shipping_address.address_1'] = 'nullable|string|max:255';
            $itemsRules['shipping_address.address_2'] = 'nullable|string|max:255';
            $itemsRules['shipping_address.country_id'] = 'required_with:shipping_address|integer';
            $itemsRules['shipping_address.state_id'] = ['required_with:shipping_address', 'integer', new CheckStateRule()];
            $itemsRules['shipping_address.city_id'] = ['nullable', new CheckCityRule()];
        } else {
            $itemsRules['shipping_address_id'] = 'nullable|integer'; // Belongs to the address table for editing time only, selecting from multiple addresses of a party.
            $itemsRules['dispatch_address_id'] = 'nullable|integer';
            $itemsRules['billing_address.address_1'] = 'nullable|string|max:255';
            $itemsRules['billing_address.address_2'] = 'nullable|string|max:255';
            $itemsRules['billing_address.country_id'] = 'nullable|integer';
            $itemsRules['billing_address.state_id'] = ['nullable', 'integer', new CheckStateRule()];
            $itemsRules['billing_address.city_id'] = ['nullable', 'integer', new CheckCityRule()];

            $itemsRules['shipping_address'] = 'nullable|array';
            $itemsRules['shipping_address.address_name'] = 'nullable|string';
            $itemsRules['shipping_address.address_1'] = 'nullable|string|max:255';
            $itemsRules['shipping_address.address_2'] = 'nullable|string|max:255';
            $itemsRules['shipping_address.shipping_name'] = 'nullable|string';
            $itemsRules['shipping_address.country_id'] = 'nullable|integer';
            $itemsRules['shipping_address.state_id'] = ['nullable', new CheckStateRule()];
            $itemsRules['shipping_address.city_id'] = ['nullable', new CheckCityRule()];
        }

        return $itemsRules;
    }

    /**
     * @return string[]
     */
    public function messages(): array
    {
        $message = [
            'challan_number.required' => 'The challan number is required.',
            'challan_date.required' => 'The challan date is required.',
            'challan_date.date' => 'The challan date must be a valid date.',
            'challan_date.date_format' => 'The challan date must be in the format dd-mm-yyyy.',
            'party_ledger_id.required' => 'The party ledger is required.',
            'gstin.min' => 'The GSTIN must be at least 15 characters long.',
            'gstin.max' => 'The GSTIN must not exceed 15 characters.',
            'gstin.regex' => 'The GSTIN format is invalid.',
            'other_details.po_date.date' => 'The PO date must be a valid date.',
            'other_details.po_date.date_format' => 'The PO date must be in the format dd-mm-yyyy.',
            'items.*.custom_fields.*.custom_field_id.required' => 'The custom field item ID is required.',
            'items.*.custom_fields.*.custom_field_id.integer' => 'The custom field item ID must be a valid integer.',
            'items.*.custom_fields.*.custom_field_id.exists' => 'The selected custom field item ID is invalid.',
            'items.*.custom_fields.*.value.required_with' => 'The value is required when a custom field item ID is provided.',

            'billing_address.country_id.required' => 'The country for billing address is required.',
            'billing_address.state_id.required' => 'The state for billing address is required.',
            'billing_address.city_id.required' => 'The city for billing address is required.',
            'billing_address.country_id.integer' => 'The country for billing address must be an integer.',
            'billing_address.state_id.integer' => 'The state for billing address must be an integer.',
            'billing_address.city_id.integer' => 'The city for billing address must be an integer.',
            'shipping_address.state_id.nullable' => 'Shipping state is optional but must be valid if provided.',
            'shipping_address.city_id.nullable' => 'Shipping city is optional but must be valid if provided.',

            //For Document Upload
            'delivery_challan_document.*.mimes' => 'The delivery challan document must be a file of type: jpg, jpeg, png, pdf, xlsx, docx.',
            'delivery_challan_document.*.max' => 'The delivery challan document may not be greater than 2MB.',
            'delivery_challan_document.max' => 'The upload document may not be greater than 15 files.',

            // Additional Charges
            'additional_charges.*.ac_ledger_id.required' => 'The additional charge ledger ID is required.',
            'additional_charges.*.ac_ledger_id.integer' => 'The additional charge ledger ID must be an integer.',
            'additional_charges.*.ac_type.required_with' => 'The additional charge type is required.',
            'additional_charges.*.ac_type.integer' => 'The additional charge type must be an integer.',
            'additional_charges.*.ac_value.required_with' => 'The additional charge value is required.',
            'additional_charges.*.ac_value.numeric' => 'The additional charge value must be a number.',
            'additional_charges.*.ac_gst_rate_id.required' => 'The additional charge GST rate ID is required.',
            'additional_charges.*.ac_gst_rate_id.integer' => 'The additional charge GST rate ID must be an integer.',
            'additional_charges.*.ac_total_without_tax.required_with' => 'The additional charge total is required.',
            'additional_charges.*.ac_total_without_tax.numeric' => 'The additional charge total must be a number.',

            // Add/Less
            'add_less.*.al_ledger_id.required' => 'The add/less ledger ID is required.',
            'add_less.*.al_ledger_id.integer' => 'The add/less ledger ID must be an integer.',
            'add_less.*.al_is_show_in_print.required' => 'The add/less show in print field is required.',
            'add_less.*.al_is_show_in_print.boolean' => 'The add/less show in print field must be true or false.',
            'add_less.*.al_type.required_with' => 'The add/less type is required.',
            'add_less.*.al_type.integer' => 'The add/less type must be an integer.',
            'add_less.*.al_value.required_with' => 'The add/less value is required.',
            'add_less.*.al_value.numeric' => 'The add/less value must be a number.',
            'add_less.*.al_total.required_with' => 'The add/less total is required.',
            'add_less.*.al_total.numeric' => 'The add/less total must be a number.',

            // Items (for Item Invoice)
            'items.required' => 'At least one item is required.',
            'items.*.item_id.required' => 'The item ID is required for all items.',
            'items.*.rpu.required' => 'The RPU is required for all items.',
            'items.*.rpu.numeric' => 'The RPU must be a numeric value for all items.',
            'items.*.rpu.max' => 'The RPU not exceed 10 characters for all items.',
            'items.*.mrp.max' => 'The MRP not exceed 10 characters for all items.',
            'items.*.discount_value.max' => 'The Discount 1 not exceed 10 characters for all items.',
            'items.*.discount_value_2.max' => 'The Discount 2 not exceed 10 characters for all items.',
            'items.*.with_tax.required' => 'The tax applicability is required for all items.',
            'items.*.quantity.required' => 'The quantity is required for all items.',
            'items.*.quantity.numeric' => 'The quantity must be a numeric value for all items.',
            'items.*.quantity.min' => 'The quantity must be at least 1 for all items.',
            'items.*.additional_description.max' => 'The additional description must not exceed 5000 characters.',
        ];

        return $message;
    }
}
