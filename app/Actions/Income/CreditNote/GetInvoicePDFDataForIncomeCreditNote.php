<?php

namespace App\Actions\Income\CreditNote;

use App\Actions\CommonAction\PreparePDFItemDataAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\PrepareCustomFieldDataForPdfPreview;
use App\Actions\Income\Sale\SaleInvoiceFormatterAction;
use App\Actions\v1\PdfConfiguration\GetPdfAdjustmentsAction;
use App\Models\Address;
use App\Models\Company;
use App\Models\CompanySetting;
use App\Models\Configuration\IncomeCreditNote;
use App\Models\EwayBill;
use App\Models\IncomeCreditNoteItemTransaction;
use App\Models\IncomeCreditNoteTransaction;
use App\Models\InvoicesLabel;
use App\Models\ItemCustomField;
use App\Models\Master\Bank;
use App\Models\Master\IncomeCreditNoteTransactionMaster;
use App\Models\PrintSetting;
use App\Models\RearrangeItem;
use App\Models\SaleTransaction;
use App\Models\TransactionCustomField;
use Carbon\Carbon;
use Lorisleiva\Actions\Concerns\AsAction;

class GetInvoicePDFDataForIncomeCreditNote
{
    use AsAction;

    public function handle(int $creditNoteId)
    {
        $data = [];

        // Load Income Credit Note Transaction
        $transaction = IncomeCreditNoteTransaction::with(
            'addresses',
            'customer.model',
            'transport',
            'incomeCreditNoteItems.items',
            'incomeCreditNoteLedgers.ledgers',
            'addLess.ledger',
            'additionalCharges.ledger',
            'dispatchAddress',
            'bankLedgerDetails.model',
        )->whereId($creditNoteId)->firstOrFail();

        $data['transaction'] = $transaction;

        // Load Company Data  and Store in session
        $data['currentCompany'] = Company::with('addresses', 'companyTax', 'user', 'mailConfiguration')->findOrFail($transaction->company_id);
        session(['current_company' => $data['currentCompany']]);
        setCompanyInSession($data['currentCompany']);

        // Process Add/Less and Additional Charges
        $data['addLess'] = prepareAddLess($transaction->addLess);
        $data['additionalCharges'] = prepareAdditionalCharges($transaction->additionalCharges);

        // Custom fields
        $data['customFieldValues'] = GetTransactionCustomFieldsAction::run(TransactionCustomField::INCOME_CREDIT_NOTE, $creditNoteId, IncomeCreditNoteTransaction::class);

        // Customer Details
        $data['customerDetail'] = $data['transaction']->customer;
        $data['panNumber'] = $data['customerDetail']->model->pan_card_number ?? null;
        $data['showPanNumber'] = isset($data['invoiceSetting']) && isset($data['invoiceSetting']['pan_no']) ? $data['invoiceSetting']['pan_no'] : true;
        $saleTransaction = SaleTransaction::whereId($transaction->original_inv_no)->first();
        $data['originalInvoiceNumber'] = $saleTransaction->full_invoice_number ?? null;
        $data['originalInvoiceDate'] = $saleTransaction->date ?? null;

        // Load Address Data
        $data['companyBillingAddress'] = $data['currentCompany']->addresses->where('address_type', Company::BILLING_ADDRESS)->first();
        $shippingAddress = $data['transaction']->addresses->where('address_type', Company::DISPATCH_ADDRESS)->first();
        $data['billingAddress'] = $data['transaction']->addresses->where('address_type', IncomeCreditNoteTransaction::BILLING_ADDRESS)->first();
        if ($transaction->same_as_billing) {
            $data['shippingAddress'] = $data['billingAddress'];
            $data['transaction']->shipping_gstin = $data['transaction']->gstin;
        } else {
            $data['shippingAddress'] = Address::whereId($data['transaction']->shipping_address_id)->first() ?? $data['transaction']->addresses->where('address_type', IncomeCreditNoteTransaction::SHIPPING_ADDRESS)->first();
        }
        $data['dispatchAddress'] = $data['transaction']->dispatchAddress;
        $data['ledgerShippingAddress'] = $data['customerDetail']->model->shippingAddress;

        $incomeCreditNoteTransactionMaster = IncomeCreditNoteTransactionMaster::with('ledgerDetails.model')->whereCompanyId($data['currentCompany']->id)->first();
        $data['eWayBill'] = EwayBill::whereTransactionType(IncomeCreditNoteTransaction::class)->whereTransactionId($creditNoteId)->whereNot('is_canceled', 1)->first();

        $data['invoiceName'] = 'Credit Note Invoice';
        $data['configuration'] = IncomeCreditNote::first();

        $data['bankDetail'] = null;
        $data['accountNumber'] = null;
        $data['accountType'] = null;
        $data['branchName'] = null;

        if ($transaction->bankLedgerDetails) {
            /** @var Bank $bankDetail */
            $bankDetail = $transaction->bankLedgerDetails?->model ?? null;
            $data['bankDetail'] = $bankDetail;
            $data['accountNumber'] = $data['bankDetail'] ? $data['bankDetail']->account_number : null;
            $data['accountType'] = $data['bankDetail'] ? $data['bankDetail']->account_type : null;
            $data['branchName'] = $data['bankDetail'] ? $data['bankDetail']->branch_name : null;
        }

        $data['transactionItems'] = PreparePDFItemDataAction::run($data['transaction']->incomeCreditNoteItems);
        if (! empty($data['transactionItems'])) {
            foreach ($data['transactionItems'] as $key => $item) {
                $data['transactionItems'][$key]['customItemsValues'] = PrepareCustomFieldDataForPdfPreview::run(ItemCustomField::INCOME_CREDIT_NOTE, $item->id, IncomeCreditNoteItemTransaction::class);
            }
        }

        $data['transactionLedgers'] = $data['transaction']->incomeCreditNoteLedgers;

        $data['invoiceDate'] = Carbon::parse($data['transaction']->date)->format('d-m-Y');
        $data['itemType'] = $data['transaction']->cn_item_type;

        // Credit Period
        $data = array_merge($data, $this->calculateCreditPeriod($data['transaction']));

        $data['isCompanyGstApplicable'] = $data['currentCompany']->is_gst_applicable ? true : false;

        $data['showGst'] = false;

        if (! $data['isCompanyGstApplicable']) {
            if (! empty($data['customerDetail']->model->gstin)) {
                $data['showGst'] = true;
            }
        } elseif (! empty($transaction->gstin)) {
            $data['showGst'] = true;
        }
        $data['invoiceSetting'] = CompanySetting::pluck('value', 'key')->toArray();

        $data['showCreditPeriod'] = false;

        // Credit Note Invoice Label
        $data['invoiceDetailLabel'] = 'Credit Note Details';
        $data['invoiceNumberLabel'] = 'Credit Note Number';
        $data['invoiceDateLabel'] = 'Credit Note Date';
        $data['taxInvoice'] = $incomeCreditNoteTransactionMaster->title_of_print;

        // Print Settings For Invoice
        $data['showPrintSettings'] = PrintSetting::pluck('status', 'name')->toArray();
        $data['changeLabel'] = InvoicesLabel::whereTransactionType(InvoicesLabel::SALE_TRANSACTION)->pluck('label_value', 'label_name')->toArray();
        $isA5Pdf = isset($data['invoiceSetting']['pdf_format']) ? ($data['invoiceSetting']['pdf_format'] == CompanySetting::A5 || $data['invoiceSetting']['pdf_format'] == CompanySetting::LANDSCAPE_A5) : false;
        // Custom Font Size
        $data['customFontSize'] = GetPdfAdjustmentsAction::run($isA5Pdf);
        $data['customProp'] = InvoicesLabel::where('is_custom_label', InvoicesLabel::PROP_NAME)->select('label_value', 'label_name')->first();

        $data = SaleInvoiceFormatterAction::run($data, RearrangeItem::INCOME_CREDIT_NOTE);

        return $data;
    }

    private function calculateCreditPeriod($transaction)
    {
        $creditPeriod = $transaction->credit_period ?? $transaction->customer->model->credit_limit_period;
        $creditType = $transaction->credit_period_type ?? $transaction->customer->model->credit_period_type;

        if (! $creditPeriod) {
            return ['creditPeriod' => null, 'dueDate' => null];
        }

        $dueDate = Carbon::parse($transaction->date);
        if ($creditType === SaleTransaction::CREDIT_PERIOD_TYPE_MONTH) {
            $dueDate->addMonths($creditPeriod);
        } elseif ($creditType === SaleTransaction::CREDIT_PERIOD_TYPE_DAY) {
            $dueDate->addDays($creditPeriod);
        }

        return [
            'creditPeriod' => getCreditPeriod($creditPeriod, $creditType),
            'dueDate' => $dueDate->format('d-m-Y'),
        ];
    }
}
