<?php

namespace App\Actions\Income\CreditNote;

use App\Models\IncomeCreditNoteItemTransaction;
use App\Models\IncomeCreditNoteLedgerTransaction;
use App\Models\IncomeCreditNoteTransaction;
use App\Models\ItemCustomFieldValue;
use App\Models\PaymentTransaction;
use Lorisleiva\Actions\Concerns\AsAction;

class HardDeleteCreditNoteTransaction
{
    use AsAction;

    public function handle($id, $allTransaction = false, $isDirectHardDelete = false)
    {
        $query = $isDirectHardDelete ? IncomeCreditNoteTransaction::withTrashed() : IncomeCreditNoteTransaction::onlyTrashed();
        if ($allTransaction) {
            $incomeCreditNoteTransactions = $query->get();
        } else {
            $incomeCreditNoteTransactions = $query->whereId($id)->get();
        }

        foreach ($incomeCreditNoteTransactions as $incomeCreditNote) {
            PaymentTransaction::withTrashed()
                ->where('company_id', $incomeCreditNote->company_id)
                ->wherePaymentVoucherNumber('income-credit-note/'.$incomeCreditNote->full_invoice_number)->financialYearDate()?->forceDelete();

            $itemsIds = $incomeCreditNote->incomeCreditNoteItems->pluck('id')->toArray();
            $ledgersIds = $incomeCreditNote->incomeCreditNoteLedgers->pluck('id')->toArray();
            $allCFIds = array_merge($itemsIds, $ledgersIds);
            ItemCustomFieldValue::whereIn('model_id', $allCFIds)->whereIn('model_type', [IncomeCreditNoteItemTransaction::class, IncomeCreditNoteLedgerTransaction::class])?->delete();

            $incomeCreditNote->customFieldValues()->delete();
            $incomeCreditNote->addresses()->delete();
            $incomeCreditNote->forceDelete();
        }

        return true;
    }
}
