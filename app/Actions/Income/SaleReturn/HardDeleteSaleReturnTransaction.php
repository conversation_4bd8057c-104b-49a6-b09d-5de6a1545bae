<?php

namespace App\Actions\Income\SaleReturn;

use App\Models\ItemCustomFieldValue;
use App\Models\PaymentTransaction;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleReturnLedgerTransaction;
use App\Models\SaleReturnTransaction;
use Lorisleiva\Actions\Concerns\AsAction;

class HardDeleteSaleReturnTransaction
{
    use AsAction;

    public function handle($id, $allTransaction = false, $isDirectHardDelete = false)
    {
        $query = $isDirectHardDelete ? SaleReturnTransaction::withTrashed() : SaleReturnTransaction::onlyTrashed();
        if ($allTransaction) {
            $saleReturnTransactions = $query->get();
        } else {
            $saleReturnTransactions = $query->whereId($id)->get();
        }

        foreach ($saleReturnTransactions as $saleReturn) {
            PaymentTransaction::withTrashed()
                ->where('company_id', $saleReturn->company_id)
                ->wherePaymentVoucherNumber('sale-return/'.$saleReturn->full_invoice_number)->financialYearDate()?->forceDelete();

            $itemsIds = $saleReturn->saleReturnItems->pluck('id')->toArray();
            $ledgersIds = $saleReturn->saleReturnLedgers->pluck('id')->toArray();
            $allCFIds = array_merge($itemsIds, $ledgersIds);
            ItemCustomFieldValue::whereIn('model_id', $allCFIds)->whereIn('model_type', [SaleReturnItemTransaction::class, SaleReturnLedgerTransaction::class])?->delete();

            $saleReturn->customFieldValues()->delete();
            $saleReturn->addresses()->delete();
            $saleReturn->forceDelete();
        }

        return true;
    }
}
