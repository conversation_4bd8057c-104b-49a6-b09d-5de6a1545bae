<?php

namespace App\Actions\v1\ItemMaster;

use App\Actions\CustomFieldsItemMaster\AddUpdateCustomFieldValueOfItemMaster;
use App\Actions\CustomFieldsItemMaster\GetItemCustomFields;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Models\FinancialYearItemOpeningBalance;
use App\Models\GstTax;
use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldDefaultValue;
use App\Models\ItemCustomFieldFormula;
use App\Models\ItemCustomFieldSetting;
use App\Models\Master\ItemMaster;
use App\Models\Master\ItemMasterGoods;
use App\Models\Master\ItemMasterService;
use App\Models\UnitOfMeasurement;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;
use Pic<PERSON>er\Barcode\BarcodeGeneratorPNG;

class StoreOrUpdateItemAction
{
    use AsAction;

    public const STORE = 'store';

    public const UPDATE = 'update';

    public const WITH_GST_TYPE = 1;

    public const WITHOUT_GST_TYPE = 2;

    public function handle($input, $method, $item = null)
    {
        try {
            DB::beginTransaction();
            $data = null;
            $input['quantity_unit'] = isset($input['quantity_unit']) && ! empty($input['quantity_unit']) ? ($input['quantity_unit'] == 0 ? null : $input['quantity_unit']) : null;

            if (! empty($item) && $input['item_type'] != $item->item_type) {
                $item->model->delete();
                $method = self::STORE;
                if ($input['item_type'] != ItemMaster::ITEM_MASTER_GOODS) {
                    // FinancialYearItemOpeningBalance::where('item_id' , $item->id)->delete();
                    FinancialYearItemOpeningBalance::where('item_id', $item->id)->update([
                        'opening_balance_qty' => 0,
                        'opening_balance_rate' => 0,
                    ]);
                }
            }

            if ($input['item_type'] == ItemMaster::ITEM_MASTER_GOODS) {
                $data = $this->createUpdateMasterItemGoods($input, $method, $item);
            }

            if ($input['item_type'] == ItemMaster::ITEM_MASTER_SERVICE) {
                $data = $this->createUpdateMasterItemServices($input, $method, $item);
            }
            // TO_COMMENT =>
            $data->unitOfArray = UnitOfMeasurement::whereIn('id', [$data->model->unit_of_measurement, $data->model->secondary_unit_of_measurement])->pluck('name', 'id')->toArray();
            $data->units_of_array = UnitOfMeasurement::whereIn('id', [$data->model->unit_of_measurement, $data->model->secondary_unit_of_measurement])->get()->toArray();
            $data->model->gst_tax_rate = $data->model->gstTax->tax_rate ?? null;
            $data->model->cess_rate = $data->model->gstGstCessRate->rate ?? null;

            if (isset($input['custom_fields']) && count($input['custom_fields']) > 0) {
                AddUpdateCustomFieldValueOfItemMaster::run($input, $data->id);
            } else {
                ItemCustomFieldSetting::where('item_id', $data->id)->delete();
                ItemCustomFieldDefaultValue::where('item_id', $data->id)->delete();
                ItemCustomFieldFormula::where('item_id', $data->id)->delete();
            }

            $data->custom_fields_value = GetItemCustomFields::run($data->id);

            /* This code for when direct update item from any transaction */
            if (isset($input['transaction_type']) && isset($input['transaction_item_id'])) {
                $data->transaction_item_custom_fields_value = GetAllCustomFieldsItemTransactionWise::run(
                    $input['transaction_type'],
                    $input['transaction_item_id'],
                    ItemCustomField::ALL_TRANSACTION_TYPE[$input['transaction_type']],
                    $data->id
                );
            }

            DB::commit();

            return $data;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    public function createUpdateMasterItemGoods($input, $method, $item = null)
    {
        try {
            $itemMasterGoodsData = $this->prepareGoodsItemData($input);

            if ($method == self::STORE) {
                $itemMasterGoods = ItemMasterGoods::create($itemMasterGoodsData);
            } else {
                $itemMasterGoods = ItemMasterGoods::find($item->model->id);
                $itemMasterGoods->update($itemMasterGoodsData);
            }

            return $this->createUpdateModel($input, $itemMasterGoods, $item, $method);
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function prepareGoodsItemData($input)
    {
        $salePriceType = $input['sale_price_type'];
        $saleWithGstPrice = isset($input['sale_price']) ? removeCommaSeparator($input['sale_price']) : null;
        $saleWithoutGstPrice = isset($input['sale_price']) ? removeCommaSeparator($input['sale_price']) : null;
        $purchasePriceType = $input['purchase_price_type'];
        $purchaseWithGstPrice = isset($input['purchase_price']) ? removeCommaSeparator($input['purchase_price']) : null;
        $purchaseWithoutGstPrice = isset($input['purchase_price']) ? removeCommaSeparator($input['purchase_price']) : null;

        $gst = null;
        $currentCompany = getCurrentCompany();
        $gstTaxId = $currentCompany->is_gst_applicable ? 12 : null;

        if ($input['is_gst_applicable']) {
            $gst = GstTax::findOrFail($input['gst_tax_id']);
            $gstTaxId = $gst->id;
            $decimalPlacesForRate = isset($input['decimal_places_for_rate']) && ! empty($input['decimal_places_for_rate']) ? $input['decimal_places_for_rate'] : 2;

            if (isset($input['sale_price'])) {
                if ($salePriceType == self::WITH_GST_TYPE) {
                    $saleWithGstPrice = removeCommaSeparator($input['sale_price']);
                    $saleWithoutGstPrice = round(removeCommaSeparator($input['sale_price']) - (removeCommaSeparator($input['sale_price']) * $gst->tax_rate) / (100 + $gst->tax_rate), $decimalPlacesForRate);
                } else {
                    $saleWithoutGstPrice = removeCommaSeparator($input['sale_price']);
                    $saleWithGstPrice = round((removeCommaSeparator($input['sale_price']) + (removeCommaSeparator($input['sale_price']) * $gst->tax_rate) / 100), $decimalPlacesForRate);
                }
            }

            if (isset($input['purchase_price'])) {
                if ($purchasePriceType == self::WITH_GST_TYPE) {
                    $purchaseWithGstPrice = removeCommaSeparator($input['purchase_price']);
                    $purchaseWithoutGstPrice = round(removeCommaSeparator($input['purchase_price']) - (removeCommaSeparator($input['purchase_price']) * $gst->tax_rate) / (100 + $gst->tax_rate), $decimalPlacesForRate);
                } else {
                    $purchaseWithoutGstPrice = removeCommaSeparator($input['purchase_price']);
                    $purchaseWithGstPrice = round((removeCommaSeparator($input['purchase_price']) + (removeCommaSeparator($input['purchase_price']) * $gst->tax_rate) / 100), $decimalPlacesForRate);
                }
            }
        }

        /* Is Reorderable in Item Level */
        $isReOrderable = isset($input['is_re_order']) ? $input['is_re_order'] : false;
        $reOrderLevel = null;
        $reOrderUOM = null;
        if ($isReOrderable) {
            $reOrderLevel = isset($input['re_order_level']) ? $input['re_order_level'] : null;
            $reOrderUOM = isset($input['re_order_uom']) ? $input['re_order_uom'] : null;
        }

        return [
            'unit_of_measurement' => $input['primary_unit_of_measurement'],
            'secondary_unit_of_measurement' => $input['secondary_unit_of_measurement'] ?? null,
            'conversion_rate' => $input['conversion_rate'] ?? null,
            'decimal_places' => $input['decimal_places'] ?? 0,
            'description' => $input['is_description_same_as_item_name'] ? $input['item_name'] : $input['description'] ?? null,
            'is_description_same_as_item_name' => $input['is_description_same_as_item_name'] ?? false,
            'is_gst_applicable' => $input['is_gst_applicable'] ?? false,
            'gst_tax_id' => $gstTaxId,
            'hsn_sac_code' => $input['hsn_sac_code'] ?? null,
            'gst_cess_rate' => $input['is_gst_applicable'] && isset($input['gst_cess_rate']) ? $input['gst_cess_rate'] : null,
            'is_rcm_applicable' => $input['is_gst_applicable'] && isset($input['is_rcm_applicable']) ? $input['is_rcm_applicable'] : false,
            'mrp' => removeCommaSeparator($input['mrp'] ?? null),
            'sale_price_type' => $salePriceType,
            'selling_price_with_gst' => $saleWithGstPrice,
            'selling_price_without_gst' => $saleWithoutGstPrice,
            'discount_type' => $input['discount_type'] ?? null,
            'discount_value' => removeCommaSeparator($input['discount_value'] ?? null),
            'income_ledger_id' => $input['income_ledger_id'] ?? null,
            'purchase_price_type' => $purchasePriceType,
            'purchase_price_with_gst' => $purchaseWithGstPrice,
            'purchase_price_without_gst' => $purchaseWithoutGstPrice,
            'purchase_discount_type' => $input['purchase_discount_type'] ?? null,
            'purchase_discount_value' => removeCommaSeparator($input['purchase_discount_value'] ?? null),
            'expense_ledger_id' => $input['expense_ledger_id'] ?? null,
            'decimal_places_for_rate' => $input['decimal_places_for_rate'] ?? 2,
            'quantity_unit' => $input['quantity_unit'] ?? null,
            'quantity' => $input['quantity'] ?? null,
            'is_with_gst_rate' => $input['is_with_gst_rate'] ?? null,
            'is_re_order' => $isReOrderable,
            're_order_level' => $reOrderLevel,
            're_order_uom' => $reOrderUOM,
            'rate' => isset($input['rate']) ? removeCommaSeparator($input['rate']) : null,
            'via_api' => $input['via_api'] ?? false,
        ];
    }

    public function createUpdateMasterItemServices($input, $method, $itemMaster)
    {
        try {
            $itemMasterServiceData = $this->prepareServicesItemData($input);

            if ($method == self::STORE) {
                $itemMasterService = ItemMasterService::create($itemMasterServiceData);
            } else {
                $itemMasterService = ItemMasterService::find($itemMaster->model->id);
                $itemMasterService->update($itemMasterServiceData);
            }

            return $this->createUpdateModel($input, $itemMasterService, $itemMaster, $method);
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function prepareServicesItemData($input)
    {
        $salePriceType = $input['sale_price_type'];
        $saleWithGstPrice = isset($input['sale_price']) ? removeCommaSeparator($input['sale_price']) : null;
        $saleWithoutGstPrice = isset($input['sale_price']) ? removeCommaSeparator($input['sale_price']) : null;
        $purchasePriceType = $input['purchase_price_type'];
        $purchaseWithGstPrice = isset($input['purchase_price']) ? removeCommaSeparator($input['purchase_price']) : null;
        $purchaseWithoutGstPrice = isset($input['purchase_price']) ? removeCommaSeparator($input['purchase_price']) : null;

        $gst = null;
        $gstTaxId = getCurrentCompany()->is_gst_applicable ? 12 : null;

        if ($input['is_gst_applicable']) {
            $gst = GstTax::findOrFail($input['gst_tax_id']);
            $gstTaxId = $gst->id;
            $decimalPlacesForRate = isset($input['decimal_places_for_rate']) && ! empty($input['decimal_places_for_rate']) ? $input['decimal_places_for_rate'] : 2;

            if (isset($input['sale_price'])) {
                if ($salePriceType == self::WITH_GST_TYPE) {
                    $saleWithGstPrice = removeCommaSeparator($input['sale_price']);
                    $saleWithoutGstPrice = round(removeCommaSeparator($input['sale_price']) - (removeCommaSeparator($input['sale_price']) * $gst->tax_rate) / (100 + $gst->tax_rate), $decimalPlacesForRate);
                } else {
                    $saleWithoutGstPrice = removeCommaSeparator($input['sale_price']);
                    $saleWithGstPrice = round((removeCommaSeparator($input['sale_price']) + (removeCommaSeparator($input['sale_price']) * $gst->tax_rate) / 100), $decimalPlacesForRate);
                }
            }

            if (isset($input['purchase_price'])) {
                if ($purchasePriceType == self::WITH_GST_TYPE) {
                    $purchaseWithGstPrice = removeCommaSeparator($input['purchase_price']);
                    $purchaseWithoutGstPrice = round(removeCommaSeparator($input['purchase_price']) - (removeCommaSeparator($input['purchase_price']) * $gst->tax_rate) / (100 + $gst->tax_rate), $decimalPlacesForRate);
                } else {
                    $purchaseWithoutGstPrice = removeCommaSeparator($input['purchase_price']);
                    $purchaseWithGstPrice = round((removeCommaSeparator($input['purchase_price']) + (removeCommaSeparator($input['purchase_price']) * $gst->tax_rate) / 100), $decimalPlacesForRate);
                }
            }
        }

        return [
            'unit_of_measurement' => $input['primary_unit_of_measurement'],
            'secondary_unit_of_measurement' => $input['secondary_unit_of_measurement'] ?? null,
            'conversion_rate' => $input['conversion_rate'] ?? null,
            'decimal_places' => $input['decimal_places'] ?? 0,
            'description' => $input['is_description_same_as_item_name'] ? $input['item_name'] : $input['description'] ?? null,
            'is_description_same_as_item_name' => $input['is_description_same_as_item_name'] ?? false,
            'is_gst_applicable' => $input['is_gst_applicable'] ?? false,
            'gst_tax_id' => $gstTaxId,
            'hsn_sac_code' => $input['hsn_sac_code'] ?? null,
            'gst_cess_rate' => $input['gst_cess_rate'] ?? null,
            'is_rcm_applicable' => $input['is_rcm_applicable'] ?? false,
            'discount_type' => $input['discount_type'] ?? null,
            'discount_value' => removeCommaSeparator($input['discount_value'] ?? null),
            'sale_price_type' => $salePriceType,
            'selling_price_with_gst' => $saleWithGstPrice,
            'selling_price_without_gst' => $saleWithoutGstPrice,
            'income_ledger_id' => $input['income_ledger_id'] ?? null,
            'purchase_price_type' => $purchasePriceType,
            'purchase_price_with_gst' => $purchaseWithGstPrice,
            'purchase_price_without_gst' => $purchaseWithoutGstPrice,
            'purchase_discount_type' => $input['purchase_discount_type'] ?? null,
            'purchase_discount_value' => removeCommaSeparator($input['purchase_discount_value'] ?? null),
            'expense_ledger_id' => $input['expense_ledger_id'] ?? null,
            'decimal_places_for_rate' => $input['decimal_places_for_rate'] ?? 2,
            'via_api' => $input['via_api'] ?? false,
        ];
    }

    public function createUpdateModel($input, $model, $itemMaster, $method = self::STORE)
    {
        try {
            $data = [
                'item_name' => $input['item_name'],
                'group_id' => $input['group_id'],
                'item_type' => $input['item_type'],
                'model_type' => get_class($model),
                'model_id' => $model->id,
                'company_id' => getCurrentCompany()->id,
                'sku' => $input['sku'] ?? null,
                'via_api' => $input['via_api'] ?? false,
                'is_import' => false,
                'is_use_in_other_companies' => $input['is_use_in_other_companies'] ?? false,
            ];

            if ($method == self::STORE) {
                $data['vastra_id'] = isset($input['vastra_id']) ? $input['vastra_id'] : null;
            } else {
                $data['vastra_id'] = $itemMaster->vastra_id;
            }

            if (! empty($itemMaster)) {
                $itemMaster = ItemMaster::find($itemMaster->id);
            } else {
                $itemMaster = ItemMaster::whereModelId($model->id)
                    ->whereModelType(get_class($model))
                    ->whereCompanyId(getCurrentCompany()->id)
                    ->first();
            }

            if (empty($itemMaster)) {
                $itemMaster = ItemMaster::create($data);

                if (isset($input['sku']) && ! empty($input['sku'])) {
                    $generator = new BarcodeGeneratorPNG();
                    $barcodeData = $generator->getBarcode($input['sku'], $generator::TYPE_CODE_128);

                    if (! empty($barcodeData)) {
                        $barcodeBase64 = base64_encode($barcodeData);
                        $itemMaster->addMediaFromBase64('data:image/png;base64,'.$barcodeBase64)
                            ->toMediaCollection(ItemMaster::BARCODE, config('app.media_disc'));
                    }
                }

                // Add item image
                if (isset($input['item_image']) && ! empty($input['item_image']) && $method == self::STORE) {
                    $itemMaster->addMedia($input['item_image'])->toMediaCollection(ItemMaster::ITEM_IMAGE, config('app.media_disc'));
                }
            } else {
                $oldSKU = $itemMaster->sku;
                $itemMaster->update($data);

                $media = $itemMaster->getMedia(ItemMaster::BARCODE)->first();
                if ((isset($input['sku']) && ! empty($input['sku']) && $oldSKU != $input['sku']) ||
                    (empty($media) && isset($input['sku']) && ! empty($input['sku']))) {
                    $itemMaster->clearMediaCollection(ItemMaster::BARCODE);
                    $generator = new BarcodeGeneratorPNG();
                    $barcodeData = $generator->getBarcode($input['sku'], $generator::TYPE_CODE_128);

                    if (! empty($barcodeData)) {
                        $barcodeBase64 = base64_encode($barcodeData);
                        $itemMaster->addMediaFromBase64('data:image/png;base64,'.$barcodeBase64)
                            ->toMediaCollection(ItemMaster::BARCODE, config('app.media_disc'));
                    }
                }

                // update item image
                if (isset($input['item_image']) && ! empty($input['item_image'])) {
                    if ($itemMaster->hasMedia(ItemMaster::ITEM_IMAGE)) {
                        $itemMaster->clearMediaCollection(ItemMaster::ITEM_IMAGE);
                    }
                    $itemMaster->addMedia($input['item_image'])->toMediaCollection(ItemMaster::ITEM_IMAGE, config('app.media_disc'));
                }
            }

            if (get_class($model) == ItemMasterGoods::class) {
                $company = getCurrentCompany();
                $getCurrentFinancialYearDetails = getCurrentFinancialYearDetails();
                $startYear = $getCurrentFinancialYearDetails['startYear'];
                $endYear = $getCurrentFinancialYearDetails['endYear'];
                $financialYearItemOpeningBalance = FinancialYearItemOpeningBalance::whereStartYear($startYear)
                    ->whereEndYear($endYear)->whereItemId($itemMaster->id)->first();
                if (empty($financialYearItemOpeningBalance)) {
                    $financialYearItemOpeningBalance = new FinancialYearItemOpeningBalance();
                }

                $financialYearItemOpeningBalance->start_year = $startYear;
                $financialYearItemOpeningBalance->end_year = $endYear;
                $financialYearItemOpeningBalance->company_id = $company->id;
                $financialYearItemOpeningBalance->item_id = $itemMaster->id;
                $financialYearItemOpeningBalance->opening_balance_type = $input['opening_balance_type'] ?? 2;
                $financialYearItemOpeningBalance->opening_balance_qty = $input['quantity'] ?? 0;
                $financialYearItemOpeningBalance->opening_balance_rate = $input['rate'] ?? 0;
                $financialYearItemOpeningBalance->save();
            }

            $itemMaster->load('model');

            return $itemMaster;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
}
