<?php

namespace App\Actions\v1\Transactions\IncomeEstimateQuote;

use App\Actions\CommonAction\GetTcsTdsDetailsAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Actions\CustomFieldsItemMaster\Transaction\GetCFTransactionWiseAction;
use App\Models\AdditionalChargesForIncomeEstimateQuoteTransaction;
use App\Models\AddLessForIncomeEstimateQuoteTransaction;
use App\Models\TransactionCustomField;
use App\Models\ItemCustomField;
use App\Models\IncomeEstimateQuoteAccountingInvoice;
use App\Models\IncomeEstimateQuoteItemInvoice;
use App\Models\IncomeEstimateQuoteTransaction;
use App\Models\UnitOfMeasurement;
use Lorisleiva\Actions\Concerns\AsAction;

class GetEstimateQuoteTransactionAction
{
    use AsAction;

    public function handle($id, $isEdit = true)
    {
        $query = IncomeEstimateQuoteTransaction::with([
            'party',
            'brokerDetails',
            'transportDetails',
            'transactionItems',
            'transactionLedgers',
            'dispatchAddress',
            'billingAddress',
            'shippingAddress',
            'additionalCharges.ledger',
            'addLess.ledger',
        ]);

        $estimateTransaction = $isEdit ? $query->financialYearDate()->findOrFail($id) : $query->findOrFail($id);
        $estimateTransaction->customer_ledger_name = $estimateTransaction->party->name ?? null;
        $estimateTransaction->media = $estimateTransaction->getIncomeEstimateQuoteFileAttribute();

        if ($estimateTransaction->tcs_tax_id) {
            $estimateTransaction->tcs_calculated = GetTcsTdsDetailsAction::run($estimateTransaction->tcs_tax_id, $estimateTransaction->party_ledger_id);
        }
        if ($estimateTransaction->tds_tax_id) {
            $estimateTransaction->tds_calculated = GetTcsTdsDetailsAction::run($estimateTransaction->tds_tax_id, $estimateTransaction->party_ledger_id);
            $estimateTransaction->tds_rounding_method = $estimateTransaction->tds_calculated['rounding_method'] ?? null;
        }

        if (count($estimateTransaction->customFieldValues) > 0) {
            $estimateTransaction->custom_values = GetTransactionCustomFieldsAction::run(TransactionCustomField::INCOME_ESTIMATE_QUOTE, $estimateTransaction->id, IncomeEstimateQuoteTransaction::class);
        }

        foreach ($estimateTransaction->transactionItems as $item) {

            /** @var IncomeEstimateQuoteItemInvoice $item */
            $item->item_name = $item->items->item_name ?? null;
            $item->ledger_name = $item->ledgers->name ?? null;

            /* This is for custom fields if transaction item has custom fields values */
            if (count($item->customFieldTransactionItemsValues) > 0) {
                $item->custom_items_values = GetCFTransactionWiseAction::run(ItemCustomField::INCOME_ESTIMATE_QUOTE, $item->id, IncomeEstimateQuoteItemInvoice::class, $item->item_id);
            }

            /* This is for all custom fields */
            $item->custom_fields = GetAllCustomFieldsItemTransactionWise::run(ItemCustomField::INCOME_ESTIMATE_QUOTE, $item->id, IncomeEstimateQuoteItemInvoice::class, $item->item_id);

            if (is_null($item->items) || is_null($item->items->model)) {
                $item->unitOfArray = [];

                continue;
            }

            $unitIds = array_filter([
                $item->unit_id,
                $item->items->model->unit_of_measurement,
                $item->items->model->secondary_unit_of_measurement ?? null,
            ]);

            // TO_COMMENT =>
            $item->unitOfArray = UnitOfMeasurement::whereIn('id', $unitIds)->pluck('name', 'id')->toArray();
            $item->units_of_array = UnitOfMeasurement::whereIn('id', $unitIds)->get()->toArray();

            $item->decimal_places = ! empty($item->decimal_places_for_quantity) ? $item->decimal_places_for_quantity : ($item->items->model->decimal_places ?? 2);
            $item->item_decimal_places = $item->items->model->decimal_places ?? 2;
            $item->decimal_places_for_rate = ! empty($item->decimal_places_for_rate) ? $item->decimal_places_for_rate : ($item->items->model->decimal_places_for_rate ?? 2);
            $item->item_decimal_places_for_rate = $item->items->model->decimal_places_for_rate ?? 2;
            $item->secondary_unit_of_measurement = $item->items->model->secondary_unit_of_measurement ?? null;
            $item->conversion_rate = $item->items->model->conversion_rate ?? null;
            $item->is_re_order = $item->items->model->is_re_order ?? false;
            unset($item->items);
            unset($item->ledgers);
        }

        foreach ($estimateTransaction->transactionLedgers as $ledger) {
            /** @var IncomeEstimateQuoteAccountingInvoice $ledger */
            $ledger->ledger_name = $ledger->ledgers->name ?? null;
            unset($ledger->ledger);
        }

        foreach ($estimateTransaction->additionalCharges as $charge) {
            /** @var AdditionalChargesForIncomeEstimateQuoteTransaction $charge */
            $charge->ledger_name = $charge->ledger->name ?? null;
            unset($charge->ledger);
        }

        foreach ($estimateTransaction->addLess as $less) {
            /** @var AddLessForIncomeEstimateQuoteTransaction $less */
            $less->ledger_name = $less->ledger->name ?? null;
            unset($less->ledger);
        }

        return $estimateTransaction;
    }
}
