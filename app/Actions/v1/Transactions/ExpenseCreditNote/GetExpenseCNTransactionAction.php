<?php

namespace App\Actions\v1\Transactions\ExpenseCreditNote;

use App\Actions\CommonAction\GetTcsTdsDetailsAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Actions\CustomFieldsItemMaster\Transaction\GetCFTransactionWiseAction;
use App\Models\AdditionalChargesForExpenseCreditNoteTransaction;
use App\Models\AddLessForExpenseCreditNoteTransaction;
use App\Models\TransactionCustomField;
use App\Models\ItemCustomField;
use App\Models\ExpenseCreditNoteItemTransaction;
use App\Models\ExpenseCreditNoteLedgerTransaction;
use App\Models\ExpenseCreditNoteTransaction;
use App\Models\PaymentDetailsForExpenseCreditNoteTransaction;
use App\Models\UnitOfMeasurement;
use Lorisleiva\Actions\Concerns\AsAction;

class GetExpenseCNTransactionAction
{
    use AsAction;

    public function handle($id)
    {
        $expenseCNTransaction = ExpenseCreditNoteTransaction::with([
            'supplier',
            'billingAddress',
            'dispatchAddress',
            'expenseCreditNoteItems',
            'expenseCreditNoteLedgers',
            'additionalCharges.ledger',
            'addLess.ledger',
            'paymentDetails.ledger',
        ])->financialYearDate()->findOrFail($id);

        $expenseCNTransaction->supplier_ledger_name = $expenseCNTransaction->supplier->name ?? null;
        $expenseCNTransaction->media = $expenseCNTransaction->getExpenseCreditNoteFileAttribute();

        if ($expenseCNTransaction->tcs_tax_id) {
            $expenseCNTransaction->tcs_calculated = GetTcsTdsDetailsAction::run($expenseCNTransaction->tcs_tax_id, $expenseCNTransaction->supplier_id);
        }
        if ($expenseCNTransaction->ledger_of_tds) {
            $expenseCNTransaction->tds_calculated = GetTcsTdsDetailsAction::run($expenseCNTransaction->ledger_of_tds, $expenseCNTransaction->supplier_id);
        }

        if (count($expenseCNTransaction->customFieldValues) > 0) {
            $expenseCNTransaction->custom_values = GetTransactionCustomFieldsAction::run(TransactionCustomField::EXPENSE_CREDIT_NOTE, $expenseCNTransaction->id, ExpenseCreditNoteTransaction::class);
        }

        foreach ($expenseCNTransaction->expenseCreditNoteItems as $item) {
            /** @var ExpenseCreditNoteItemTransaction $item */
            $item->item_name = $item->items->item_name ?? null;
            $item->ledger_name = $item->ledger->name ?? null;

            /* This is for custom fields if transaction item has custom fields values */
            if (count($item->customFieldTransactionItemsValues) > 0) {
                $item->custom_items_values = GetCFTransactionWiseAction::run(ItemCustomField::EXPENSE_CREDIT_NOTE, $item->id, ExpenseCreditNoteItemTransaction::class, $item->item_id);
            }

            /* This is for all custom fields */
            $item->custom_fields = GetAllCustomFieldsItemTransactionWise::run(ItemCustomField::EXPENSE_CREDIT_NOTE, $item->id, ExpenseCreditNoteItemTransaction::class, $item->item_id);

            if (is_null($item->items) || is_null($item->items->model)) {
                $item->unitOfArray = [];

                continue;
            }

            $unitIds = array_filter([
                $item->unit_id,
                $item->items->model->unit_of_measurement,
                $item->items->model->secondary_unit_of_measurement ?? null,
            ]);

            // TO_COMMENT =>
            $item->unitOfArray = UnitOfMeasurement::whereIn('id', $unitIds)->pluck('name', 'id')->toArray();
            $item->units_of_array = UnitOfMeasurement::whereIn('id', $unitIds)->get()->toArray();

            $item->decimal_places = ! empty($item->decimal_places_for_quantity) ? $item->decimal_places_for_quantity : ($item->items->model->decimal_places ?? 2);
            $item->item_decimal_places = $item->items->model->decimal_places ?? 2;
            $item->decimal_places_for_rate = ! empty($item->decimal_places_for_rate) ? $item->decimal_places_for_rate : ($item->items->model->decimal_places_for_rate ?? 2);
            $item->item_decimal_places_for_rate = $item->items->model->decimal_places_for_rate ?? 2;
            $item->secondary_unit_of_measurement = $item->items->model->secondary_unit_of_measurement ?? null;
            $item->conversion_rate = $item->items->model->conversion_rate ?? null;
            $item->is_re_order = $item->items->model->is_re_order ?? false;
            unset($item->items);
            unset($item->ledger);
        }

        foreach ($expenseCNTransaction->expenseCreditNoteLedgers as $ledger) {
            /** @var ExpenseCreditNoteLedgerTransaction $ledger */
            $ledger->ledger_name = $ledger->ledger->name ?? null;
            unset($ledger->ledger);
        }

        foreach ($expenseCNTransaction->additionalCharges as $charge) {
            /** @var AdditionalChargesForExpenseCreditNoteTransaction $charge */
            $charge->ledger_name = $charge->ledger->name ?? null;
            unset($charge->ledger);
        }

        foreach ($expenseCNTransaction->addLess as $less) {
            /** @var AddLessForExpenseCreditNoteTransaction $less */
            $less->ledger_name = $less->ledger->name ?? null;
            unset($less->ledger);
        }

        foreach ($expenseCNTransaction->paymentDetails as $payment) {
            /** @var PaymentDetailsForExpenseCreditNoteTransaction $payment */
            $payment->ledger_name = $payment->ledger->name ?? null;
            unset($payment->ledger);
        }

        return $expenseCNTransaction;
    }
}
