<?php

namespace App\Actions\v1\Transactions\IncomeCreditNote;

use App\Actions\CommonAction\GetTcsTdsDetailsAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Actions\CustomFieldsItemMaster\Transaction\GetCFTransactionWiseAction;
use App\Models\AdditionalChargesForIncomeCreditNoteTransaction;
use App\Models\AddLessForIncomeCreditNoteTransaction;
use App\Models\TransactionCustomField;
use App\Models\ItemCustomField;
use App\Models\IncomeCreditNoteItemTransaction;
use App\Models\IncomeCreditNoteLedgerTransaction;
use App\Models\IncomeCreditNoteTransaction;
use App\Models\PaymentDetailsForIncomeCreditNoteTransaction;
use App\Models\UnitOfMeasurement;
use Lorisleiva\Actions\Concerns\AsAction;

class GetIncomeCNTransactionAction
{
    use AsAction;

    public function handle($id)
    {
        $incomeCNTransaction = IncomeCreditNoteTransaction::with([
            'customer',
            'dispatchAddress',
            'billingAddress',
            'shippingAddress',
            'incomeCreditNoteItems',
            'incomeCreditNoteLedgers',
            'additionalCharges.ledger',
            'addLess.ledger',
            'paymentDetails.ledger',
        ])->financialYearDate()->findOrFail($id);

        $incomeCNTransaction->customer_ledger_name = $incomeCNTransaction->customer->name ?? null;
        $incomeCNTransaction->media = $incomeCNTransaction->getIncomeCreditNoteFileAttribute();

        if ($incomeCNTransaction->tcs_tax_id) {
            $incomeCNTransaction->tcs_calculated = GetTcsTdsDetailsAction::run($incomeCNTransaction->tcs_tax_id, $incomeCNTransaction->customer_ledger_id);
        }
        if ($incomeCNTransaction->tds_tax_id) {
            $incomeCNTransaction->tds_calculated = GetTcsTdsDetailsAction::run($incomeCNTransaction->tds_tax_id, $incomeCNTransaction->customer_ledger_id);
            $incomeCNTransaction->tds_rounding_method = $incomeCNTransaction->tds_calculated['rounding_method'] ?? null;
        }

        if (count($incomeCNTransaction->customFieldValues) > 0) {
            $incomeCNTransaction->custom_values = GetTransactionCustomFieldsAction::run(TransactionCustomField::INCOME_CREDIT_NOTE, $incomeCNTransaction->id, IncomeCreditNoteTransaction::class);
        }

        foreach ($incomeCNTransaction->incomeCreditNoteItems as $item) {

            /** @var IncomeCreditNoteItemTransaction $item */
            $item->item_name = $item->items->item_name ?? null;
            $item->ledger_name = $item->ledger->name ?? null;

            /* This is for custom fields if transaction item has custom fields values */
            if (count($item->customFieldTransactionItemsValues) > 0) {
                $item->custom_items_values = GetCFTransactionWiseAction::run(ItemCustomField::INCOME_CREDIT_NOTE, $item->id, IncomeCreditNoteItemTransaction::class, $item->item_id);
            }

            /* This is for all custom fields */
            $item->custom_fields = GetAllCustomFieldsItemTransactionWise::run(ItemCustomField::INCOME_CREDIT_NOTE, $item->id, IncomeCreditNoteItemTransaction::class, $item->item_id);

            if (is_null($item->items) || is_null($item->items->model)) {
                $item->unitOfArray = [];

                continue;
            }

            $unitIds = array_filter([
                $item->unit_id,
                $item->items->model->unit_of_measurement,
                $item->items->model->secondary_unit_of_measurement ?? null,
            ]);

            // TO_COMMENT =>
            $item->unitOfArray = UnitOfMeasurement::whereIn('id', $unitIds)->pluck('name', 'id')->toArray();
            $item->units_of_array = UnitOfMeasurement::whereIn('id', $unitIds)->get()->toArray();

            $item->decimal_places = ! empty($item->decimal_places_for_quantity) ? $item->decimal_places_for_quantity : ($item->items->model->decimal_places ?? 2);
            $item->item_decimal_places = $item->items->model->decimal_places ?? 2;
            $item->decimal_places_for_rate = ! empty($item->decimal_places_for_rate) ? $item->decimal_places_for_rate : ($item->items->model->decimal_places_for_rate ?? 2);
            $item->item_decimal_places_for_rate = $item->items->model->decimal_places_for_rate ?? 2;
            $item->secondary_unit_of_measurement = $item->items->model->secondary_unit_of_measurement ?? null;
            $item->conversion_rate = $item->items->model->conversion_rate ?? null;
            $item->is_re_order = $item->items->model->is_re_order ?? false;
            unset($item->items);
            unset($item->ledger);
        }

        foreach ($incomeCNTransaction->incomeCreditNoteLedgers as $ledger) {
            /** @var IncomeCreditNoteLedgerTransaction $ledger */
            $ledger->ledger_name = $ledger->ledger->name ?? null;
            unset($ledger->ledger);
        }

        foreach ($incomeCNTransaction->additionalCharges as $charge) {
            /** @var AdditionalChargesForIncomeCreditNoteTransaction $charge */
            $charge->ledger_name = $charge->ledger->name ?? null;
            unset($charge->ledger);
        }

        foreach ($incomeCNTransaction->addLess as $less) {
            /** @var AddLessForIncomeCreditNoteTransaction $less */
            $less->ledger_name = $less->ledger->name ?? null;
            unset($less->ledger);
        }

        foreach ($incomeCNTransaction->paymentDetails as $payment) {
            /** @var PaymentDetailsForIncomeCreditNoteTransaction $payment */
            $payment->ledger_name = $payment->ledger->name ?? null;
            unset($payment->ledger);
        }

        return $incomeCNTransaction;
    }
}
