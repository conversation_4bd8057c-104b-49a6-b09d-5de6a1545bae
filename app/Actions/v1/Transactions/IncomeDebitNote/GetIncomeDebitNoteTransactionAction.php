<?php

namespace App\Actions\v1\Transactions\IncomeDebitNote;

use App\Actions\CommonAction\GetTcsTdsDetailsAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Actions\CustomFieldsItemMaster\Transaction\GetCFTransactionWiseAction;
use App\Models\AdditionalChargesForIncomeDebitNoteTransaction;
use App\Models\AddLessForIncomeDebitNoteTransaction;
use App\Models\TransactionCustomField;
use App\Models\ItemCustomField;
use App\Models\IncomeDebitNoteItemTransaction;
use App\Models\IncomeDebitNoteLedgerTransaction;
use App\Models\IncomeDebitNoteTransaction;
use App\Models\PaymentDetailsForIncomeDebitNoteTransaction;
use App\Models\UnitOfMeasurement;
use Lorisleiva\Actions\Concerns\AsAction;

class GetIncomeDebitNoteTransactionAction
{
    use AsAction;

    public function handle($id)
    {
        $incomeDebitNoteTransaction = IncomeDebitNoteTransaction::with([
            'customer',
            'dispatchAddress',
            'billingAddress',
            'shippingAddress',
            'incomeDebitNoteItems',
            'incomeDebitNoteLedgers',
            'additionalCharges.ledger',
            'addLess.ledger',
            'paymentDetails.ledger',
        ])->financialYearDate()->findOrFail($id);

        $incomeDebitNoteTransaction->customer_ledger_name = $incomeDebitNoteTransaction->customer->name ?? null;
        $incomeDebitNoteTransaction->media = $incomeDebitNoteTransaction->getIncomeDebitNoteFileAttribute();

        if ($incomeDebitNoteTransaction->tcs_tax_id) {
            $incomeDebitNoteTransaction->tcs_calculated = GetTcsTdsDetailsAction::run($incomeDebitNoteTransaction->tcs_tax_id, $incomeDebitNoteTransaction->customer_ledger_id);
        }
        if ($incomeDebitNoteTransaction->tds_tax_id) {
            $incomeDebitNoteTransaction->tds_calculated = GetTcsTdsDetailsAction::run($incomeDebitNoteTransaction->tds_tax_id, $incomeDebitNoteTransaction->customer_ledger_id);
            $incomeDebitNoteTransaction->tds_rounding_method = $incomeDebitNoteTransaction->tds_calculated['rounding_method'] ?? null;
        }

        if (count($incomeDebitNoteTransaction->customFieldValues) > 0) {
            $incomeDebitNoteTransaction->custom_values = GetTransactionCustomFieldsAction::run(TransactionCustomField::INCOME_DEBIT_NOTE, $incomeDebitNoteTransaction->id, IncomeDebitNoteTransaction::class);
        }

        foreach ($incomeDebitNoteTransaction->incomeDebitNoteItems as $item) {

            /** @var IncomeDebitNoteItemTransaction $item */
            $item->item_name = $item->items->item_name ?? null;
            $item->ledger_name = $item->ledger->name ?? null;

            /* This is for custom fields if transaction item has custom fields values */
            if (count($item->customFieldTransactionItemsValues) > 0) {
                $item->custom_items_values = GetCFTransactionWiseAction::run(ItemCustomField::INCOME_DEBIT_NOTE, $item->id, IncomeDebitNoteItemTransaction::class, $item->item_id);
            }

            /* This is for all custom fields */
            $item->custom_fields = GetAllCustomFieldsItemTransactionWise::run(ItemCustomField::INCOME_DEBIT_NOTE, $item->id, IncomeDebitNoteItemTransaction::class, $item->item_id);

            if (is_null($item->items) || is_null($item->items->model)) {
                $item->unitOfArray = [];

                continue;
            }

            $unitIds = array_filter([
                $item->unit_id,
                $item->items->model->unit_of_measurement,
                $item->items->model->secondary_unit_of_measurement ?? null,
            ]);

            // TO_COMMENT =>
            $item->unitOfArray = UnitOfMeasurement::whereIn('id', $unitIds)->pluck('name', 'id')->toArray();
            $item->units_of_array = UnitOfMeasurement::whereIn('id', $unitIds)->get()->toArray();

            $item->decimal_places = ! empty($item->decimal_places_for_quantity) ? $item->decimal_places_for_quantity : ($item->items->model->decimal_places ?? 2);
            $item->item_decimal_places = $item->items->model->decimal_places ?? 2;
            $item->decimal_places_for_rate = ! empty($item->decimal_places_for_rate) ? $item->decimal_places_for_rate : ($item->items->model->decimal_places_for_rate ?? 2);
            $item->item_decimal_places_for_rate = $item->items->model->decimal_places_for_rate ?? 2;
            $item->secondary_unit_of_measurement = $item->items->model->secondary_unit_of_measurement ?? null;
            $item->conversion_rate = $item->items->model->conversion_rate ?? null;
            $item->is_re_order = $item->items->model->is_re_order ?? false;
            unset($item->items);
            unset($item->ledger);
        }

        foreach ($incomeDebitNoteTransaction->incomeDebitNoteLedgers as $ledger) {
            /** @var IncomeDebitNoteLedgerTransaction $ledger */
            $ledger->ledger_name = $ledger->ledger->name ?? null;
            unset($ledger->ledger);
        }

        foreach ($incomeDebitNoteTransaction->additionalCharges as $charge) {
            /** @var AdditionalChargesForIncomeDebitNoteTransaction $charge */
            $charge->ledger_name = $charge->ledger->name ?? null;
            unset($charge->ledger);
        }

        foreach ($incomeDebitNoteTransaction->addLess as $less) {
            /** @var AddLessForIncomeDebitNoteTransaction $less */
            $less->ledger_name = $less->ledger->name ?? null;
            unset($less->ledger);
        }

        foreach ($incomeDebitNoteTransaction->paymentDetails as $payment) {
            /** @var PaymentDetailsForIncomeDebitNoteTransaction $payment */
            $payment->ledger_name = $payment->ledger->name ?? null;
            unset($payment->ledger);
        }

        return $incomeDebitNoteTransaction;
    }
}
