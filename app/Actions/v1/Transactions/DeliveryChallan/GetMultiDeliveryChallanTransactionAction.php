<?php

namespace App\Actions\v1\Transactions\DeliveryChallan;

use App\Actions\CommonAction\GetTcsTdsDetailsAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\GetAllCustomFieldsItemTransactionWise;
use App\Actions\CustomFieldsItemMaster\Transaction\GetCFTransactionWiseAction;
use App\Models\AdditionalChargesForDeliveryChallanTransaction;
use App\Models\AddLessForDeliveryChallanTransaction;
use App\Models\ItemCustomField;
use App\Models\DeliveryChallanTransaction;
use App\Models\DeliveryChallanTransactionItem;
use App\Models\SaleTransactionItem;
use App\Models\TransactionCustomField;
use App\Models\UnitOfMeasurement;
use Illuminate\Support\Arr;
use Lorisleiva\Actions\Concerns\AsAction;

class GetMultiDeliveryChallanTransactionAction
{
    use AsAction;

    public $deliveryChallanTransactions = null;

    public $data = [];

    public function handle($input)
    {
        $deliveryChallanIds = $input['ids'] ?? [];
        $invoiceType = $input['invoiceType'] ?? null;
        $invoiceNumber = $input['invoiceNumber'] ?? null;
        $transactionId = $input['transactionId'] ?? null;

        $this->deliveryChallanTransactions = DeliveryChallanTransaction::with([
            'party',
            'transactionItems.items.model.gstTax',
            'transactionItems.items.model.gstGstCessRate',
            'transactionItems.classificationNatureType',
            'billingAddress',
            'shippingAddress',
            'additionalCharges',
            'addLess',
        ])
            ->whereIn('id', $deliveryChallanIds)
            ->get();

        if (! empty($invoiceNumber)) {
            $deliveryChallanTransaction = $this->deliveryChallanTransactions->where('id', $invoiceNumber)->first();
        } else {
            $deliveryChallanTransaction = $this->deliveryChallanTransactions->first() ?? [];
        }

        if (isset($deliveryChallanTransaction->tcs_tax_id) && $deliveryChallanTransaction->tcs_tax_id) {
            $deliveryChallanTransaction->tcs_calculated = GetTcsTdsDetailsAction::run($deliveryChallanTransaction->tcs_tax_id, $deliveryChallanTransaction->party_ledger_id);
        }
        if (isset($deliveryChallanTransaction->tds_tax_id) && $deliveryChallanTransaction->tds_tax_id) {
            $deliveryChallanTransaction->tds_calculated = GetTcsTdsDetailsAction::run($deliveryChallanTransaction->tds_tax_id, $deliveryChallanTransaction->party_ledger_id);
        }

        if (count($deliveryChallanTransaction->customFieldValues) > 0) {
            $deliveryChallanTransaction->custom_values = GetTransactionCustomFieldsAction::run(TransactionCustomField::DELIVERY_CHALLAN, $deliveryChallanTransaction->id, DeliveryChallanTransaction::class);
        }

        $this->data['isShowInvoiceTypeModal'] = false;
        $this->data['isShowInvoiceNumberModal'] = false;
        $this->data['invoiceTypeModalList'] = [];
        $this->data['invoiceNumberModalList'] = [];
        $this->data['deliveryChallanIds'] = $this->deliveryChallanTransactions->pluck('id'); // Ids for Invoice Type Modal
        $this->data['invoiceType'] = $invoiceType;
        $this->data['matchedInputName'] = '';

        if (count($deliveryChallanIds) > 1 && $this->data['isShowInvoiceTypeModal'] != true && $invoiceNumber == null) {
            $keysToCompare = [
                'billing_address',
                'shipping_address',
                'broker_id',
                'brokerage',
                'brokerage_on_value_type',
                'credit_period',
                'credit_period_type',
                'transport_id',
                'transporter_document_number',
                'transporter_document_date',
                'po_no',
                'po_date',
                'transaction_items',
            ];

            $deliveryChallan = $this->deliveryChallanTransactions->toArray();

            unset($deliveryChallan[0]['sale_transactions']);
            unset($deliveryChallan[0]['transaction_items']);

            $count = count($deliveryChallan);

            for ($i = 0; $i < $count; $i++) {
                $this->data['invoiceNumberModalList'][$deliveryChallan[$i]['id']] = $deliveryChallan[$i]['challan_number']; // Show Invoices in Modal
                if ($count > 1) {
                    for ($j = $i + 1; $j < $count; $j++) {
                        $isSimilar = true;
                        $firstTransaction = Arr::only($deliveryChallan[$i], $keysToCompare);
                        $restDCTransaction = Arr::only($deliveryChallan[$j], $keysToCompare);

                        foreach ($firstTransaction as $key => $value) {
                            if (empty($firstTransaction[$key]) || empty($restDCTransaction[$key])) {
                                $isSimilar = false;
                                $this->data['matchedInputName'] = $this->formatFieldString($key);
                                break;
                            }
                            if ($firstTransaction[$key] != $restDCTransaction[$key] && ! in_array($key, ['billing_address', 'shipping_address', 'transaction_items', 'transaction_ledgers'])) {
                                $isSimilar = false;
                                $this->data['matchedInputName'] = $this->formatFieldString($key);
                                break;
                            }

                            // Compare Billing Address
                            foreach ($firstTransaction['billing_address'] as $field => $value) {
                                if ($firstTransaction['billing_address'][$field] != $restDCTransaction['billing_address'][$field] && ! in_array($field, ['id', 'model_id', 'model_type', 'address_type', 'created_at', 'updated_at'])) {
                                    $isSimilar = false;
                                    $this->data['matchedInputName'] = $this->formatFieldString($field);
                                    break;
                                }
                            }

                            // Compare Shipping Address
                            if (! empty($firstTransaction['shipping_address']) && ! empty($restDCTransaction['shipping_address'])) {
                                foreach ($firstTransaction['shipping_address'] as $field => $value) {
                                    if ($firstTransaction['shipping_address'][$field] != $restDCTransaction['shipping_address'][$field] && ! in_array($field, ['id', 'model_id', 'model_type', 'address_type', 'created_at', 'updated_at'])) {
                                        $isSimilar = false;
                                        $this->data['matchedInputName'] = $this->formatFieldString($field);
                                        break;
                                    }
                                }
                            }
                        }
                        $key = 1; // old code it's not used currently used app/Actions/v1/Transactions/DeliveryChallan/GetMultiDeliveryChallanTransactionAction.php
                        if (isset($firstTransaction['transaction_ledgers']) && count($restDCTransaction['transaction_ledgers']) > 0) {  // Transaction Ledgers Classification compare
                            if ($firstTransaction['transaction_ledgers'][0]['classification_nature_type']['name'] != $restDCTransaction['transaction_ledgers'][0]['classification_nature_type']['name']) {
                                $isSimilar = false;
                                $this->data['matchedInputName'] = $this->formatFieldString($key);
                            }
                        }

                        if (! $isSimilar) {
                            $this->data['isShowInvoiceNumberModal'] = true;
                        }
                    }
                }
            }
        }

        if (! empty($deliveryChallanTransaction)) {
            $deliveryChallanTransaction['billingAddress'] = $deliveryChallanTransaction->billingAddress ?? null;
            $deliveryChallanTransaction['shippingAddress'] = $deliveryChallanTransaction->shippingAddress ?? null;
            $deliveryChallanTransaction['isRcmApplicable'] = 0;
        }

        $deliveryChallanTransaction['customer_ledger_name'] = $deliveryChallanTransaction['party']['name'] ?? null;
        $this->data['deliveryChallanTransaction'] = $deliveryChallanTransaction;
        $this->data['totalCessRate'] = 0;
        $transactionItems = [];

        foreach ($this->deliveryChallanTransactions as $key => $transaction) {
            $this->data['totalCessRate'] += $transaction['cess'];
            foreach ($transaction->transactionItems as $item) {
                /** @var DeliveryChallanTransactionItem $item */
                $item->item_name = $item->items->item_name ?? null;

                /* This is for custom fields if transaction item has custom fields values */
                if (count($item->customFieldTransactionItemsValues) > 0) {
                    $item->custom_items_values = GetCFTransactionWiseAction::run(ItemCustomField::DELIVERY_CHALLAN, $item->id, DeliveryChallanTransactionItem::class, $item->item_id);
                }

                /* This is for all custom fields */
                $item->custom_values = GetAllCustomFieldsItemTransactionWise::run(ItemCustomField::DELIVERY_CHALLAN, $item->id, DeliveryChallanTransactionItem::class, $item->item_id);


                if (is_null($item->items) || is_null($item->items->model)) {
                    $item->unitOfArray = [];

                    continue;
                }
                $unitIds = array_filter([
                    $item->items->model->unit_of_measurement,
                    $item->items->model->secondary_unit_of_measurement ?? null,
                ]);

                $item->unitOfArray = UnitOfMeasurement::whereIn('id', $unitIds)->pluck('name', 'id')->toArray();

                $item->decimal_places = ! empty($item->decimal_places_for_quantity) ? $item->decimal_places_for_quantity : ($item->items->model->decimal_places ?? 2);
                $item->item_decimal_places = $item->items->model->decimal_places ?? 2;
                $item->decimal_places_for_rate = ! empty($item->decimal_places_for_rate) ? $item->decimal_places_for_rate : ($item->items->model->decimal_places_for_rate ?? 2);
                $item->item_decimal_places_for_rate = $item->items->model->decimal_places_for_rate ?? 2;
                $item->secondary_unit_of_measurement = $item->items->model->secondary_unit_of_measurement ?? null;
                $item->conversion_rate = $item->items->model->conversion_rate ?? null;
                $item->is_re_order = $item->items->model->is_re_order ?? false;

                $item->gst_tax_id = $item->items->model->gst_tax_id ?? null;
                $item->gst_rate = $item->items->model->gstTax->tax_rate ?? null;

                $transactionItems[] = $item;
            }

            foreach ($transaction->additionalCharges as $charge) {
                /** @var AdditionalChargesForDeliveryChallanTransaction $charge */
                $charge->ledger_name = $charge->ledger->name ?? null;
                unset($charge->ledger);
            }

            foreach ($transaction->addLess as $less) {
                /** @var AddLessForDeliveryChallanTransaction $less */
                $less->ledger_name = $less->ledger->name ?? null;
                unset($less->ledger);
            }
        }

        // Remaining Items qty calculate
        $transactionItems = collect($transactionItems)->filter(function ($item) use ($transactionId) {
            $deliveryChallanId = $item->transaction_id;
            $saleItemQty = SaleTransactionItem::whereHas('saleTransaction', function ($query) use ($deliveryChallanId) {
                $query->whereRaw('FIND_IN_SET(?, delivery_challan_no)', [$deliveryChallanId])->whereNull('deleted_at');
            })
                ->when(! empty($transactionId), function ($query) use ($transactionId) {
                    $query->where('sale_transactions_id', '!=', $transactionId);
                })
                ->where('item_id', $item->item_id)
                ->sum('quantity');

            if (! empty($saleItemQty)) {
                $qty = max(0, $item->quantity - $saleItemQty);
                $item->quantity = round($qty, 2);
            }

            return $item->quantity > 0;
        })->values();

        $itemCollectData = [];
        $itemCollect = collect($transactionItems)->groupBy('item_id');
        foreach ($itemCollect as $key => $itemGroup) {
            if (count($itemGroup) > 1) {
                $combinedItems = $itemGroup->groupBy(function ($item) {
                    return $item['ledger_id'].'-'.$item['unit_id'].'-'.$item['mrp'].'-'.$item['rpu_with_gst'].'-'.$item['rpu_without_gst'].'-'.$item['discount_type'].'-'.$item['discount_value'].'-'.$item['discount_type_2'].'-'.$item['discount_value_2'].'-'.$item['gst_tax_percentage'].'-'.$item['classification_is_rcm_applicable'].'-'.$item['classification_nature_type'].'-'.$item['additional_description'];
                })->map(function ($group) {
                    $collectionItem = $group->first();
                    $collectionItem->quantity = $group->sum('quantity');
                    $this->getItemTransaction($collectionItem);

                    return $collectionItem;
                });

                foreach ($combinedItems as $combinedItem) {
                    $itemCollectData[] = $combinedItem;
                }
            } else {
                $this->getItemTransaction($itemGroup->first());
                $itemCollectData[] = $itemGroup->first();
            }
        }

        $this->data['tcs_amount'] = $this->tcsCalculate($deliveryChallanTransaction, $itemCollectData);
        $this->data['allTransactionItems'] = $itemCollectData;

        return $this->data;
    }

    public function formatFieldString($string)
    {
        $string = str_replace('_', ' ', $string);
        $string = ucwords($string);

        return $string;
    }

    public function getItemTransaction($itemData)
    {
        $primaryUnit = $itemData->items->model->unit_of_measurement;
        $secondary = $itemData->items->model->secondary_unit_of_measurement;

        if ($itemData->unit_id == $primaryUnit) {
            if (isCompanyGstApplicable()) {
                $itemData->setAttribute('with_gst_amount', $itemData->items->model->selling_price_with_gst);
                $itemData->setAttribute('without_gst_amount', $itemData->items->model->selling_price_without_gst);
            } else {
                $itemData->setAttribute('without_gst_amount', $itemData->items->model->selling_price_without_gst);
            }
        } elseif ($itemData->unit_id == $secondary) {
            $withOutGstAmount = $itemData->items->model->selling_price_with_gst / $itemData->items->model->conversion_rate;
            if ($itemData->items->model->gst_cess_rate != null) {
                $withGstAmount = $withOutGstAmount * ($itemData->items->model->gst_cess_rate) / 100;
            } else {
                $withGstAmount = $withOutGstAmount;
            }

            if (isCompanyGstApplicable()) {
                $itemData->setAttribute('without_gst_amount', $withOutGstAmount);
                $itemData->setAttribute('with_gst_amount', $withGstAmount);
            } else {
                $itemData->setAttribute('without_gst_amount', $itemData->items->model->selling_price_without_gst);
            }
        } else {
            if (isCompanyGstApplicable()) {
                $itemData->setAttribute('with_gst_amount', $itemData->items->model->selling_price_with_gst);
                $itemData->setAttribute('without_gst_amount', $itemData->items->model->selling_price_without_gst);
            } else {
                $itemData->setAttribute('without_gst_amount', $itemData->items->model->selling_price_without_gst);
            }

        }

        return $itemData;
    }

    public function tcsCalculate($transaction, $itemCollectData)
    {
        $taxableValues = array_column($itemCollectData, 'total');
        $totalSubTotal = array_sum($taxableValues);

        $fixDigit = getCompanyFixedDigitNumber();

        // additional charges
        $totalSubTotal += $transaction->additionalCharges->sum('total_without_tax') ?? 0;

        $totalSubTotal += $transaction->cess;
        $totalSubTotal += $transaction->cgst;
        $totalSubTotal += $transaction->sgst;
        $totalSubTotal += $transaction->igst;

        $tcsAmount = round(($totalSubTotal * $transaction->tcs_rate) / 100, $fixDigit);

        return $tcsAmount;
    }
}
