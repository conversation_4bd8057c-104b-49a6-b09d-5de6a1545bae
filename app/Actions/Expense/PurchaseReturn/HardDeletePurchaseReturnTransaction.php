<?php

namespace App\Actions\Expense\PurchaseReturn;

use App\Models\ItemCustomFieldValue;
use App\Models\PurchaseReturnItemTransaction;
use App\Models\PurchaseReturnLedgerTransaction;
use App\Models\PurchaseReturnTransaction;
use App\Models\ReceiptTransaction;
use Lorisleiva\Actions\Concerns\AsAction;

class HardDeletePurchaseReturnTransaction
{
    use AsAction;

    public function handle($id, $allTransaction = false, $isDirectHardDelete = false)
    {
        $query = $isDirectHardDelete ? PurchaseReturnTransaction::withTrashed() : PurchaseReturnTransaction::onlyTrashed();
        if ($allTransaction) {
            $purchaseReturnTransactions = $query->get();
        } else {
            $purchaseReturnTransactions = $query->whereId($id)->get();
        }

        foreach ($purchaseReturnTransactions as $purchaseReturn) {
            ReceiptTransaction::withTrashed()
                ->where('company_id', $purchaseReturn->company_id)
                ->whereReceiptNumber('purchase-return/'.$purchaseReturn->voucher_number)->financialYearDate()?->forceDelete();

            $itemsIds = $purchaseReturn->purchaseReturnItems->pluck('id')->toArray();
            $ledgersIds = $purchaseReturn->purchaseReturnLedgers->pluck('id')->toArray();
            $allCFIds = array_merge($itemsIds, $ledgersIds);
            ItemCustomFieldValue::whereIn('model_id', $allCFIds)->whereIn('model_type', [PurchaseReturnItemTransaction::class, PurchaseReturnLedgerTransaction::class])?->delete();

            $purchaseReturn->customFieldValues()->delete();
            $purchaseReturn->addresses()->delete();
            $purchaseReturn->forceDelete();
        }

        return true;
    }
}
