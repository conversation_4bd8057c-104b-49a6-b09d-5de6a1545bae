<?php

namespace App\Actions\Expense\PurchaseOrder;

use App\Actions\CommonAction\PreparePDFItemDataAction;
use App\Actions\CustomFields\GetTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\PrepareCustomFieldDataForPdfPreview;
use App\Actions\v1\PdfConfiguration\GetPdfAdjustmentsAction;
use App\Models\Address;
use App\Models\Company;
use App\Models\CompanySetting;
use App\Models\Configuration\PurchaseOrderConfiguration;
use App\Models\TransactionCustomField;
use App\Models\InvoicesLabel;
use App\Models\ItemCustomField;
use App\Models\Master\Bank;
use App\Models\Master\PurchaseOrderTransactionMaster;
use App\Models\PrintSetting;
use App\Models\PurchaseOrderItemInvoice;
use App\Models\PurchaseOrderTransaction;
use Lorisleiva\Actions\Concerns\AsAction;

class GetInvoicePDFDataForPurchaseOrder
{
    use AsAction;

    public function handle($orderId)
    {
        $data = [];

        $data['transaction'] = PurchaseOrderTransaction::with([
            'party.model',
            'transactionItems.items.model',
            'transactionItems.unit',
            'transactionLedgers.ledgers',
            'transportDetails',
            'brokerDetails',
            'billingAddress',
            'shippingAddress',
            'transactionTitle',
            'addLess.ledger',
            'additionalCharges.ledger',
        ])->whereId($orderId)->first();

        $data['currentCompany'] = Company::with('billingAddress', 'companyTax', 'user')->whereId($data['transaction']->company_id)->first();

        session(['current_company' => $data['currentCompany']]);

        // Process Add/Less and Additional Charges
        $data['addLess'] = prepareAddLess($data['transaction']->addLess);
        $data['additionalCharges'] = prepareAdditionalCharges($data['transaction']->additionalCharges);

        // Custom fields
        $data['customFieldValues'] = GetTransactionCustomFieldsAction::run(TransactionCustomField::PURCHASE_ORDER, $orderId, PurchaseOrderTransaction::class);

        $data['fromNameExists'] = CompanySetting::where('key', 'from_name')->exists();
        $data['replyToEmailExists'] = CompanySetting::where('key', 'replay_to_email')->exists();
        $data['purchaseOrderTransactionMaster'] = PurchaseOrderTransactionMaster::whereCompanyId($data['currentCompany']->id)->first();
        $data['configuration'] = PurchaseOrderConfiguration::whereCompanyId($data['currentCompany']->id)->first();
        $data['companyBillingAddress'] = $data['currentCompany']->billingAddress;

        $data['bankDetail'] = null;
        $data['accountNumber'] = null;
        $data['accountType'] = null;
        $data['branchName'] = null;
        if (! empty($data['purchaseOrderTransactionMaster']->ledgerDetails)) {
            /** @var Bank $bankDetail */
            $bankDetail = $data['purchaseOrderTransactionMaster']->ledgerDetails->model ?? null;
            $data['bankDetail'] = $bankDetail;
            $data['accountNumber'] = $data['bankDetail'] ? $data['bankDetail']->account_number : null;
            $data['accountType'] = $data['bankDetail'] ? $data['bankDetail']->account_type : null;
            $data['branchName'] = $data['bankDetail'] ? $data['bankDetail']->branch_name : null;
        }
        $data['invoiceSetting'] = CompanySetting::pluck('value', 'key')->toArray();
        $data['transactionItems'] = PreparePDFItemDataAction::run($data['transaction']->transactionItems);
        if (! empty($data['transactionItems'])) {
            foreach ($data['transactionItems'] as $key => $item) {
                $data['transactionItems'][$key]['customItemsValues'] = PrepareCustomFieldDataForPdfPreview::run(ItemCustomField::PURCHASE_ORDER, $item->id, PurchaseOrderItemInvoice::class);
            }
        }

        $data['transactionLedgers'] = $data['transaction']->transactionLedgers;

        $data['customerDetail'] = $data['transaction']->party;
        $data['panNumber'] = $data['customerDetail']->model->pan_card_number ?? null;
        $data['showPanNumber'] = isset($data['invoiceSetting']['expense_pan_no']) ? $data['invoiceSetting']['expense_pan_no'] : true;
        $data['ledgerShippingAddress'] = $data['customerDetail']->shippingAddress ?? null;
        $data['billingAddress'] = $data['transaction']->billingAddress;
        if ($data['transaction']->same_as_billing) {
            $data['shippingAddress'] = $data['billingAddress'];
            $data['transaction']->shipping_gstin = $data['transaction']->gstin;
        } else {
            $data['shippingAddress'] = Address::whereId($data['transaction']->shipping_address_id)->first() ?? $data['transaction']->shippingAddress;
        }

        $data['itemType'] = $data['transaction']->order_type;
        $data['dueDate'] = null;
        $data['creditPeriod'] = null;
        $data['invoiceName'] = 'Purchase Order Invoice';
        $data['invoiceSetting'] = CompanySetting::pluck('value', 'key')->toArray();
        $data['changeLabel'] = InvoicesLabel::whereTransactionType(InvoicesLabel::EXPENSE_TRANSACTION)->pluck('label_value', 'label_name')->toArray();
        $data['panNumber'] = $data['customerDetail']->model->pan_card_number ?? null;
        $data['isCompanyGstApplicable'] = $data['currentCompany']->is_gst_applicable ? true : false;

        $data['showGst'] = false;

        if (! $data['isCompanyGstApplicable']) {
            if (! empty($data['customerDetail']->model->gstin)) {
                $data['showGst'] = true;
            }
        } elseif (! empty($data['transaction']->gstin)) {
            $data['showGst'] = true;
        }
        $data['defaultPrintTitle'] = $data['transaction']->transactionTitle->name;
        $data['showPanNumber'] = isset($data['invoiceSetting']['expense_pan_no']) ? $data['invoiceSetting']['expense_pan_no'] : true;
        $data['validFor'] = ! empty($data['transaction']->valid_for) && ! empty($data['transaction']->valid_for_type) ? $data['transaction']->valid_for.' '.PurchaseOrderTransaction::CREDIT_PERIOD_TYPE[$data['transaction']->valid_for_type] : null;
        $data['showPrintSettings'] = PrintSetting::pluck('status', 'name')->toArray();

        $isA5Pdf = isset($data['invoiceSetting']['expense_pdf_format']) ? $data['invoiceSetting']['expense_pdf_format'] == CompanySetting::A5 : false;
        $data['customFontSize'] = GetPdfAdjustmentsAction::run($isA5Pdf);
        $data['customProp'] = InvoicesLabel::where('is_custom_label', InvoicesLabel::PROP_NAME)->select('label_value', 'label_name')->first();

        return $data;
    }
}
