<?php

namespace App\Actions\Expense\PurchaseOrder;

use App\Models\ItemCustomFieldValue;
use App\Models\PurchaseOrderAccountingInvoice;
use App\Models\PurchaseOrderItemInvoice;
use Lorisleiva\Actions\Concerns\AsAction;

class DeletePurchaseOrder
{
    use AsAction;

    public function handle($purchaseOrderTransaction)
    {
        $itemsIds = $purchaseOrderTransaction->transactionItems->pluck('id')->toArray();
        $ledgersIds = $purchaseOrderTransaction->transactionLedgers->pluck('id')->toArray();
        $allCFIds = array_merge($itemsIds, $ledgersIds);
        ItemCustomFieldValue::whereIn('model_id', $allCFIds)->whereIn('model_type', [PurchaseOrderItemInvoice::class, PurchaseOrderAccountingInvoice::class])?->delete();

        $purchaseOrderTransaction->customFieldValues()->delete();

        $transaction = $purchaseOrderTransaction->delete();

        return $transaction;
    }
}
