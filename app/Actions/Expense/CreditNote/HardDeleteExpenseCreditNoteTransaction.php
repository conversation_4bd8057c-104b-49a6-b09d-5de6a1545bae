<?php

namespace App\Actions\Expense\CreditNote;

use App\Models\ExpenseCreditNoteItemTransaction;
use App\Models\ExpenseCreditNoteLedgerTransaction;
use App\Models\ExpenseCreditNoteTransaction;
use App\Models\ItemCustomFieldValue;
use App\Models\PaymentTransaction;
use Lorisleiva\Actions\Concerns\AsAction;

class HardDeleteExpenseCreditNoteTransaction
{
    use AsAction;

    public function handle($id, $allTransaction = false, $isDirectHardDelete = false)
    {
        $query = $isDirectHardDelete ? ExpenseCreditNoteTransaction::withTrashed() : ExpenseCreditNoteTransaction::onlyTrashed();
        if ($allTransaction) {
            $expenseCreditNoteTransactions = $query->get();
        } else {
            $expenseCreditNoteTransactions = $query->whereId($id)->get();
        }

        foreach ($expenseCreditNoteTransactions as $expenseCreditNote) {
            PaymentTransaction::withTrashed()
                ->where('company_id', $expenseCreditNote->company_id)
                ->wherePaymentVoucherNumber('expense-credit-note/'.$expenseCreditNote->voucher_number)->financialYearDate()?->forceDelete();

            $itemsIds = $expenseCreditNote->expenseCreditNoteItems->pluck('id')->toArray();
            $ledgersIds = $expenseCreditNote->expenseCreditNoteLedgers->pluck('id')->toArray();
            $allCFIds = array_merge($itemsIds, $ledgersIds);
            ItemCustomFieldValue::whereIn('model_id', $allCFIds)->whereIn('model_type', [ExpenseCreditNoteItemTransaction::class, ExpenseCreditNoteLedgerTransaction::class])?->delete();

            $expenseCreditNote->customFieldValues()->delete();
            $expenseCreditNote->addresses()->delete();
            $expenseCreditNote->forceDelete();
        }

        return true;
    }
}
