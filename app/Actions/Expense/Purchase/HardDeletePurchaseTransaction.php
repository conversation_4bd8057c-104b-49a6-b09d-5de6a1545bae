<?php

namespace App\Actions\Expense\Purchase;

use App\Models\ExpenseCreditNoteTransaction;
use App\Models\ExpenseDebitNoteTransaction;
use App\Models\ItemCustomFieldValue;
use App\Models\PaymentTransaction;
use App\Models\PurchaseItemTransaction;
use App\Models\PurchaseLedgerTransaction;
use App\Models\PurchaseReturnTransaction;
use App\Models\PurchaseTransaction;
use App\Models\ReceiptTransaction;
use Lorisleiva\Actions\Concerns\AsAction;

class HardDeletePurchaseTransaction
{
    use AsAction;

    public function handle($id, $allTransaction = false, $isDirectHardDelete = false)
    {
        $query = $isDirectHardDelete ? PurchaseTransaction::withTrashed() : PurchaseTransaction::onlyTrashed();
        if ($allTransaction) {
            $purchaseTransactions = $query->get();
        } else {
            $purchaseTransactions = $query->whereId($id)->get();
        }

        foreach ($purchaseTransactions as $purchase) {
            PaymentTransaction::withTrashed()
                ->where('company_id', $purchase->company_id)
                ->wherePaymentVoucherNumber('purchase/'.$purchase->voucher_number)->financialYearDate()?->forceDelete();

            $purchaseReturn = PurchaseReturnTransaction::withTrashed()->whereOriginalInvNo($purchase->id)->first();
            if ($purchaseReturn) {
                ReceiptTransaction::withTrashed()
                    ->where('company_id', $purchaseReturn->company_id)
                    ->whereReceiptNumber('purchase-return/'.$purchaseReturn->voucher_number)->financialYearDate()?->forceDelete();
                $purchaseReturn->addresses()->delete();
                $purchaseReturn->forceDelete();
            }

            $expenseDebitNote = ExpenseDebitNoteTransaction::withTrashed()->whereOriginalInvNo($purchase->id)->first();
            if ($expenseDebitNote) {
                ReceiptTransaction::withTrashed()
                    ->where('company_id', $expenseDebitNote->company_id)
                    ->whereReceiptNumber('expense-debit-note/'.$expenseDebitNote->voucher_number)->financialYearDate()?->forceDelete();
                $expenseDebitNote->addresses()->delete();
                $expenseDebitNote->forceDelete();
            }

            $expenseCreditNote = ExpenseCreditNoteTransaction::withTrashed()->whereOriginalInvNo($purchase->id)->first();
            if ($expenseCreditNote) {
                PaymentTransaction::withTrashed()
                    ->where('company_id', $expenseCreditNote->company_id)
                    ->wherePaymentVoucherNumber('expense-credit-note/'.$expenseCreditNote->voucher_number)->financialYearDate()?->forceDelete();
                $expenseCreditNote->addresses()->delete();
                $expenseCreditNote->forceDelete();
            }

            $itemsIds = $purchase->purchaseTransactionItems->pluck('id')->toArray();
            $ledgersIds = $purchase->purchaseTransactionLedger->pluck('id')->toArray();
            $allCFIds = array_merge($itemsIds, $ledgersIds);
            ItemCustomFieldValue::whereIn('model_id', $allCFIds)->whereIn('model_type', [PurchaseItemTransaction::class, PurchaseLedgerTransaction::class])?->delete();

            $purchase->customFieldValues()->delete();
            $purchase->addresses()->delete();
            $purchase->forceDelete();
        }

        return true;
    }
}
