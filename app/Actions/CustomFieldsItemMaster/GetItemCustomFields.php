<?php

namespace App\Actions\CustomFieldsItemMaster;

use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldDefaultValue;
use App\Models\ItemCustomFieldFormula;
use App\Models\ItemCustomFieldSetting;
use Lorisleiva\Actions\Concerns\AsAction;

class GetItemCustomFields
{
    use AsAction;

    public function handle($itemId)
    {
        $customFieldsValueList = ItemCustomFieldSetting::with('customField')->where('item_id', $itemId)->get()->map(function ($setting) use ($itemId) {

            $itemCFDefaultValue = ItemCustomFieldDefaultValue::where('item_id', $itemId)->where('custom_field_id', $setting->customField->id)->first();
            $value = $itemCFDefaultValue ? GetInputTypeWiseValue::run($itemCFDefaultValue) : null;
            $itemCFFormulas = ItemCustomFieldFormula::where('item_id', $itemId)->where('custom_field_id', $setting->customField->id)->first();

            return [
                'id' => $setting->custom_field_id,
                'custom_field_id' => $setting->custom_field_id,
                'label_name' => $setting->customField->label_name,
                'custom_field_type' => $setting->customField->custom_field_type,
                'enable_for_all' => $setting->customField->enable_for_all,
                'open_in_popup' => $setting->customField->open_in_popup,
                'status' => true,
                'local_status' => true,
                'default_value' => $value,
                'value' => $value,
                'default_formula' => $itemCFFormulas ?? null,
                'is_able_to_edit' => true,
                'select_default_value_or_formula' => $itemCFDefaultValue ? 1 : ($itemCFFormulas ? 2 : 1),
                'input_type' => ItemCustomField::CUSTOM_FIELD_ARRAY_WITH_INPUT_TYPE[$setting->customField->custom_field_type]['input_type'] ?? null,
                'eligible_for_formula' => $setting->customField->custom_field_type == ItemCustomField::CF_TYPE_NUMBER,
                'options' => $setting->customField->options ?? [],
                'option_id' => $setting->customField->custom_field_type == ItemCustomField::CF_TYPE_DROPDOWN ? ($itemCFDefaultValue?->value_select_option_id) : null,
                'field_type' => $itemCFDefaultValue?->field_type ?? null,
            ];
        });

        // $this->appendDisabledCustomFields($customFieldsValueList, $itemId);

        $this->appendQuantityFormula($customFieldsValueList, $itemId);

        return $customFieldsValueList;
    }

    private function appendDisabledCustomFields(&$customFieldsValueList, $itemId)
    {
        $allCustomFields = ItemCustomField::all();

        foreach ($allCustomFields as $customField) {
            if (! ItemCustomFieldSetting::where('custom_field_id', $customField->id)->where('item_id', $itemId)->exists()) {
                $customFieldsValueList->push([
                    'custom_field_id' => $customField->id,
                    'label_name' => $customField->label_name,
                    'custom_field_type' => $customField->custom_field_type,
                    'enable_for_all' => $customField->enable_for_all,
                    'open_in_popup' => $customField->open_in_popup,
                    'status' => $customField->status,
                    'local_status' => $customField->status,
                    'default_value' => null,
                    'value' => null,
                    'default_formula' => null,
                    'is_able_to_edit' => false,
                    'select_default_value_or_formula' => null,
                    'input_type' => ItemCustomField::CUSTOM_FIELD_ARRAY_WITH_INPUT_TYPE[$customField->custom_field_type]['input_type'] ?? null,
                    'eligible_for_formula' => $customField->custom_field_type == ItemCustomField::CF_TYPE_NUMBER,
                    'options' => $customField->options ?? [],
                    'option_id' => null,
                    'field_type' => null,
                ]);
            }
        }
    }

    private function appendQuantityFormula(&$customFieldsValueList, $itemId = null)
    {
        $hasEligible = collect($customFieldsValueList)->contains(function ($item) {
            return $item['eligible_for_formula'] == true;
        });

        if ($hasEligible) {
            if ($itemId) {
                $itemDefaultFormula = ItemCustomFieldFormula::where('is_system_field', true)
                    ->where('system_field_name', 'Quantity')
                    ->where('item_id', $itemId)
                    ->select('id', 'formula', 'custom_field_id', 'item_id', 'used_cf_ids_for_formula')
                    ->first();
            }

            $customFieldsValueList->push([
                'label_name' => 'Quantity',
                'status' => true,
                'local_status' => true,
                'is_system_field' => true,
                'system_field_name' => 'Quantity',
                'default_formula' => $itemDefaultFormula ? $itemDefaultFormula : null,
            ]);
        }
    }
}
