<?php

namespace App\Actions\CustomFieldsItemMaster;

use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldDefaultValue;
use App\Models\ItemCustomFieldFormula;
use App\Models\ItemCustomFieldValue;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

class DeleteCustomFieldOfItemMaster
{
    use AsAction;

    public function handle($id)
    {
        $customFieldItemMaster = ItemCustomField::findOrFail($id);

        $customFieldUsedInFormula = ItemCustomFieldFormula::with('customField')->whereRaw("JSON_CONTAINS(used_cf_ids_for_formula, '[{$id}]')")->first();
        if ($customFieldUsedInFormula) {
            if ($customFieldUsedInFormula->custom_field_id != null && $customFieldUsedInFormula->is_system_field == false) {
                throw new UnprocessableEntityHttpException('This custom field cannot be deleted because it is used in the "' . $customFieldUsedInFormula->customField->label_name . '" custom field formula.');
            } else {
                throw new UnprocessableEntityHttpException('This custom field cannot be deleted because it is used in the Quantity formula.');
            }
        }

        $customFieldsTransactionValue = ItemCustomFieldValue::where('custom_field_id', $id)->exists();
        if ($customFieldsTransactionValue) {
            throw new UnprocessableEntityHttpException('This custom field cannot be deleted because it is used in transactions.');
        }

        $customFieldItemMaster->delete();
    }
}
