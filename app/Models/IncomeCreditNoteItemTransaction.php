<?php

namespace App\Models;

use App\Models\Master\ItemMaster;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Carbon;

/**
 * App\Models\IncomeCreditNoteItemTransaction
 *
 * @property int $id
 * @property int $income_cn_id
 * @property int $item_id
 * @property string|null $additional_description
 * @property int|null $unit_id
 * @property float|null $quantity
 * @property float|null $rpu_with_gst
 * @property float|null $rpu_without_gst
 * @property int $discount_type
 * @property float|null $discount_value
 * @property float|null $total_discount_amount
 * @property int|null $gst_id
 * @property float|null $gst_tax_percentage
 * @property float|null $total
 * @property int|null $ledger_id
 * @property float|null $classification_igst_tax
 * @property float|null $classification_cgst_tax
 * @property float|null $classification_sgst_tax
 * @property float|null $classification_cess_tax
 * @property float $classification_is_rcm_applicable
 * @property float|null $classification_nature_type
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $consolidating_items_to_invoice
 * @property float $cess_rate
 * @property float $cess_amount
 * @property float $taxable_value
 * @property-read TaxClassificationDetails|null $classificationNatureType
 * @property-read Ledger|null $ledger
 * @property-read IncomeCreditNoteTransaction $incomeCreditNoteTransaction
 *
 * @method static Builder|IncomeCreditNoteItemTransaction newModelQuery()
 * @method static Builder|IncomeCreditNoteItemTransaction newQuery()
 * @method static Builder|IncomeCreditNoteItemTransaction query()
 * @method static Builder|IncomeCreditNoteItemTransaction whereAdditionalDescription($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereCessAmount($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereCessRate($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereClassificationCessTax($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereClassificationCgstTax($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereClassificationIgstTax($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereClassificationIsRcmApplicable($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereClassificationNatureType($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereClassificationSgstTax($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereConsolidatingItemsToInvoice($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereCreatedAt($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereDiscountType($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereDiscountValue($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereGstId($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereGstTaxPercentage($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereId($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereIncomeCnId($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereItemId($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereLedgerId($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereQuantity($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereRpuWithGst($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereRpuWithoutGst($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereTotal($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereTotalDiscountAmount($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereUnitId($value)
 * @method static Builder|IncomeCreditNoteItemTransaction whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property float $taxable_amount
 * @property-read ItemMaster $items
 * @property-read \App\Models\UnitOfMeasurement|null $unit
 *
 * @method static Builder|IncomeCreditNoteItemTransaction whereTaxableAmount($value)
 */
class IncomeCreditNoteItemTransaction extends Model
{
    use HasFactory;

    public $table = 'income_credit_note_item_transactions';

    public $fillable = [
        'income_cn_id',
        'item_id',
        'additional_description',
        'unit_id',
        'hsn_code',
        'quantity',
        'mrp',
        'with_tax',
        'rpu_with_gst',
        'rpu_without_gst',
        'discount_type',
        'discount_value',
        'discount_type_2',
        'discount_value_2',
        'total_discount_amount',
        'gst_id',
        'gst_tax_percentage',
        'total',
        'ledger_id',
        'classification_nature_type',
        'classification_is_rcm_applicable',
        'classification_igst_tax',
        'classification_cgst_tax',
        'classification_sgst_tax',
        'classification_cess_tax',
        'consolidating_items_to_invoice',
        'cess_rate',
        'cess_amount',
        'taxable_amount',
        'decimal_places_for_quantity',
        'decimal_places_for_rate',
    ];

    public $casts = [
        'hsn_code' => 'string',
        'quantity' => 'double',
        'rpu_with_gst' => 'double',
        'rpu_without_gst' => 'double',
        'discount_type' => 'integer',
        'discount_value' => 'double',
        'discount_type_2' => 'integer',
        'discount_value_2' => 'double',
        'total_discount_amount' => 'double',
        'gst_tax_percentage' => 'double',
        'total' => 'double',
        'additional_description' => 'string',
        'classification_nature_type' => 'integer',
        'classification_is_rcm_applicable' => 'double',
        'classification_igst_tax' => 'double',
        'classification_cgst_tax' => 'double',
        'classification_sgst_tax' => 'double',
        'classification_cess_tax' => 'double',
        'consolidating_items_to_invoice' => 'string',
        'cess_rate' => 'double',
        'cess_amount' => 'double',
        'with_tax' => 'boolean',
    ];

    public $appends = ['taxable_value'];

    public function classificationNatureType(): BelongsTo
    {
        return $this->belongsTo(TaxClassificationDetails::class, 'classification_nature_type', 'id');
    }

    public function items(): BelongsTo
    {
        return $this->belongsTo(ItemMaster::class, 'item_id', 'id');
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasurement::class, 'unit_id', 'id');
    }

    public function ledger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'ledger_id', 'id');
    }

    public function incomeCreditNoteTransaction(): BelongsTo
    {
        return $this->belongsTo(IncomeCreditNoteTransaction::class, 'income_cn_id', 'id');
    }

    public function gst(): BelongsTo
    {
        return $this->belongsTo(GstTax::class, 'gst_id', 'id');
    }

    public function customFieldTransactionItemsValues(): MorphMany
    {
        return $this->morphMany(ItemCustomFieldValue::class, 'model');
    }

    protected function taxableValue(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => ((float) $this->quantity * (float) $this->rpu_without_gst) - (float) $this->total_discount_amount,
        );
    }

    public function getLedgerReport()
    {
        $transaction = $this->incomeCreditNoteTransaction;

        return [
            'transaction_id' => $transaction->id,
            'date' => $transaction->date->format('d-m-y'),
            'ledger_name' => $transaction->customer->name,
            'transaction_type' => 'Sale Cr. Note',
            'voucher_no' => $transaction->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => $this->taxable_value,
            'credit_amount' => 0,
            'narration' => $transaction->narration ?? null,
            'balance' => 0,
        ];
    }
}
