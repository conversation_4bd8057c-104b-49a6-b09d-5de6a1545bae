<?php

namespace App\Models;

use App\Models\Master\ItemMaster;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Carbon;

/**
 * App\Models\ExpenseDebitNoteItemTransaction
 *
 * @property int $id
 * @property int $debit_note_id
 * @property int $item_id
 * @property string|null $additional_description
 * @property int|null $unit_id
 * @property float|null $quantity
 * @property float|null $rpu_with_gst
 * @property float|null $rpu_without_gst
 * @property int $discount_type
 * @property float|null $discount_value
 * @property float|null $total_discount_amount
 * @property int|null $gst_id
 * @property float|null $gst_tax_percentage
 * @property float|null $total
 * @property int|null $ledger_id
 * @property float|null $classification_igst_tax
 * @property float|null $classification_cgst_tax
 * @property float|null $classification_sgst_tax
 * @property float|null $classification_cess_tax
 * @property float $classification_is_rcm_applicable
 * @property float|null $classification_nature_type
 * @property string|null $consolidating_items_to_invoice
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property float $cess_rate
 * @property float $cess_amount
 * @property float $taxable_value
 * @property-read TaxClassificationDetails|null $classificationNatureType
 * @property-read Ledger|null $ledger
 *
 * @method static Builder|ExpenseDebitNoteItemTransaction newModelQuery()
 * @method static Builder|ExpenseDebitNoteItemTransaction newQuery()
 * @method static Builder|ExpenseDebitNoteItemTransaction query()
 * @method static Builder|ExpenseDebitNoteItemTransaction whereAdditionalDescription($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereCessAmount($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereCessRate($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereClassificationCessTax($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereClassificationCgstTax($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereClassificationIgstTax($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereClassificationIsRcmApplicable($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereClassificationNatureType($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereClassificationSgstTax($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereConsolidatingItemsToInvoice($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereCreatedAt($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereDebitNoteId($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereDiscountType($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereDiscountValue($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereGstId($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereGstTaxPercentage($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereId($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereItemId($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereLedgerId($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereQuantity($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereRpuWithGst($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereRpuWithoutGst($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereTotal($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereTotalDiscountAmount($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereUnitId($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property float $classification_is_itc_applicable
 * @property float $taxable_amount
 * @property-read ExpenseDebitNoteTransaction $expenseDebitNotesTransaction
 * @property-read ItemMaster $items
 *
 * @method static Builder|ExpenseDebitNoteItemTransaction whereClassificationIsItcApplicable($value)
 * @method static Builder|ExpenseDebitNoteItemTransaction whereTaxableAmount($value)
 *
 * @property-read \App\Models\UnitOfMeasurement|null $unit
 */
class ExpenseDebitNoteItemTransaction extends Model
{
    use HasFactory;

    public $table = 'expense_debit_note_transaction_items';

    public $fillable = [
        'debit_note_id',
        'item_id',
        'additional_description',
        'unit_id',
        'hsn_code',
        'mrp',
        'quantity',
        'with_tax',
        'rpu_with_gst',
        'rpu_without_gst',
        'discount_type',
        'discount_value',
        'total_discount_amount',
        'gst_id',
        'gst_tax_percentage',
        'total',
        'ledger_id',
        'classification_nature_type',
        'classification_is_rcm_applicable',
        'classification_is_itc_applicable',
        'classification_igst_tax',
        'classification_cgst_tax',
        'classification_sgst_tax',
        'classification_cess_tax',
        'consolidating_items_to_invoice',
        'cess_rate',
        'cess_amount',
        'taxable_amount',
        'discount_type_2',
        'discount_value_2',
        'decimal_places_for_quantity',
        'decimal_places_for_rate',
    ];

    public $casts = [
        'hsn_code' => 'string',
        'quantity' => 'double',
        'rpu_with_gst' => 'double',
        'rpu_without_gst' => 'double',
        'discount_type' => 'integer',
        'discount_value' => 'double',
        'discount_type_2' => 'integer',
        'discount_value_2' => 'double',
        'total_discount_amount' => 'double',
        'gst_tax_percentage' => 'double',
        'total' => 'double',
        'classification_nature_type' => 'integer',
        'classification_is_rcm_applicable' => 'double',
        'classification_is_itc_applicable' => 'double',
        'classification_igst_tax' => 'double',
        'classification_cgst_tax' => 'double',
        'classification_sgst_tax' => 'double',
        'classification_cess_tax' => 'double',
        'consolidating_items_to_invoice' => 'string',
        'cess_rate' => 'double',
        'cess_amount' => 'double',
        'with_tax' => 'boolean',
    ];

    public $append = ['taxable_value'];

    public function classificationNatureType(): BelongsTo
    {
        return $this->belongsTo(TaxClassificationDetails::class, 'classification_nature_type', 'id');
    }

    public function ledger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'ledger_id', 'id');
    }

    public function items(): BelongsTo
    {
        return $this->belongsTo(ItemMaster::class, 'item_id', 'id');
    }

    public function expenseDebitNotesTransaction(): BelongsTo
    {
        return $this->belongsTo(ExpenseDebitNoteTransaction::class, 'debit_note_id', 'id');
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasurement::class, 'unit_id', 'id');
    }

    public function gst(): BelongsTo
    {
        return $this->belongsTo(GstTax::class, 'gst_id', 'id');
    }

    public function customFieldTransactionItemsValues(): MorphMany
    {
        return $this->morphMany(ItemCustomFieldValue::class, 'model');
    }

    protected function taxableValue(): Attribute
    {
        return Attribute::make(
            get: fn ($value
            ) => ((float) $this->quantity * (float) $this->rpu_without_gst) - (float) $this->total_discount_amount,
        );
    }

    public function getLedgerReport()
    {
        $transaction = $this->expenseDebitNotesTransaction;

        return [
            'transaction_id' => $transaction->id,
            'date' => $transaction->voucher_date->format('d-m-y'),
            'ledger_name' => $transaction->supplier->name,
            'transaction_type' => 'Purchase Return',
            'voucher_no' => $transaction->voucher_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'narration' => $transaction->narration ?? null,
            'credit_amount' => $this->taxable_value,
            'balance' => 0,
        ];
    }
}
