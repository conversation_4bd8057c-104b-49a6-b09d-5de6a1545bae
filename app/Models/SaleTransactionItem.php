<?php

namespace App\Models;

use App\Models\Master\ItemMaster;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Carbon;

/**
 * App\Models\SaleTransactionItem
 *
 * @property int $id
 * @property int $sale_transactions_id
 * @property int $item_id
 * @property string|null $additional_description
 * @property int|null $unit_id
 * @property float|null $quantity
 * @property float|null $free_quantity
 * @property float|null $rpu_with_gst
 * @property float|null $rpu_without_gst
 * @property int $discount_type
 * @property float|null $discount_value
 * @property float|null $total_discount_amount
 * @property int|null $gst_id
 * @property float|null $gst_tax_percentage
 * @property float|null $total
 * @property int|null $ledger_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property float|null $classification_igst_tax
 * @property float|null $classification_cgst_tax
 * @property float|null $classification_sgst_tax
 * @property float|null $classification_cess_tax
 * @property int $classification_is_rcm_applicable
 * @property int|null $classification_nature_type
 * @property string|null $consolidating_items_to_invoice
 * @property float $cess_rate
 * @property float $cess_amount
 * @property float $taxable_value
 * @property-read TaxClassificationDetails|null $classificationNatureType
 * @property-read ItemMaster $items
 * @property-read Ledger|null $ledger
 * @property-read SaleTransaction $saleTransaction
 * @property-read UnitOfMeasurement $unit
 *
 * @method static Builder|SaleTransactionItem newModelQuery()
 * @method static Builder|SaleTransactionItem newQuery()
 * @method static Builder|SaleTransactionItem query()
 * @method static Builder|SaleTransactionItem whereAdditionalDescription($value)
 * @method static Builder|SaleTransactionItem whereCessAmount($value)
 * @method static Builder|SaleTransactionItem whereCessRate($value)
 * @method static Builder|SaleTransactionItem whereClassificationCessTax($value)
 * @method static Builder|SaleTransactionItem whereClassificationCgstTax($value)
 * @method static Builder|SaleTransactionItem whereClassificationIgstTax($value)
 * @method static Builder|SaleTransactionItem whereClassificationIsRcmApplicable($value)
 * @method static Builder|SaleTransactionItem whereClassificationNatureType($value)
 * @method static Builder|SaleTransactionItem whereClassificationSgstTax($value)
 * @method static Builder|SaleTransactionItem whereConsolidatingItemsToInvoice($value)
 * @method static Builder|SaleTransactionItem whereCreatedAt($value)
 * @method static Builder|SaleTransactionItem whereDiscountType($value)
 * @method static Builder|SaleTransactionItem whereDiscountValue($value)
 * @method static Builder|SaleTransactionItem whereGstId($value)
 * @method static Builder|SaleTransactionItem whereGstTaxPercentage($value)
 * @method static Builder|SaleTransactionItem whereId($value)
 * @method static Builder|SaleTransactionItem whereItemId($value)
 * @method static Builder|SaleTransactionItem whereLedgerId($value)
 * @method static Builder|SaleTransactionItem whereQuantity($value)
 * @method static Builder|SaleTransactionItem whereFreeQuantity($value)
 * @method static Builder|SaleTransactionItem whereRpuWithGst($value)
 * @method static Builder|SaleTransactionItem whereRpuWithoutGst($value)
 * @method static Builder|SaleTransactionItem whereSaleTransactionsId($value)
 * @method static Builder|SaleTransactionItem whereTotal($value)
 * @method static Builder|SaleTransactionItem whereTotalDiscountAmount($value)
 * @method static Builder|SaleTransactionItem whereUnitId($value)
 * @method static Builder|SaleTransactionItem whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property float $taxable_amount
 *
 * @method static Builder|SaleTransactionItem whereTaxableAmount($value)
 */
class SaleTransactionItem extends Model
{
    public $table = 'sale_transaction_items';

    public $fillable = [
        'sale_transactions_id',
        'item_id',
        'additional_description',
        'unit_id',
        'hsn_code',
        'quantity',
        'free_quantity',
        'rpu_with_gst',
        'rpu_without_gst',
        'discount_type',
        'discount_value',
        'discount_type_2',
        'discount_value_2',
        'total_discount_amount',
        'gst_id',
        'gst_tax_percentage',
        'total',
        'ledger_id',
        'classification_nature_type',
        'classification_is_rcm_applicable',
        'classification_is_itc_applicable',
        'classification_igst_tax',
        'classification_cgst_tax',
        'classification_sgst_tax',
        'classification_cess_tax',
        'consolidating_items_to_invoice',
        'cess_rate',
        'cess_amount',
        'taxable_amount',
        'mrp',
        'with_tax',
        'decimal_places_for_quantity',
        'decimal_places_for_rate',
    ];

    public $casts = [
        'hsn_code' => 'string',
        'quantity' => 'double',
        'free_quantity' => 'double',
        'rpu_with_gst' => 'double',
        'rpu_without_gst' => 'double',
        'discount_type' => 'integer',
        'discount_value' => 'double',
        'discount_type_2' => 'integer',
        'discount_value_2' => 'double',
        'total_discount_amount' => 'double',
        'gst_tax_percentage' => 'double',
        'total' => 'double',
        'consolidating_items_to_invoice' => 'string',
        'cess_rate' => 'double',
        'cess_amount' => 'double',
        'with_tax' => 'boolean',
        'decimal_places_for_rate' => 'integer',
    ];

    public $appends = ['taxable_value'];

    public function classificationNatureType(): BelongsTo
    {
        return $this->belongsTo(TaxClassificationDetails::class, 'classification_nature_type', 'id');
    }

    public function saleTransaction(): BelongsTo
    {
        return $this->belongsTo(SaleTransaction::class, 'sale_transactions_id', 'id');
    }

    public function items(): BelongsTo
    {
        return $this->belongsTo(ItemMaster::class, 'item_id', 'id');
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasurement::class, 'unit_id', 'id');
    }

    public function ledger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'ledger_id', 'id');
    }

    public function gst(): BelongsTo
    {
        return $this->belongsTo(GstTax::class, 'gst_id', 'id');
    }

    public function customFieldTransactionItemsValues(): MorphMany
    {
        return $this->morphMany(ItemCustomFieldValue::class, 'model');
    }

    protected function taxableValue(): Attribute
    {
        return Attribute::make(
            get: fn ($value
            ) => ((float) $this->quantity * (float) $this->rpu_without_gst) - (float) $this->total_discount_amount,
        );
    }

    public function getLedgerReport()
    {
        $saleTransaction = $this->saleTransaction;

        return [
            'transaction_id' => $saleTransaction->id,
            'date' => $saleTransaction->date->format('d-m-y'),
            'ledger_name' => $saleTransaction->customer->name,
            'transaction_type' => 'Sale',
            'voucher_no' => $saleTransaction->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'narration' => $saleTransaction->narration ?? null,
            'credit_amount' => $this->taxable_value,
            'balance' => 0,
        ];
    }
}
