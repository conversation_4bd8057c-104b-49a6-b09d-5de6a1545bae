<?php

namespace App\Models;

use App\Models\Master\ItemMaster;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class DeliveryChallanTransactionItem extends Model
{
    use HasFactory;

    public $table = 'delivery_challan_transactions_items';

    public $fillable = [
        'transaction_id',
        'item_id',
        'ledger_id',
        'unit_id',
        'quantity',
        'additional_description',
        'consolidating_items_to_invoice',
        'mrp',
        'with_tax',
        'rpu_with_gst',
        'rpu_without_gst',
        'discount_type',
        'discount_value',
        'discount_type_2',
        'discount_value_2',
        'total_discount_amount',
        'gst_id',
        'gst_tax_percentage',
        'total',
        'classification_nature_type',
        'classification_is_rcm_applicable',
        'classification_igst_tax',
        'classification_cgst_tax',
        'classification_sgst_tax',
        'classification_cess_tax',
        'decimal_places_for_quantity',
        'decimal_places_for_rate',
        'hsn_code',
    ];

    public $casts = [
        'quantity' => 'double',
        'rpu_with_gst' => 'double',
        'rpu_without_gst' => 'double',
        'discount_type' => 'integer',
        'discount_value' => 'double',
        'discount_type_2' => 'integer',
        'discount_value_2' => 'double',
        'total_discount_amount' => 'double',
        'gst_tax_percentage' => 'double',
        'total' => 'double',
        'classification_nature_type' => 'integer',
        'classification_is_rcm_applicable' => 'double',
        'classification_igst_tax' => 'double',
        'classification_cgst_tax' => 'double',
        'classification_sgst_tax' => 'double',
        'classification_cess_tax' => 'double',
        'consolidating_items_to_invoice' => 'string',
        'cess_rate' => 'double',
        'cess_amount' => 'double',
        'with_tax' => 'boolean',
        'hsn_code' => 'string',
    ];

    public $appends = ['taxable_value'];

    public function items(): BelongsTo
    {
        return $this->belongsTo(ItemMaster::class, 'item_id', 'id');
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasurement::class, 'unit_id', 'id');
    }

    public function deliveryChallanTransaction(): BelongsTo
    {
        return $this->belongsTo(DeliveryChallanTransaction::class, 'transaction_id', 'id');
    }

    public function customFieldTransactionItemsValues(): MorphMany
    {
        return $this->morphMany(ItemCustomFieldValue::class, 'model');
    }

    public function classificationNatureType(): BelongsTo
    {
        return $this->belongsTo(TaxClassificationDetails::class, 'classification_nature_type', 'id');
    }

    protected function taxableValue(): Attribute
    {
        return Attribute::make(
            get: fn ($value
            ) => ((float) $this->quantity * (float) $this->rpu_without_gst) - (float) $this->total_discount_amount,
        );
    }
}
