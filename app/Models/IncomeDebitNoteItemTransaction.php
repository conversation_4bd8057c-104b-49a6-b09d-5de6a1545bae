<?php

namespace App\Models;

use App\Models\Master\ItemMaster;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Carbon;

/**
 * App\Models\IncomeDebitNoteItemTransaction
 *
 * @property int $id
 * @property int $income_dn_id
 * @property int $item_id
 * @property string|null $additional_description
 * @property int|null $unit_id
 * @property float|null $quantity
 * @property float|null $rpu_with_gst
 * @property float|null $rpu_without_gst
 * @property int $discount_type
 * @property float|null $discount_value
 * @property float|null $total_discount_amount
 * @property int|null $gst_id
 * @property float|null $gst_tax_percentage
 * @property float|null $total
 * @property int|null $ledger_id
 * @property float|null $classification_igst_tax
 * @property float|null $classification_cgst_tax
 * @property float|null $classification_sgst_tax
 * @property float|null $classification_cess_tax
 * @property float $classification_is_rcm_applicable
 * @property float $classification_is_itc_applicable
 * @property float|null $classification_nature_type
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $consolidating_items_to_invoice
 * @property float $cess_rate
 * @property float $cess_amount
 * @property float $taxable_value
 * @property-read TaxClassificationDetails|null $classificationNatureType
 * @property-read Ledger|null $ledger
 * @property-read IncomeDebitNoteTransaction $incomeDebitNoteTransaction
 *
 * @method static Builder|IncomeDebitNoteItemTransaction newModelQuery()
 * @method static Builder|IncomeDebitNoteItemTransaction newQuery()
 * @method static Builder|IncomeDebitNoteItemTransaction query()
 * @method static Builder|IncomeDebitNoteItemTransaction whereAdditionalDescription($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereCessAmount($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereCessRate($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereClassificationCessTax($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereClassificationCgstTax($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereClassificationIgstTax($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereClassificationIsItcApplicable($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereClassificationIsRcmApplicable($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereClassificationNatureType($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereClassificationSgstTax($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereConsolidatingItemsToInvoice($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereCreatedAt($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereDiscountType($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereDiscountValue($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereGstId($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereGstTaxPercentage($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereId($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereIncomeDnId($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereItemId($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereLedgerId($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereQuantity($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereRpuWithGst($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereRpuWithoutGst($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereTotal($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereTotalDiscountAmount($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereUnitId($value)
 * @method static Builder|IncomeDebitNoteItemTransaction whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property float $taxable_amount
 * @property-read ItemMaster $items
 * @property-read \App\Models\UnitOfMeasurement|null $unit
 *
 * @method static Builder|IncomeDebitNoteItemTransaction whereTaxableAmount($value)
 */
class IncomeDebitNoteItemTransaction extends Model
{
    use HasFactory;

    public $table = 'income_debit_note_item_transactions';

    public $fillable = [
        'income_dn_id',
        'item_id',
        'additional_description',
        'unit_id',
        'hsn_code',
        'quantity',
        'mrp',
        'with_tax',
        'rpu_with_gst',
        'rpu_without_gst',
        'discount_type',
        'discount_value',
        'discount_type_2',
        'discount_value_2',
        'total_discount_amount',
        'gst_id',
        'gst_tax_percentage',
        'total',
        'ledger_id',
        'classification_nature_type',
        'classification_is_rcm_applicable',
        'classification_igst_tax',
        'classification_cgst_tax',
        'classification_sgst_tax',
        'classification_cess_tax',
        'consolidating_items_to_invoice',
        'cess_rate',
        'cess_amount',
        'taxable_amount',
        'decimal_places_for_quantity',
        'decimal_places_for_rate',
    ];

    public $casts = [
        'hsn_code' => 'string',
        'quantity' => 'double',
        'rpu_with_gst' => 'double',
        'rpu_without_gst' => 'double',
        'discount_type' => 'integer',
        'discount_value' => 'double',
        'discount_type_2' => 'integer',
        'discount_value_2' => 'double',
        'total_discount_amount' => 'double',
        'gst_tax_percentage' => 'double',
        'total' => 'double',
        'additional_description' => 'string',
        'classification_nature_type' => 'integer',
        'classification_is_rcm_applicable' => 'double',
        'classification_igst_tax' => 'double',
        'classification_cgst_tax' => 'double',
        'classification_sgst_tax' => 'double',
        'classification_cess_tax' => 'double',
        'consolidating_items_to_invoice' => 'string',
        'cess_rate' => 'double',
        'cess_amount' => 'double',
        'with_tax' => 'boolean',
    ];

    public $appends = ['taxable_value'];

    public function classificationNatureType(): BelongsTo
    {
        return $this->belongsTo(TaxClassificationDetails::class, 'classification_nature_type', 'id');
    }

    public function ledger(): BelongsTo
    {
        return $this->belongsTo(Ledger::class, 'ledger_id', 'id');
    }

    public function items(): BelongsTo
    {
        return $this->belongsTo(ItemMaster::class, 'item_id', 'id');
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(UnitOfMeasurement::class, 'unit_id', 'id');
    }

    public function incomeDebitNoteTransaction(): BelongsTo
    {
        return $this->belongsTo(IncomeDebitNoteTransaction::class, 'income_dn_id', 'id');
    }

    public function gst(): BelongsTo
    {
        return $this->belongsTo(GstTax::class, 'gst_id', 'id');
    }

    public function customFieldTransactionItemsValues(): MorphMany
    {
        return $this->morphMany(ItemCustomFieldValue::class, 'model');
    }

    public function taxableValue(): Attribute
    {
        return Attribute::make(
            get: fn ($value
            ) => ((float) $this->quantity * (float) $this->rpu_without_gst) - (float) $this->total_discount_amount,
        );
    }

    public function getLedgerReport()
    {
        $transaction = $this->incomeDebitNoteTransaction;

        return [
            'transaction_id' => $transaction->id,
            'date' => $transaction->date->format('d-m-y'),
            'ledger_name' => $transaction->customer->name,
            'transaction_type' => 'Sale Dr. Note',
            'voucher_no' => $transaction->full_invoice_number,
            'invoice_no' => '',
            'debit_amount' => 0,
            'narration' => $transaction->narration ?? null,
            'credit_amount' => $this->taxable_value,
            'balance' => 0,
        ];
    }
}
