<?php

namespace App\Repositories\v1;

use App\Actions\CommonAction\AdvancePaymentAction;
use App\Actions\CommonAction\CheckPartyLedgerMobileNumberAction;
use App\Actions\CommonAction\DirectCustomerSupplierCreate;
use App\Actions\CustomFields\StoreTransactionCustomFieldsAction;
use App\Actions\CustomFields\UpdateTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\StoreCFTransactionWiseAction;
use App\Actions\CustomFieldsItemMaster\Transaction\UpdateCFTransactionWiseAction;
use App\Models\AdditionalChargesForSalesReturnTransaction;
use App\Models\AddLessForSalesReturnTransaction;
use App\Models\GstTax;
use App\Models\ItemCustomField;
use App\Models\Ledger;
use App\Models\Master\ItemMaster;
use App\Models\PaymentDetailsForSalesReturnTransaction;
use App\Models\PaymentTransaction;
use App\Models\PaymentTransactionItem;
use App\Models\SaleReturnItemTransaction;
use App\Models\SaleReturnLedgerTransaction;
use App\Models\SaleReturnTransaction;
use App\Models\SettleAdvancePayment;
use App\Models\TaxClassificationDetails;
use App\Models\TransactionCustomField;
use App\Repositories\API\v1\BaseAPIRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Interface SaleReturnTransactionRepository.
 */
class SaleReturnTransactionRepository extends BaseAPIRepository
{
    public $classificationNatureType = null;

    public $isRCMApplicable = false;

    public $isGSTEnabled = null;

    public $oldSaleReturnTransaction = null;

    public function model()
    {
        return SaleReturnTransaction::class;
    }

    public function store($input)
    {
        try {
            DB::beginTransaction();

            unset($input['_token']);

            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['customer_ledger_id'], Ledger::CUSTOMER);
                $input['customer_ledger_id'] = $partyId;
            }

            /* Ledger mobile number */
            if (isset($input['party_phone_number'])) {
                $ledgerData = [
                    'ledger_id' => $input['customer_ledger_id'],
                    'phone_1' => $input['party_phone_number'],
                    'region_iso_1' => $input['region_iso'],
                    'region_code_1' => $input['region_code'],
                ];
                CheckPartyLedgerMobileNumberAction::run($ledgerData);
            }

            /* Prepare sale return transaction */
            $saleReturnTransactionData = $this->prepareSaleReturnData($input);

            /* Create sale return */
            $saleReturnTransaction = SaleReturnTransaction::create($saleReturnTransactionData);

            /* Store Sale return Document */
            if (isset($input['sale_return_document']) && ! empty($input['sale_return_document'])) {
                foreach ($input['sale_return_document'] as $image) {
                    $saleReturnTransaction->addMedia($image)->toMediaCollection(SaleReturnTransaction::SALE_RETURN_DOCUMENT, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Store Estimate Quote items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['sale_return_item_type'] == SaleReturnTransaction::ITEM_INVOICE) {
                $this->storeItems($input['items'], $saleReturnTransaction);
            }
            if ($input['sale_return_item_type'] == SaleReturnTransaction::ACCOUNTING_INVOICE) {
                $this->storeLedgers($input['ledgers'], $saleReturnTransaction);
            }

            /* Store Addresses */
            $this->storeAddresses($saleReturnTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($saleReturnTransaction->shipping_address_id) && ! empty($saleReturnTransaction->shippingAddress)) {
                $saleReturnTransaction->update([
                    'shipping_address_id' => $saleReturnTransaction->shippingAddress->id,
                ]);
            }

            /* Store Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->storeAdditionalCharges($input['additional_charges'], $saleReturnTransaction->id);
            }

            /* Store Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->storeAddLess($input['add_less'], $saleReturnTransaction->id);
            }

            /* Store Payment Details And Create Receipt */
            if (isset($input['payment_details']) && count($input['payment_details']) != 0) {
                $this->storePaymentDetails($input['payment_details'], $saleReturnTransaction);
            }

            /* Store Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                StoreTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::SALE_RETURN,
                    $saleReturnTransaction->id,
                    SaleReturnTransaction::class
                );
            }

            /* Settle Advance Payment */
            if (isset($input['advance_payment']) && count($input['advance_payment']) != 0) {
                AdvancePaymentAction::run($input['advance_payment'], $saleReturnTransaction);
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($saleReturnTransaction);

            DB::commit();

            return $saleReturnTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    public function update($input, $saleReturnTransaction)
    {
        try {
            DB::beginTransaction();

            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* Old sale return transaction */
            $this->oldSaleReturnTransaction = clone $saleReturnTransaction;

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['customer_ledger_id'], Ledger::CUSTOMER);
                $input['customer_ledger_id'] = $partyId;
            }

            /* Ledger mobile number */
            if (isset($input['party_phone_number'])) {
                $ledgerData = [
                    'ledger_id' => $input['customer_ledger_id'],
                    'phone_1' => $input['party_phone_number'],
                    'region_iso_1' => $input['region_iso'],
                    'region_code_1' => $input['region_code'],
                ];
                CheckPartyLedgerMobileNumberAction::run($ledgerData);
            }

            /* Prepare sale return transaction */
            $input['created_at'] = $this->oldSaleReturnTransaction->created_at;
            $saleReturnTransactionData = $this->prepareSaleReturnData($input);
            unset($saleReturnTransactionData['credit_note_number']);

            /* Update sale return transaction */
            /** @var SaleReturnTransaction $saleReturnTransaction */
            $saleReturnTransaction->update($saleReturnTransactionData);

            /* Update Sale return Document */
            if (isset($input['sale_return_document']) && ! empty($input['sale_return_document'])) {
                foreach ($input['sale_return_document'] as $image) {
                    $saleReturnTransaction->addMedia($image)->toMediaCollection(SaleReturnTransaction::SALE_RETURN_DOCUMENT, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Update sale items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['sale_return_item_type'] == SaleReturnTransaction::ITEM_INVOICE && $this->oldSaleReturnTransaction->sale_return_item_type == $input['sale_return_item_type']) {
                $this->updateItems($input['items'], $saleReturnTransaction);
            } elseif ($input['sale_return_item_type'] == SaleReturnTransaction::ACCOUNTING_INVOICE && $this->oldSaleReturnTransaction->sale_return_item_type == $input['sale_return_item_type']) {
                $this->updateLedgers($input['ledgers'], $saleReturnTransaction);
            } elseif ($input['sale_return_item_type'] == SaleReturnTransaction::ITEM_INVOICE && $this->oldSaleReturnTransaction->sale_return_item_type != $input['sale_return_item_type']) {
                $saleReturnTransaction->saleReturnLedgers()->delete();
                $this->storeItems($input['items'], $saleReturnTransaction);
            } elseif ($input['sale_return_item_type'] == SaleReturnTransaction::ACCOUNTING_INVOICE && $this->oldSaleReturnTransaction->sale_return_item_type != $input['sale_return_item_type']) {
                $saleReturnTransaction->saleReturnItems()->delete();
                $this->storeLedgers($input['ledgers'], $saleReturnTransaction);
            }

            /* Update Addresses */
            $this->updateAddresses($saleReturnTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($saleReturnTransaction->shipping_address_id) && ! empty($saleReturnTransaction->shippingAddress)) {
                $saleReturnTransaction->update([
                    'shipping_address_id' => $saleReturnTransaction->shippingAddress->id,
                ]);
            }

            /* Update or Create Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->updateAdditionalCharges($input['additional_charges'], $saleReturnTransaction->id);
            } else {
                $saleReturnTransaction->additionalCharges()->delete();
            }

            /* Update or Create Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->updateAddLess($input['add_less'], $saleReturnTransaction->id);
            } else {
                $saleReturnTransaction->addLess()->delete();
            }

            /* Update or Create Payment Details And Receipt */
            if (isset($input['payment_details']) && count($input['payment_details']) != 0) {
                $this->updatePaymentDetails($input['payment_details'], $saleReturnTransaction);
            } else {
                $ledgerIdsForRemovePayments = $saleReturnTransaction->paymentDetails->pluck('ledger_id')->toArray();

                PaymentTransaction::where('payment_voucher_number', 'sale-return/'.$this->oldSaleReturnTransaction->full_invoice_number)
                    ->where('ledger_id', $this->oldSaleReturnTransaction->customer_ledger_id)
                    ->whereIn('bank_cash_ledger_id', $ledgerIdsForRemovePayments)
                    ->financialYearDate()?->forceDelete();

                $saleReturnTransaction->paymentDetails()->delete();
            }

            /* Settle Advance Payment */
            if (isset($input['advance_payment']) && count($input['advance_payment']) != 0) {
                (new AdvancePaymentAction)->updateAdvancePayment($input['advance_payment'], $saleReturnTransaction);
            } else {
                SettleAdvancePayment::whereModelId($saleReturnTransaction->id)->whereModelType(get_class($saleReturnTransaction))->delete();
            }

            /* Update Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                UpdateTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::SALE_RETURN,
                    $saleReturnTransaction->id,
                    SaleReturnTransaction::class
                );
            } else {
                $saleReturnTransaction->customFieldValues()->delete();
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($saleReturnTransaction);

            DB::commit();

            return $saleReturnTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    private function prepareSaleReturnData($input): array
    {
        /* Destructure input arrays for clarity */
        $otherDetails = $input['other_details'] ?? [];
        $brokerDetails = $input['broker_details'] ?? [];
        $transportDetails = $input['transport_details'] ?? [];
        $ewayBillDetails = $input['eway_bill_details'] ?? [];
        $tcsDetails = $input['tcs_details'] ?? [];
        $tdsDetails = $input['tds_details'] ?? [];

        /* calculate credit period date */
        $creditPeriod = $otherDetails['credit_period'] ?? null;
        $creditPeriodType = $otherDetails['credit_period_type'] ?? null;
        $invoiceDate = Carbon::parse($input['date']);
        $creditPeriodDueDate = $creditPeriod != null && $creditPeriodType != null ? calculateCreditPeriodDate($creditPeriod, $creditPeriodType, $invoiceDate) : null;

        $shippingName = $input['shipping_address']['shipping_name'] ?? $input['shipping_name'] ?? null;
        $shippingGSTIN = $input['shipping_address']['shipping_gstin'] ?? $input['shipping_gstin'] ?? null;
        $addressName = $input['shipping_address']['address_name'] ?? $input['address_name'] ?? null;
        $partyNameSameAsAddressName = $input['shipping_address']['party_name_same_as_address_name'] ?? $input['party_name_same_as_address_name'] ?? false;
        if ($partyNameSameAsAddressName) {
            $addressName = $shippingName;
        }

        return [
            'company_id' => $input['company']->id,
            'payment_mode' => null, // Removed field
            'payment_type_ledger_id' => null, // Removed field
            'full_invoice_number' => $input['full_invoice_number'],
            'credit_note_number' => $input['invoice_number'],
            'date' => Carbon::parse($input['date']),
            'customer_ledger_id' => $input['customer_ledger_id'],
            'party_phone_number' => $input['party_phone_number'] ?? null,
            'region_iso' => $input['region_iso'] ?? null,
            'region_code' => $input['region_code'] ?? null,
            'gstin' => $this->isGSTEnabled && isset($input['gstin']) ? $input['gstin'] : null,
            'original_inv_no' => $input['original_inv_no'] ?? null,
            'original_inv_date' => $input['original_inv_date'] ?? null,
            'broker_id' => $brokerDetails['broker_id'] ?? null,
            'brokerage_for_sale' => $brokerDetails['brokerage_for_sale'] ?? null,
            'brokerage_on_value_type' => $brokerDetails['brokerage_on_value_type'] ?? null,
            'credit_period' => $creditPeriod,
            'credit_period_type' => $creditPeriodType,
            'credit_period_due_date' => $creditPeriodDueDate,
            'transport_id' => $transportDetails['transport_id'] ?? null,
            'transporter_document_number' => $transportDetails['transporter_document_number'] ?? null,
            'transporter_document_date' => isset($transportDetails['transporter_document_date']) ? Carbon::parse($transportDetails['transporter_document_date']) : null,
            'transporter_vehicle_number' => $transportDetails['transporter_vehicle_number'] ?? null,
            'shipping_name' => $shippingName,
            'shipping_gstin' => $shippingGSTIN,
            'address_name' => $addressName,
            'party_name_same_as_address_name' => $partyNameSameAsAddressName,
            'po_no' => $otherDetails['po_no'] ?? null,
            'po_date' => isset($otherDetails['po_date']) ? Carbon::parse($otherDetails['po_date']) : null,
            'sale_return_item_type' => $input['sale_return_item_type'],
            'tcs_tax_id' => $tcsDetails['tcs_tax_id'] ?? null,
            'tcs_rate' => $tcsDetails['tcs_rate'] ?? null,
            'tcs_amount' => $tcsDetails['tcs_amount'] ?? null,
            'tds_tax_id' => $tdsDetails['tds_tax_id'] ?? null, // will be added
            'tds_rate' => $tdsDetails['tds_rate'] ?? null, // will be added
            'tds_amount' => $tdsDetails['tds_amount'] ?? null, // will be added
            'cgst' => $this->isGSTEnabled ? $input['cgst'] : 0,
            'sgst' => $this->isGSTEnabled ? $input['sgst'] : 0,
            'igst' => $this->isGSTEnabled ? $input['igst'] : 0,
            'rounding_amount' => $input['rounding_amount'] ?? null,
            'total' => $input['grand_total'],
            'grand_total' => $input['grand_total'],
            'taxable_value' => $input['taxable_value'],
            'gross_value' => $input['gross_value'],
            'narration' => $input['narration'] ?? null,
            'term_and_condition' => $input['term_and_condition'] ?? null,
            'dispatch_address_id' => $input['dispatch_address_id'] ?? null,
            'shipping_address_id' => $input['shipping_address_id'] ?? null,
            'same_as_billing' => $input['same_as_billing'] ?? false,
            'is_gst_enabled' => $this->isGSTEnabled,
            'cess' => $this->isGSTEnabled ? $input['cess'] : 0,
            'is_cgst_sgst_igst_calculated' => $input['is_cgst_sgst_igst_calculated'],
            'is_gst_na' => $input['is_gst_na'],
            'created_by' => getLoginUser()->id,
            'via_api' => $input['via_api'] ?? false,
            'estimate_quote_no' => $input['estimate_quote_no'] ?? null,
            'delivery_challan_no' => $input['delivery_challan_no'] ?? null,
            'is_import' => $input['is_import'] ?? false,
            'is_round_off_not_changed' => $input['is_round_off_not_changed'] ?? true,
            'created_at' => $input['created_at'] ?? Carbon::now(), // this is for import excel
            'updated_at' => $input['updated_at'] ?? Carbon::now(), // this is for import excel
            'round_off_method' => $input['round_off_method'],
            'bank_id' => $input['bank_id'] ?? null,
        ];
    }

    private function storeItems($items, $saleReturnTransaction)
    {
        try {
            foreach ($items as $key => $item) {
                /* Prepare item data */
                $saleReturnTransactionItemData = $this->prepareItemData($item, $saleReturnTransaction);

                /* Store item */
                $saleReturnTransactionItem = SaleReturnItemTransaction::create($saleReturnTransactionItemData);

                /* Store Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    StoreCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::SALE_RETURN,
                        $saleReturnTransactionItem->id,
                        SaleReturnItemTransaction::class
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateItems($items, $saleReturnTransaction)
    {
        try {
            /* Get item ids to be removed */
            $itemIds = SaleReturnItemTransaction::where('srt_id', $saleReturnTransaction->id)->pluck('id')->toArray();
            $editItemIds = Arr::pluck($items, 'id') ?? [];
            $removeItemIds = array_diff($itemIds, $editItemIds);

            /* Delete removed items */
            SaleReturnItemTransaction::whereIn('id', array_values($removeItemIds))?->delete();

            foreach ($items as $key => $item) {
                /* Prepare item data */
                $saleReturnTransactionItemData = $this->prepareItemData($item, $saleReturnTransaction);

                /* Update or create item */
                if (! isset($item['id']) || $item['id'] == null) {
                    $saleReturnTransactionItem = SaleReturnItemTransaction::create($saleReturnTransactionItemData);
                } else {
                    $saleReturnTransactionItem = SaleReturnItemTransaction::whereId($item['id'])->first();
                    if (! empty($saleReturnTransactionItem)) {
                        $saleReturnTransactionItem->update($saleReturnTransactionItemData);
                    }
                }

                /* Update Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    UpdateCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::SALE_RETURN,
                        $saleReturnTransactionItem->id,
                        SaleReturnItemTransaction::class
                    );
                } else {
                    $saleReturnTransactionItem->customFieldTransactionItemsValues()->delete();
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareItemData($item, $saleReturnTransaction)
    {
        $cessRate = 0;
        $cessAmount = 0;
        if ($this->isGSTEnabled) {
            $itemMaster = ItemMaster::with(['model.gstGstCessRate'])->whereId($item['item_id'])->first();
            $cessRate = $itemMaster->model->gstGstCessRate->rate ?? 0;
            $taxableValue = ($item['quantity'] * $item['rpu_without_gst']) - $item['total_discount_amount'];
            $cessAmount = ($taxableValue * $cessRate) / 100;
        }

        return [
            'srt_id' => $saleReturnTransaction->id,
            'item_id' => $item['item_id'],
            'additional_description' => $item['additional_description'] ?? null,
            'ledger_id' => $item['ledger_id'],
            'unit_id' => $item['unit_id'],
            'hsn_code' => $item['hsn_code'] ?? null,
            'quantity' => $item['quantity'],
            'free_quantity' => $item['free_quantity'] ?? null,
            'mrp' => $item['mrp'] ?? null,
            'with_tax' => $item['with_tax'] ?? false,
            'rpu_with_gst' => $item['rpu_with_gst'],
            'rpu_without_gst' => $item['rpu_without_gst'],
            'discount_type' => $item['discount_type'] ?? null,
            'discount_value' => $item['discount_value'] ?? null,
            'discount_type_2' => $item['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $item['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $item['total_discount_amount'] ?? 0,
            'gst_id' => $item['gst_tax'],
            'gst_tax_percentage' => $item['gst_tax_percentage'],
            'total' => $item['total'],
            'classification_nature_type' => $this->classificationNatureType,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_igst_tax' => $this->isGSTEnabled ? $item['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $item['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $item['classification_sgst_tax'] : null,
            'classification_cess_tax' => $this->isGSTEnabled && isset($item['cess']) ? $item['cess'] : null,
            'consolidating_items_to_invoice' => $item['consolidating_items_to_invoice'] ?? false,
            'cess_rate' => $cessRate,
            'cess_amount' => $cessAmount,
            'decimal_places_for_quantity' => $item['decimal_places'] ?? 2,
            'decimal_places_for_rate' => $item['decimal_places_for_rate'] ?? 2,
        ];
    }

    private function storeLedgers($ledgers, $saleReturnTransaction)
    {
        try {
            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $saleReturnTransactionLedgerData = $this->prepareLedgerData($ledger, $saleReturnTransaction);

                /* Store ledger */
                $saleReturnTransactionLedger = SaleReturnLedgerTransaction::create($saleReturnTransactionLedgerData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateLedgers($ledgers, $saleTransaction)
    {
        try {
            $ledgerIds = SaleReturnLedgerTransaction::where('srt_id', $saleTransaction->id)->pluck('id')->toArray();
            $editLedgerIds = Arr::pluck($ledgers, 'id') ?? [];
            $removeLedgerIds = array_diff($ledgerIds, $editLedgerIds);

            /* Delete removed ledgers */
            SaleReturnLedgerTransaction::whereIn('id', array_values($removeLedgerIds))?->delete();

            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $saleTransactionLedgerData = $this->prepareLedgerData($ledger, $saleTransaction);

                /* Update or create ledger */
                if (! isset($ledger['id']) || $ledger['id'] == null) {
                    $saleTransactionLedger = SaleReturnLedgerTransaction::create($saleTransactionLedgerData);
                } else {
                    $saleTransactionLedger = SaleReturnLedgerTransaction::whereId($ledger['id'])->first();
                    if (! empty($saleTransactionLedger)) {
                        $saleTransactionLedger->update($saleTransactionLedgerData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareLedgerData($ledger, $saleReturnTransaction)
    {
        return [
            'srt_id' => $saleReturnTransaction->id,
            'ledger_id' => $ledger['ledger_id'],
            'additional_description' => $ledger['additional_description'] ?? null,
            'with_tax' => $ledger['with_tax'] ?? false,
            'rpu_with_gst' => $ledger['rpu_with_gst'],
            'rpu_without_gst' => $ledger['rpu_without_gst'],
            'discount_type' => $ledger['discount_type'] ?? null,
            'discount_value' => $ledger['discount_value'] ?? null,
            'discount_type_2' => $ledger['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $ledger['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $ledger['total_discount_amount'] ?? 0,
            'gst_id' => $ledger['gst_tax'],
            'gst_tax_percentage' => $ledger['gst_tax_percentage'],
            'total' => $ledger['total'],
            'classification_igst_tax' => $this->isGSTEnabled ? $ledger['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $ledger['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $ledger['classification_sgst_tax'] : null,
            // 'classification_cess_tax' => $this->isGSTEnabled ? $ledger['cess'] : null,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_nature_type' => $this->classificationNatureType,
        ];
    }

    private function storeAddresses($saleReturnTransaction, $input)
    {
        try {
            $dispatchAddress = $input['dispatch_address'] ?? [];
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            $addresses = [];

            if ($dispatchAddress) {
                $addresses[] = $this->prepareAddressData($saleReturnTransaction, $dispatchAddress, SaleReturnTransaction::DISPATCH_ADDRESS);
            }

            if ($billingAddress) {
                $addresses[] = $this->prepareAddressData($saleReturnTransaction, $billingAddress, SaleReturnTransaction::BILLING_ADDRESS);
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $addresses[] = $this->prepareAddressData($saleReturnTransaction, $shippingAddress, SaleReturnTransaction::SHIPPING_ADDRESS);
                }
            }

            if (! empty($addresses)) {
                $saleReturnTransaction->addresses()->createMany($addresses);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddresses($saleReturnTransaction, $input)
    {
        try {
            $dispatchAddress = $input['dispatch_address'] ?? [];
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            if ($dispatchAddress) {
                $saleReturnTransaction->addresses()->updateOrCreate(
                    ['address_type' => SaleReturnTransaction::DISPATCH_ADDRESS, 'model_id' => $saleReturnTransaction->id],
                    $this->prepareAddressData($saleReturnTransaction, $dispatchAddress, SaleReturnTransaction::DISPATCH_ADDRESS)
                );
            }

            if ($billingAddress) {
                $saleReturnTransaction->addresses()->updateOrCreate(
                    ['address_type' => SaleReturnTransaction::BILLING_ADDRESS, 'model_id' => $saleReturnTransaction->id],
                    $this->prepareAddressData($saleReturnTransaction, $billingAddress, SaleReturnTransaction::BILLING_ADDRESS)
                );
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $saleReturnTransaction->addresses()->updateOrCreate(
                        ['address_type' => SaleReturnTransaction::SHIPPING_ADDRESS, 'model_id' => $saleReturnTransaction->id],
                        $this->prepareAddressData($saleReturnTransaction, $shippingAddress, SaleReturnTransaction::SHIPPING_ADDRESS)
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddressData($saleReturnTransaction, $address, $addressType)
    {
        return [
            'address_1' => $address['address_1'] ?? null,
            'address_2' => $address['address_2'] ?? null,
            'country_id' => $address['country_id'] ?? null,
            'state_id' => $address['state_id'] ?? null,
            'city_id' => $address['city_id'] ?? null,
            'pin_code' => $address['pin_code'] ?? null,
            'model_id' => $saleReturnTransaction->id,
            'model_type' => SaleReturnTransaction::class,
            'address_type' => $addressType,
        ];
    }

    private function storeAdditionalCharges($additionChargesData, $saleReturnTransactionId)
    {
        try {
            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $saleReturnTransactionId);

                /* Store additional charge */
                AdditionalChargesForSalesReturnTransaction::create($additionChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAdditionalCharges($additionChargesData, $saleReturnTransactionId)
    {
        try {
            $additionalChargesIds = AdditionalChargesForSalesReturnTransaction::where('sale_return_id', $saleReturnTransactionId)->pluck('id')->toArray();
            $editedAdditionalChargesIds = Arr::pluck($additionChargesData, 'id');
            $removeAdditionalChargesIds = array_diff($additionalChargesIds, $editedAdditionalChargesIds);

            /* Delete additional charges */
            AdditionalChargesForSalesReturnTransaction::whereIn('id', array_values($removeAdditionalChargesIds))?->delete();

            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $saleReturnTransactionId);

                /* Update additional charge */
                if (! isset($additionCharge['id']) || $additionCharge['id'] == null) {
                    AdditionalChargesForSalesReturnTransaction::create($additionChargeData);
                } else {
                    $additionalChargeRecord = AdditionalChargesForSalesReturnTransaction::where('id', $additionCharge['id'])->first();
                    if (! empty($additionalChargeRecord)) {
                        $additionalChargeRecord->update($additionChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAdditionalChargesData($additionCharge, $saleReturnTransactionId)
    {
        if ($this->isGSTEnabled) {
            $gst = null;
            if (isset($additionCharge['ac_gst_rate_id'])) {
                $gst = GstTax::whereId($additionCharge['ac_gst_rate_id'])->first();
            }
            $gstTaxPercentage = ! empty($gst) ? $gst->tax_rate : null;
        }

        return [
            'sale_return_id' => $saleReturnTransactionId,
            'ledger_id' => $additionCharge['ac_ledger_id'],
            'charge_type' => $additionCharge['ac_type'],
            'value' => $additionCharge['ac_value'],
            'gst_rate_id' => $this->isGSTEnabled && ! empty($gst) ? $gst->id : null,
            'gst_percentage' => $this->isGSTEnabled ? $gstTaxPercentage : null,
            'total_without_tax' => $additionCharge['ac_total_without_tax'],
            'total' => $additionCharge['ac_total'],
        ];
    }

    private function storeAddLess($addLessData, $saleReturnTransactionId)
    {
        try {

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $saleReturnTransactionId);

                /* Store add less */
                AddLessForSalesReturnTransaction::create($addLessChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddLess($addLessData, $saleReturnTransactionId)
    {
        try {
            $addLessIds = AddLessForSalesReturnTransaction::where('sale_return_id', $saleReturnTransactionId)->pluck('id')->toArray();
            $editedAddLessIds = Arr::pluck($addLessData, 'id');
            $removeAddLessIds = array_diff($addLessIds, $editedAddLessIds);

            /* Delete add less */
            AddLessForSalesReturnTransaction::whereIn('id', array_values($removeAddLessIds))?->delete();

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $saleReturnTransactionId);

                /* Update add less */
                if (! isset($addLessCharge['id']) || $addLessCharge['id'] == null) {
                    AddLessForSalesReturnTransaction::create($addLessChargeData);
                } else {
                    $addLessRecord = AddLessForSalesReturnTransaction::where('id', $addLessCharge['id'])->first();
                    if (! empty($addLessRecord)) {
                        $addLessRecord->update($addLessChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddLessData($addLessCharge, $saleReturnTransactionId)
    {
        return [
            'sale_return_id' => $saleReturnTransactionId,
            'ledger_id' => $addLessCharge['al_ledger_id'],
            'is_show_in_print' => $addLessCharge['al_is_show_in_print'] ?? false,
            'type' => $addLessCharge['al_type'],
            'value' => $addLessCharge['al_value'],
            'total' => $addLessCharge['al_total'],
        ];
    }

    private function storePaymentDetails($paymentDetails, $saleReturnTransaction)
    {
        try {
            foreach ($paymentDetails as $paymentDetail) {
                /* Prepare payment detail data */
                $paymentDetailData = $this->preparePaymentDetailData($paymentDetail, $saleReturnTransaction);

                /* Store payment detail */
                $paymentDetailRecord = PaymentDetailsForSalesReturnTransaction::create($paymentDetailData);

                $receiptTransaction = PaymentTransaction::create([
                    'company_id' => $saleReturnTransaction->company_id,
                    'date' => $paymentDetailData['date'],
                    'payment_voucher_number' => 'sale-return/'.$saleReturnTransaction->full_invoice_number,
                    'is_default_created_by_transaction' => true,
                    'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                    'ledger_id' => $saleReturnTransaction->customer_ledger_id,
                    'total_paid_amount' => $paymentDetailData['amount'],
                    'narration' => $saleReturnTransaction->narration ?? null,
                    'payment_mode' => $paymentDetailData['mode'],
                    'reference_number' => $paymentDetailData['reference_no'],
                    'created_by' => $saleReturnTransaction->created_by,
                ]);

                PaymentTransactionItem::create([
                    'pc_transaction_id' => $receiptTransaction->id,
                    'sale_return_id' => $saleReturnTransaction->id,
                    'invoice_number' => $saleReturnTransaction->full_invoice_number,
                    'bill_type' => PaymentTransaction::SALE_RETURN_BILL_TYPE,
                    'paid_amount' => $paymentDetailData['amount'],
                    'discount' => 0,
                    'round_off' => 0,
                ]);

                $paymentDetailRecord->update([
                    'payment_id' => $receiptTransaction->id,
                ]);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updatePaymentDetails($paymentDetails, $saleReturnTransaction)
    {
        try {
            $paymentDetailsIds = PaymentDetailsForSalesReturnTransaction::where('sale_return_id', $saleReturnTransaction->id)->pluck('id')->toArray();
            $editedPaymentDetailsIds = Arr::pluck($paymentDetails, 'pd_id');
            $removePaymentDetailsIds = array_diff($paymentDetailsIds, $editedPaymentDetailsIds);

            /* Delete payment details */
            foreach ($removePaymentDetailsIds as $removePaymentDetailsId) {
                $removePaymentDetails = PaymentDetailsForSalesReturnTransaction::where('id', $removePaymentDetailsId)->first();

                $receiptTransaction = PaymentTransaction::where('payment_voucher_number', 'sale-return/'.$this->oldSaleReturnTransaction->full_invoice_number)
                    ->where('ledger_id', $this->oldSaleReturnTransaction->customer_ledger_id)
                    ->where('bank_cash_ledger_id', $removePaymentDetails->ledger_id)
                    ->whereId($removePaymentDetails->payment_id)
                    ->financialYearDate()?->forceDelete();

                $removePaymentDetails->delete();
            }

            foreach ($paymentDetails as $paymentDetail) {
                /* Prepare payment details data */
                $paymentDetailData = $this->preparePaymentDetailData($paymentDetail, $saleReturnTransaction);

                /* Update payment details */
                if (! isset($paymentDetail['pd_id']) || $paymentDetail['pd_id'] == null) {
                    $paymentDetailRecord = PaymentDetailsForSalesReturnTransaction::create($paymentDetailData);

                    $paymentTransaction = PaymentTransaction::create([
                        'company_id' => $saleReturnTransaction->company_id,
                        'date' => $paymentDetailData['date'],
                        'payment_voucher_number' => 'sale-return/'.$saleReturnTransaction->full_invoice_number,
                        'is_default_created_by_transaction' => true,
                        'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                        'ledger_id' => $saleReturnTransaction->customer_ledger_id,
                        'total_paid_amount' => $paymentDetailData['amount'],
                        'narration' => $saleReturnTransaction->narration ?? null,
                        'payment_mode' => $paymentDetailData['mode'],
                        'reference_number' => $paymentDetailData['reference_no'],
                        'created_by' => $saleReturnTransaction->created_by,
                    ]);

                    PaymentTransactionItem::create([
                        'pc_transaction_id' => $paymentTransaction->id,
                        'sale_return_id' => $saleReturnTransaction->id,
                        'invoice_number' => $saleReturnTransaction->full_invoice_number,
                        'bill_type' => PaymentTransaction::SALE_RETURN_BILL_TYPE,
                        'paid_amount' => $paymentDetailData['amount'],
                        'discount' => 0,
                        'round_off' => 0,
                    ]);
                } else {
                    $paymentDetailRecord = PaymentDetailsForSalesReturnTransaction::where('id', $paymentDetail['pd_id'])->first();

                    if (! empty($paymentDetailRecord)) {
                        $paymentDetailRecord->update($paymentDetailData);
                    }

                    $paymentTransaction = PaymentTransaction::where('payment_voucher_number', 'sale-return/'.$this->oldSaleReturnTransaction->full_invoice_number)
                        ->where('ledger_id', $this->oldSaleReturnTransaction->customer_ledger_id)
                        ->whereId($paymentDetailRecord->payment_id)
                        ->first();

                    if (! empty($paymentTransaction)) {
                        $paymentTransaction->update([
                            'payment_voucher_number' => 'sale-return/'.$saleReturnTransaction->full_invoice_number,
                            'date' => $paymentDetailData['date'],
                            'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                            'ledger_id' => $saleReturnTransaction->customer_ledger_id,
                            'total_paid_amount' => $paymentDetailData['amount'],
                            'narration' => $saleReturnTransaction->narration ?? null,
                            'payment_mode' => $paymentDetailData['mode'],
                            'reference_number' => $paymentDetailData['reference_no'],
                        ]);

                        $receiptTransactionItem = PaymentTransactionItem::where('pc_transaction_id', $paymentTransaction->id)->first();
                        if (! empty($receiptTransactionItem)) {
                            $receiptTransactionItem->update([
                                'invoice_number' => $saleReturnTransaction->full_invoice_number,
                                'paid_amount' => $paymentDetailData['amount'],
                            ]);
                        }
                    }
                }

                $paymentDetailRecord->update([
                    'payment_id' => $paymentTransaction->id,
                ]);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function preparePaymentDetailData($paymentDetail, $saleReturnTransaction)
    {
        return [
            'sale_return_id' => $saleReturnTransaction->id,
            'ledger_id' => $paymentDetail['pd_ledger_id'],
            'date' => Carbon::parse($paymentDetail['pd_date']),
            'amount' => $paymentDetail['pd_amount'],
            'mode' => $paymentDetail['pd_mode'] ?? null,
            'reference_no' => $paymentDetail['pd_reference_number'] ?? null,
        ];
    }
}
