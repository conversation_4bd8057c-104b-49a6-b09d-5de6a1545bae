<?php

namespace App\Repositories\v1;

use App\Actions\CommonAction\AdvancePaymentAction;
use App\Actions\CommonAction\DirectCustomerSupplierCreate;
use App\Actions\CustomFields\StoreTransactionCustomFieldsAction;
use App\Actions\CustomFields\UpdateTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\StoreCFTransactionWiseAction;
use App\Actions\CustomFieldsItemMaster\Transaction\UpdateCFTransactionWiseAction;
use App\Models\AdditionalChargesForExpenseCreditNoteTransaction;
use App\Models\AddLessForExpenseCreditNoteTransaction;
use App\Models\ItemCustomField;
use App\Models\ExpenseCreditNoteItemTransaction;
use App\Models\ExpenseCreditNoteLedgerTransaction;
use App\Models\ExpenseCreditNoteTransaction;
use App\Models\GstTax;
use App\Models\Ledger;
use App\Models\Master\ItemMaster;
use App\Models\PaymentDetailsForExpenseCreditNoteTransaction;
use App\Models\PaymentTransaction;
use App\Models\PaymentTransactionItem;
use App\Models\SettleAdvancePayment;
use App\Models\TaxClassificationDetails;
use App\Models\TransactionCustomField;
use App\Repositories\API\v1\BaseAPIRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Interface ExpenseCNTransactionRepository.
 */
class ExpenseCNTransactionRepository extends BaseAPIRepository
{
    public $classificationNatureType = null;

    public $isRCMApplicable = false;

    public $isGSTEnabled = null;

    public $oldExpenseCNTransaction = null;

    public function model()
    {
        return ExpenseCreditNoteTransaction::class;
    }

    public function store($input)
    {
        try {
            DB::beginTransaction();
            unset($input['_token']);

            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['supplier_id'], Ledger::SUPPLIER);
                $input['supplier_id'] = $partyId;
            }

            /* Prepare expense credit note transaction */
            $expenseCNTransactionData = $this->prepareExpenseCNData($input);

            /* Create expense credit note */
            $expenseCNTransaction = ExpenseCreditNoteTransaction::create($expenseCNTransactionData);

            /* Store expense credit note Document */
            if (isset($input['expense_credit_note_document']) && ! empty($input['expense_credit_note_document'])) {
                foreach ($input['expense_credit_note_document'] as $image) {
                    $expenseCNTransaction->addMedia($image)->toMediaCollection(ExpenseCreditNoteTransaction::EXPENSE_CREDIT_NOTE_DOCUMENT, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Store expense credit note items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['expense_cn_item_type'] == ExpenseCreditNoteTransaction::ITEM_INVOICE) {
                $this->storeItems($input['items'], $expenseCNTransaction);
            }
            if ($input['expense_cn_item_type'] == ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE) {
                $this->storeLedgers($input['ledgers'], $expenseCNTransaction);
            }

            /* Store Addresses */
            $this->storeAddresses($expenseCNTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($expenseCNTransaction->shipping_address_id) && ! empty($expenseCNTransaction->dispatchAddress)) {
                $expenseCNTransaction->update([
                    'shipping_address_id' => $expenseCNTransaction->dispatchAddress->id, // in expense transaction dispatch address is shipping address
                ]);
            }

            /* Store Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->storeAdditionalCharges($input['additional_charges'], $expenseCNTransaction->id);
            }

            /* Store Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->storeAddLess($input['add_less'], $expenseCNTransaction->id);
            }

            /* Store Payment Details And Create Receipt */
            if (isset($input['payment_details']) && count($input['payment_details']) != 0) {
                $this->storePaymentDetails($input['payment_details'], $expenseCNTransaction);
            }

            /* Settle Advance Payment */
            if (isset($input['advance_payment']) && count($input['advance_payment']) != 0) {
                AdvancePaymentAction::run($input['advance_payment'], $expenseCNTransaction);
            }

            /* Store Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                StoreTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::EXPENSE_CREDIT_NOTE,
                    $expenseCNTransaction->id,
                    ExpenseCreditNoteTransaction::class
                );
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($expenseCNTransaction);

            DB::commit();

            return $expenseCNTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    public function update($input, $expenseCNTransaction)
    {
        try {
            DB::beginTransaction();

            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* Old expense credit note transaction */
            $this->oldExpenseCNTransaction = clone $expenseCNTransaction;

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['supplier_id'], Ledger::SUPPLIER);
                $input['supplier_id'] = $partyId;
            }

            /* Prepare expense credit note transaction */
            $input['created_at'] = $expenseCNTransaction->created_at;
            $expenseCNTransactionData = $this->prepareExpenseCNData($input);

            /* Update expense credit note transaction */
            /** @var ExpenseCreditNoteTransaction $expenseCNTransaction */
            $expenseCNTransaction->update($expenseCNTransactionData);

            /* Update expense credit note Document */
            if (isset($input['expense_credit_note_document']) && ! empty($input['expense_credit_note_document'])) {
                foreach ($input['expense_credit_note_document'] as $image) {
                    $expenseCNTransaction->addMedia($image)->toMediaCollection(ExpenseCreditNoteTransaction::EXPENSE_CREDIT_NOTE_DOCUMENT, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Update expense credit note items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['expense_cn_item_type'] == ExpenseCreditNoteTransaction::ITEM_INVOICE && $this->oldExpenseCNTransaction->expense_cn_item_type == $input['expense_cn_item_type']) {
                $this->updateItems($input['items'], $expenseCNTransaction);
            } elseif ($input['expense_cn_item_type'] == ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE && $this->oldExpenseCNTransaction->expense_cn_item_type == $input['expense_cn_item_type']) {
                $this->updateLedgers($input['ledgers'], $expenseCNTransaction);
            } elseif ($input['expense_cn_item_type'] == ExpenseCreditNoteTransaction::ITEM_INVOICE && $this->oldExpenseCNTransaction->expense_cn_item_type != $input['expense_cn_item_type']) {
                $expenseCNTransaction->expenseCreditNoteLedgers()->delete();
                $this->storeItems($input['items'], $expenseCNTransaction);
            } elseif ($input['expense_cn_item_type'] == ExpenseCreditNoteTransaction::ACCOUNTING_INVOICE && $this->oldExpenseCNTransaction->expense_cn_item_type != $input['expense_cn_item_type']) {
                $expenseCNTransaction->expenseCreditNoteItems()->delete();
                $this->storeLedgers($input['ledgers'], $expenseCNTransaction);
            }

            /* Update Addresses */
            $this->updateAddresses($expenseCNTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($expenseCNTransaction->shipping_address_id) && ! empty($expenseCNTransaction->dispatchAddress)) {
                $expenseCNTransaction->update([
                    'shipping_address_id' => $expenseCNTransaction->dispatchAddress->id, // in expense transaction dispatch address is shipping address
                ]);
            }

            /* Update or Create Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->updateAdditionalCharges($input['additional_charges'], $expenseCNTransaction->id);
            } else {
                $expenseCNTransaction->additionalCharges()->delete();
            }

            /* Update or Create Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->updateAddLess($input['add_less'], $expenseCNTransaction->id);
            } else {
                $expenseCNTransaction->addLess()->delete();
            }

            /* Update or Create Payment Details And Receipt */
            if (isset($input['payment_details']) && count($input['payment_details']) != 0) {
                $this->updatePaymentDetails($input['payment_details'], $expenseCNTransaction);
            } else {
                $ledgerIdsForRemovePayments = $expenseCNTransaction->paymentDetails->pluck('ledger_id')->toArray();

                PaymentTransaction::where('payment_voucher_number', 'expense-credit-note/'.$this->oldExpenseCNTransaction->voucher_number)
                    ->where('ledger_id', $this->oldExpenseCNTransaction->supplier_id)
                    ->whereIn('bank_cash_ledger_id', $ledgerIdsForRemovePayments)
                    ->financialYearDate()?->forceDelete();

                $expenseCNTransaction->paymentDetails()->delete();
            }

            /* Settle Advance Payment */
            if (isset($input['advance_payment']) && count($input['advance_payment']) != 0) {
                (new AdvancePaymentAction)->updateAdvancePayment($input['advance_payment'], $expenseCNTransaction);
            } else {
                SettleAdvancePayment::whereModelId($expenseCNTransaction->id)->whereModelType(get_class($expenseCNTransaction))->delete();
            }

            /* Update Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                UpdateTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::EXPENSE_CREDIT_NOTE,
                    $expenseCNTransaction->id,
                    ExpenseCreditNoteTransaction::class
                );
            } else {
                $expenseCNTransaction->customFieldValues()->delete();
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($expenseCNTransaction);

            DB::commit();

            return $expenseCNTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    private function prepareExpenseCNData($input): array
    {
        /* Destructure input arrays for clarity */
        $brokerDetails = $input['broker_details'] ?? [];
        $transportDetails = $input['transport_details'] ?? [];
        $tcsDetails = $input['tcs_details'] ?? [];
        $tdsDetails = $input['tds_details'] ?? [];

        $shippingName = $input['shipping_address']['shipping_name'] ?? $input['shipping_name'] ?? null;
        $shippingGSTIN = $input['shipping_address']['shipping_gstin'] ?? $input['shipping_gstin'] ?? null;
        $addressName = $input['shipping_address']['address_name'] ?? $input['address_name'] ?? null;
        $partyNameSameAsAddressName = $input['shipping_address']['party_name_same_as_address_name'] ?? $input['party_name_same_as_address_name'] ?? false;
        if ($partyNameSameAsAddressName) {
            $addressName = $shippingName;
        }

        return [
            'company_id' => $input['company']->id,
            'payment_mode' => null, // Removed field
            'payment_type_ledger_id' => null, // Removed field
            'voucher_number' => $input['voucher_number'],
            'voucher_date' => Carbon::parse($input['voucher_date']),
            'supplier_id' => $input['supplier_id'],
            'original_inv_no' => $input['original_inv_no'] ?? null,
            'original_inv_date' => $input['original_inv_date'] ?? null,
            'supplier_purchase_return_number' => $input['supplier_purchase_return_number'] ?? null,
            'supplier_purchase_return_date' => isset($input['supplier_purchase_return_date']) ? Carbon::parse($input['supplier_purchase_return_date']) : null,
            'gstin' => $this->isGSTEnabled && isset($input['gstin']) ? $input['gstin'] : null,
            'broker_id' => $brokerDetails['broker_id'] ?? null,
            'brokerage_for_sale' => $brokerDetails['brokerage_for_sale'] ?? null,
            'brokerage_on_value_type' => $brokerDetails['brokerage_on_value_type'] ?? null,
            'transport_id' => $transportDetails['transport_id'] ?? null,
            'transporter_document_number' => $transportDetails['transporter_document_number'] ?? null,
            'transporter_document_date' => isset($transportDetails['transporter_document_date']) ? Carbon::parse($transportDetails['transporter_document_date']) : null,
            'transporter_vehicle_number' => $transportDetails['transporter_vehicle_number'] ?? null,
            'shipping_name' => $shippingName,
            'shipping_gstin' => $shippingGSTIN,
            'address_name' => $addressName,
            'party_name_same_as_address_name' => $partyNameSameAsAddressName,
            'expense_cn_item_type' => $input['expense_cn_item_type'],
            'tcs_tax_id' => $tcsDetails['tcs_tax_id'] ?? null,
            'tcs_rate' => $tcsDetails['tcs_rate'] ?? null,
            'tcs_amount' => $tcsDetails['tcs_amount'] ?? null,
            'ledger_of_tds' => $tdsDetails['tds_tax_id'] ?? null,
            'tds_rate' => $tdsDetails['tds_rate'] ?? null,
            'tds_amount' => $tdsDetails['tds_amount'] ?? null,
            'cgst' => $this->isGSTEnabled ? $input['cgst'] : 0,
            'sgst' => $this->isGSTEnabled ? $input['sgst'] : 0,
            'igst' => $this->isGSTEnabled ? $input['igst'] : 0,
            'rounding_amount' => $input['rounding_amount'] ?? null,
            'total' => $input['grand_total'],
            'grand_total' => $input['grand_total'],
            'taxable_value' => $input['taxable_value'],
            'gross_value' => $input['gross_value'],
            'narration' => $input['narration'] ?? null,
            'shipping_address_id' => $input['shipping_address_id'] ?? null,
            'same_as_billing' => $input['same_as_billing'] ?? false,
            'is_gst_enabled' => $this->isGSTEnabled,
            'cess' => $this->isGSTEnabled ? $input['cess'] : 0,
            'is_cgst_sgst_igst_calculated' => $input['is_cgst_sgst_igst_calculated'],
            'is_gst_na' => $input['is_gst_na'],
            'created_by' => getLoginUser()->id,
            'via_api' => $input['via_api'] ?? false,
            'is_import' => $input['is_import'] ?? false,
            'is_round_off_not_changed' => $input['is_round_off_not_changed'] ?? true,
            'created_at' => $input['created_at'] ?? Carbon::now(), // this is for import excel
            'updated_at' => $input['updated_at'] ?? Carbon::now(), // this is for import excel
            'round_off_method' => $input['round_off_method'],
        ];
    }

    private function storeItems($items, $expenseCNTransaction)
    {
        try {
            foreach ($items as $key => $item) {
                /* Prepare item data */
                $expenseCNTransactionItemData = $this->prepareItemData($item, $expenseCNTransaction);

                /* Store item */
                $expenseCNTransactionItem = ExpenseCreditNoteItemTransaction::create($expenseCNTransactionItemData);

                /* Store Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    StoreCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::EXPENSE_CREDIT_NOTE,
                        $expenseCNTransactionItem->id,
                        ExpenseCreditNoteItemTransaction::class
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateItems($items, $expenseCNTransaction)
    {
        try {
            /* Get item ids to be removed */
            $itemIds = ExpenseCreditNoteItemTransaction::where('expense_cn_id', $expenseCNTransaction->id)->pluck('id')->toArray();
            $editItemIds = Arr::pluck($items, 'id') ?? [];
            $removeItemIds = array_diff($itemIds, $editItemIds);

            /* Delete removed items */
            ExpenseCreditNoteItemTransaction::whereIn('id', array_values($removeItemIds))?->delete();

            foreach ($items as $key => $item) {
                /* Prepare item data */
                $expenseCNTransactionItemData = $this->prepareItemData($item, $expenseCNTransaction);

                /* Update or create item */
                if (! isset($item['id']) || $item['id'] == null) {
                    $expenseCNTransactionItem = ExpenseCreditNoteItemTransaction::create($expenseCNTransactionItemData);
                } else {
                    $expenseCNTransactionItem = ExpenseCreditNoteItemTransaction::whereId($item['id'])->first();
                    if (! empty($expenseCNTransactionItem)) {
                        $expenseCNTransactionItem->update($expenseCNTransactionItemData);
                    }
                }

                /* Update Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    UpdateCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::EXPENSE_CREDIT_NOTE,
                        $expenseCNTransactionItem->id,
                        ExpenseCreditNoteItemTransaction::class
                    );
                } else {
                    $expenseCNTransactionItem->customFieldTransactionItemsValues()->delete();
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareItemData($item, $expenseCNTransaction)
    {
        $cessRate = 0;
        $cessAmount = 0;
        if ($this->isGSTEnabled) {
            $itemMaster = ItemMaster::with(['model.gstGstCessRate'])->whereId($item['item_id'])->first();
            $cessRate = $itemMaster->model->gstGstCessRate->rate ?? 0;
            $taxableValue = ($item['quantity'] * $item['rpu_without_gst']) - $item['total_discount_amount'];
            $cessAmount = ($taxableValue * $cessRate) / 100;
        }

        return [
            'expense_cn_id' => $expenseCNTransaction->id,
            'item_id' => $item['item_id'],
            'additional_description' => $item['additional_description'] ?? null,
            'ledger_id' => $item['ledger_id'],
            'unit_id' => $item['unit_id'],
            'hsn_code' => $item['hsn_code'] ?? null,
            'quantity' => $item['quantity'],
            'mrp' => $item['mrp'] ?? null,
            'with_tax' => $item['with_tax'] ?? false,
            'rpu_with_gst' => $item['rpu_with_gst'],
            'rpu_without_gst' => $item['rpu_without_gst'],
            'discount_type' => $item['discount_type'] ?? null,
            'discount_value' => $item['discount_value'] ?? null,
            'discount_type_2' => $item['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $item['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $item['total_discount_amount'] ?? 0,
            'gst_id' => $item['gst_tax'],
            'gst_tax_percentage' => $item['gst_tax_percentage'],
            'total' => $item['total'],
            'classification_nature_type' => $this->classificationNatureType,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_igst_tax' => $this->isGSTEnabled ? $item['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $item['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $item['classification_sgst_tax'] : null,
            'classification_cess_tax' => $this->isGSTEnabled && isset($item['cess']) ? $item['cess'] : null,
            'classification_is_itc_applicable' => $item['classification_is_itc_applicable'] ?? true,
            'consolidating_items_to_invoice' => $item['consolidating_items_to_invoice'] ?? false,
            'cess_rate' => $cessRate,
            'cess_amount' => $cessAmount,
            'decimal_places_for_quantity' => $item['decimal_places'] ?? 2,
            'decimal_places_for_rate' => $item['decimal_places_for_rate'] ?? 2,
        ];
    }

    private function storeLedgers($ledgers, $expenseCNTransaction)
    {
        try {
            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $expenseCNTransactionLedgerData = $this->prepareLedgerData($ledger, $expenseCNTransaction);

                /* Store ledger */
                $expenseCNTransactionLedger = ExpenseCreditNoteLedgerTransaction::create($expenseCNTransactionLedgerData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateLedgers($ledgers, $expenseCNTransaction)
    {
        try {
            $ledgerIds = ExpenseCreditNoteLedgerTransaction::where('expense_cn_id', $expenseCNTransaction->id)->pluck('id')->toArray();
            $editLedgerIds = Arr::pluck($ledgers, 'id') ?? [];
            $removeLedgerIds = array_diff($ledgerIds, $editLedgerIds);

            /* Delete removed ledgers */
            ExpenseCreditNoteLedgerTransaction::whereIn('id', array_values($removeLedgerIds))?->delete();

            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $expenseCNTransactionLedgerData = $this->prepareLedgerData($ledger, $expenseCNTransaction);

                /* Update or create ledger */
                if (! isset($ledger['id']) || $ledger['id'] == null) {
                    $expenseCNTransactionLedger = ExpenseCreditNoteLedgerTransaction::create($expenseCNTransactionLedgerData);
                } else {
                    $expenseCNTransactionLedger = ExpenseCreditNoteLedgerTransaction::whereId($ledger['id'])->first();
                    if (! empty($expenseCNTransactionLedger)) {
                        $expenseCNTransactionLedger->update($expenseCNTransactionLedgerData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareLedgerData($ledger, $expenseCNTransaction)
    {
        return [
            'expense_cn_id' => $expenseCNTransaction->id,
            'ledger_id' => $ledger['ledger_id'],
            'additional_description' => $ledger['additional_description'] ?? null,
            'with_tax' => $ledger['with_tax'] ?? false,
            'rpu_with_gst' => $ledger['rpu_with_gst'],
            'rpu_without_gst' => $ledger['rpu_without_gst'],
            'discount_type' => $ledger['discount_type'] ?? null,
            'discount_value' => $ledger['discount_value'] ?? null,
            'discount_type_2' => $ledger['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $ledger['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $ledger['total_discount_amount'] ?? 0,
            'gst_id' => $ledger['gst_tax'],
            'gst_tax_percentage' => $ledger['gst_tax_percentage'],
            'total' => $ledger['total'],
            'classification_igst_tax' => $this->isGSTEnabled ? $ledger['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $ledger['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $ledger['classification_sgst_tax'] : null,
            'classification_is_itc_applicable' => $ledger['classification_is_itc_applicable'] ?? true,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_nature_type' => $this->classificationNatureType,
        ];
    }

    private function storeAddresses($expenseCNTransaction, $input)
    {
        try {
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            $addresses = [];

            if ($billingAddress) {
                $addresses[] = $this->prepareAddressData($expenseCNTransaction, $billingAddress, ExpenseCreditNoteTransaction::BILLING_ADDRESS);
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $addresses[] = $this->prepareAddressData($expenseCNTransaction, $shippingAddress, ExpenseCreditNoteTransaction::SHIPPING_ADDRESS);
                }
            }

            if (! empty($addresses)) {
                $expenseCNTransaction->addresses()->createMany($addresses);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddresses($expenseCNTransaction, $input)
    {
        try {
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            if ($billingAddress) {
                $expenseCNTransaction->addresses()->updateOrCreate(
                    ['address_type' => ExpenseCreditNoteTransaction::BILLING_ADDRESS, 'model_id' => $expenseCNTransaction->id],
                    $this->prepareAddressData($expenseCNTransaction, $billingAddress, ExpenseCreditNoteTransaction::BILLING_ADDRESS)
                );
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $expenseCNTransaction->addresses()->updateOrCreate(
                        ['address_type' => ExpenseCreditNoteTransaction::SHIPPING_ADDRESS, 'model_id' => $expenseCNTransaction->id],
                        $this->prepareAddressData($expenseCNTransaction, $shippingAddress, ExpenseCreditNoteTransaction::SHIPPING_ADDRESS)
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddressData($expenseCNTransaction, $address, $addressType)
    {
        return [
            'address_1' => $address['address_1'] ?? null,
            'address_2' => $address['address_2'] ?? null,
            'country_id' => $address['country_id'] ?? null,
            'state_id' => $address['state_id'] ?? null,
            'city_id' => $address['city_id'] ?? null,
            'pin_code' => $address['pin_code'] ?? null,
            'model_id' => $expenseCNTransaction->id,
            'model_type' => ExpenseCreditNoteTransaction::class,
            'address_type' => $addressType,
        ];
    }

    private function storeAdditionalCharges($additionChargesData, $expenseCNTransactionId)
    {
        try {
            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $expenseCNTransactionId);

                /* Store additional charge */
                AdditionalChargesForExpenseCreditNoteTransaction::create($additionChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAdditionalCharges($additionChargesData, $expenseCNTransactionId)
    {
        try {
            $additionalChargesIds = AdditionalChargesForExpenseCreditNoteTransaction::where('expense_cn_id', $expenseCNTransactionId)->pluck('id')->toArray();
            $editedAdditionalChargesIds = Arr::pluck($additionChargesData, 'id');
            $removeAdditionalChargesIds = array_diff($additionalChargesIds, $editedAdditionalChargesIds);

            /* Delete additional charges */
            AdditionalChargesForExpenseCreditNoteTransaction::whereIn('id', array_values($removeAdditionalChargesIds))?->delete();

            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $expenseCNTransactionId);

                /* Update additional charge */
                if (! isset($additionCharge['id']) || $additionCharge['id'] == null) {
                    AdditionalChargesForExpenseCreditNoteTransaction::create($additionChargeData);
                } else {
                    $additionalChargeRecord = AdditionalChargesForExpenseCreditNoteTransaction::where('id', $additionCharge['id'])->first();
                    if (! empty($additionalChargeRecord)) {
                        $additionalChargeRecord->update($additionChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAdditionalChargesData($additionCharge, $expenseCNTransactionId)
    {
        if ($this->isGSTEnabled) {
            $gst = null;
            if (isset($additionCharge['ac_gst_rate_id'])) {
                $gst = GstTax::whereId($additionCharge['ac_gst_rate_id'])->first();
            }
            $gstTaxPercentage = ! empty($gst) ? $gst->tax_rate : null;
        }

        return [
            'expense_cn_id' => $expenseCNTransactionId,
            'ledger_id' => $additionCharge['ac_ledger_id'],
            'charge_type' => $additionCharge['ac_type'],
            'value' => $additionCharge['ac_value'],
            'gst_rate_id' => $this->isGSTEnabled && ! empty($gst) ? $gst->id : null,
            'gst_percentage' => $this->isGSTEnabled ? $gstTaxPercentage : null,
            'total_without_tax' => $additionCharge['ac_total_without_tax'],
            'total' => $additionCharge['ac_total'],
        ];
    }

    private function storeAddLess($addLessData, $expenseCNTransactionId)
    {
        try {

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $expenseCNTransactionId);

                /* Store add less */
                AddLessForExpenseCreditNoteTransaction::create($addLessChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddLess($addLessData, $purchaseReturnTransactionId)
    {
        try {
            $addLessIds = AddLessForExpenseCreditNoteTransaction::where('expense_cn_id', $purchaseReturnTransactionId)->pluck('id')->toArray();
            $editedAddLessIds = Arr::pluck($addLessData, 'id');
            $removeAddLessIds = array_diff($addLessIds, $editedAddLessIds);

            /* Delete add less */
            AddLessForExpenseCreditNoteTransaction::whereIn('id', array_values($removeAddLessIds))?->delete();

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $purchaseReturnTransactionId);

                /* Update add less */
                if (! isset($addLessCharge['id']) || $addLessCharge['id'] == null) {
                    AddLessForExpenseCreditNoteTransaction::create($addLessChargeData);
                } else {
                    $addLessRecord = AddLessForExpenseCreditNoteTransaction::where('id', $addLessCharge['id'])->first();
                    if (! empty($addLessRecord)) {
                        $addLessRecord->update($addLessChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddLessData($addLessCharge, $expenseCNTransactionId)
    {
        return [
            'expense_cn_id' => $expenseCNTransactionId,
            'ledger_id' => $addLessCharge['al_ledger_id'],
            'is_show_in_print' => $addLessCharge['al_is_show_in_print'] ?? false,
            'type' => $addLessCharge['al_type'],
            'value' => $addLessCharge['al_value'],
            'total' => $addLessCharge['al_total'],
        ];
    }

    private function storePaymentDetails($paymentDetails, $expenseCNTransaction)
    {
        try {
            foreach ($paymentDetails as $paymentDetail) {
                /* Prepare payment detail data */
                $paymentDetailData = $this->preparePaymentDetailData($paymentDetail, $expenseCNTransaction);

                /* Store payment detail */
                $paymentDetailRecord = PaymentDetailsForExpenseCreditNoteTransaction::create($paymentDetailData);

                $paymentTransaction = PaymentTransaction::create([
                    'company_id' => $expenseCNTransaction->company_id,
                    'date' => $paymentDetailData['date'],
                    'payment_voucher_number' => 'expense-credit-note/'.$expenseCNTransaction->voucher_number,
                    'is_default_created_by_transaction' => true,
                    'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                    'ledger_id' => $expenseCNTransaction->supplier_id,
                    'total_paid_amount' => $paymentDetailData['amount'],
                    'narration' => $expenseCNTransaction->narration ?? null,
                    'payment_mode' => $paymentDetailData['mode'],
                    'reference_number' => $paymentDetailData['reference_no'],
                    'created_by' => $expenseCNTransaction->created_by,
                ]);

                PaymentTransactionItem::create([
                    'pc_transaction_id' => $paymentTransaction->id,
                    'expense_credit_id' => $expenseCNTransaction->id,
                    'invoice_number' => $expenseCNTransaction->voucher_number,
                    'bill_type' => PaymentTransaction::EXPENSE_CREDIT_NOTE_BILL_TYPE,
                    'paid_amount' => $paymentDetailData['amount'],
                    'discount' => 0,
                    'round_off' => 0,
                ]);

                $paymentDetailRecord->update(['payment_id' => $paymentTransaction->id]);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updatePaymentDetails($paymentDetails, $expenseCNTransaction)
    {
        try {
            $paymentDetailsIds = PaymentDetailsForExpenseCreditNoteTransaction::where('expense_cn_id', $expenseCNTransaction->id)->pluck('id')->toArray();
            $editedPaymentDetailsIds = Arr::pluck($paymentDetails, 'pd_id');
            $removePaymentDetailsIds = array_diff($paymentDetailsIds, $editedPaymentDetailsIds);

            /* Delete payment details */
            foreach ($removePaymentDetailsIds as $removePaymentDetailsId) {
                $removePaymentDetails = PaymentDetailsForExpenseCreditNoteTransaction::where('id', $removePaymentDetailsId)->first();

                $paymentTransaction = PaymentTransaction::where('payment_voucher_number', 'expense-credit-note/'.$this->oldExpenseCNTransaction->voucher_number)
                    ->where('ledger_id', $this->oldExpenseCNTransaction->supplier_id)
                    ->where('bank_cash_ledger_id', $removePaymentDetails->ledger_id)
                    ->whereId($removePaymentDetails->payment_id)
                    ->financialYearDate()?->forceDelete();

                $removePaymentDetails->delete();
            }

            foreach ($paymentDetails as $paymentDetail) {
                /* Prepare payment details data */
                $paymentDetailData = $this->preparePaymentDetailData($paymentDetail, $expenseCNTransaction);

                /* Update payment details */
                if (! isset($paymentDetail['pd_id']) || $paymentDetail['pd_id'] == null) {
                    $paymentDetailRecord = PaymentDetailsForExpenseCreditNoteTransaction::create($paymentDetailData);

                    $paymentTransaction = PaymentTransaction::create([
                        'company_id' => $expenseCNTransaction->company_id,
                        'date' => $paymentDetailData['date'],
                        'payment_voucher_number' => 'expense-credit-note/'.$expenseCNTransaction->voucher_number,
                        'is_default_created_by_transaction' => true,
                        'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                        'ledger_id' => $expenseCNTransaction->supplier_id,
                        'total_paid_amount' => $paymentDetailData['amount'],
                        'narration' => $expenseCNTransaction->narration ?? null,
                        'payment_mode' => $paymentDetailData['mode'],
                        'reference_number' => $paymentDetailData['reference_no'],
                        'created_by' => $expenseCNTransaction->created_by,
                    ]);

                    PaymentTransactionItem::create([
                        'pc_transaction_id' => $paymentTransaction->id,
                        'expense_credit_id' => $expenseCNTransaction->id,
                        'invoice_number' => $expenseCNTransaction->voucher_number,
                        'bill_type' => PaymentTransaction::EXPENSE_CREDIT_NOTE_BILL_TYPE,
                        'paid_amount' => $paymentDetailData['amount'],
                        'discount' => 0,
                        'round_off' => 0,
                    ]);
                } else {
                    $paymentDetailRecord = PaymentDetailsForExpenseCreditNoteTransaction::where('id', $paymentDetail['pd_id'])->first();

                    if (! empty($paymentDetailRecord)) {
                        $paymentDetailRecord->update($paymentDetailData);
                    }

                    $paymentTransaction = PaymentTransaction::where('payment_voucher_number', 'expense-credit-note/'.$this->oldExpenseCNTransaction->voucher_number)
                        ->where('ledger_id', $this->oldExpenseCNTransaction->supplier_id)
                        ->whereId($paymentDetailRecord->payment_id)
                        ->first();

                    if (! empty($paymentTransaction)) {
                        $paymentTransaction->update([
                            'payment_voucher_number' => 'expense-credit-note/'.$expenseCNTransaction->voucher_number,
                            'date' => $paymentDetailData['date'],
                            'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                            'ledger_id' => $expenseCNTransaction->supplier_id,
                            'total_paid_amount' => $paymentDetailData['amount'],
                            'narration' => $expenseCNTransaction->narration ?? null,
                            'payment_mode' => $paymentDetailData['mode'],
                            'reference_number' => $paymentDetailData['reference_no'],
                        ]);

                        $paymentTransactionItem = PaymentTransactionItem::where('pc_transaction_id', $paymentTransaction->id)->first();
                        if (! empty($paymentTransactionItem)) {
                            $paymentTransactionItem->update([
                                'invoice_number' => $expenseCNTransaction->voucher_number,
                                'paid_amount' => $paymentDetailData['amount'],
                            ]);
                        }
                    }
                }

                $paymentDetailRecord->update(['payment_id' => $paymentTransaction->id]);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function preparePaymentDetailData($paymentDetail, $expenseCNTransaction)
    {
        return [
            'expense_cn_id' => $expenseCNTransaction->id,
            'ledger_id' => $paymentDetail['pd_ledger_id'],
            'date' => Carbon::parse($paymentDetail['pd_date']),
            'amount' => $paymentDetail['pd_amount'],
            'mode' => $paymentDetail['pd_mode'] ?? null,
            'reference_no' => $paymentDetail['pd_reference_number'] ?? null,
        ];
    }
}
