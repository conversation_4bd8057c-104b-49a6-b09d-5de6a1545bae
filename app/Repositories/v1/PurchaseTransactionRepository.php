<?php

namespace App\Repositories\v1;

use App\Actions\CommonAction\AdvancePaymentAction;
use App\Actions\CommonAction\DirectCustomerSupplierCreate;
use App\Actions\CustomFields\StoreTransactionCustomFieldsAction;
use App\Actions\CustomFields\UpdateTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\StoreCFTransactionWiseAction;
use App\Actions\CustomFieldsItemMaster\Transaction\UpdateCFTransactionWiseAction;
use App\Models\AdditionalChargesForPurchaseTransaction;
use App\Models\AddLessForPurchaseTransaction;
use App\Models\ItemCustomField;
use App\Models\GstTax;
use App\Models\Ledger;
use App\Models\Master\ItemMaster;
use App\Models\PaymentDetailsForPurchaseTransaction;
use App\Models\PaymentTransaction;
use App\Models\PaymentTransactionItem;
use App\Models\PurchaseItemTransaction;
use App\Models\PurchaseLedgerTransaction;
use App\Models\PurchaseTransaction;
use App\Models\SettleAdvancePayment;
use App\Models\TaxClassificationDetails;
use App\Models\TransactionCustomField;
use App\Repositories\API\v1\BaseAPIRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Interface PurchaseTransactionRepository.
 */
class PurchaseTransactionRepository extends BaseAPIRepository
{
    public $classificationNatureType = null;

    public $isRCMApplicable = false;

    public $isGSTEnabled = null;

    public $oldPurchaseTransaction = null;

    public function model()
    {
        return PurchaseTransaction::class;
    }

    public function store($input)
    {
        try {
            DB::beginTransaction();
            unset($input['_token']);

            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['supplier_ledger_id'], Ledger::SUPPLIER);
                $input['supplier_ledger_id'] = $partyId;
            }

            /* Prepare Purchase transaction */
            $purchaseTransactionData = $this->preparePurchaseData($input);

            /* Create Purchase */
            $purchaseTransaction = PurchaseTransaction::create($purchaseTransactionData);

            /* Store Purchase Document */
            if (isset($input['upload_purchase_invoice']) && ! empty($input['upload_purchase_invoice'])) {
                foreach ($input['upload_purchase_invoice'] as $image) {
                    $purchaseTransaction->addMedia($image)->toMediaCollection(PurchaseTransaction::PURCHASE_INVOICE, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Store Purchase items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['purchase_item_type'] == PurchaseTransaction::ITEM_INVOICE) {
                $this->storeItems($input['items'], $purchaseTransaction);
            }
            if ($input['purchase_item_type'] == PurchaseTransaction::ACCOUNTING_INVOICE) {
                $this->storeLedgers($input['ledgers'], $purchaseTransaction);
            }

            /* Store Addresses */
            $this->storeAddresses($purchaseTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($purchaseTransaction->shipping_address_id) && ! empty($purchaseTransaction->dispatchAddress)) {
                $purchaseTransaction->update([
                    'shipping_address_id' => $purchaseTransaction->dispatchAddress->id, // in expense transaction dispatch address is shipping address
                ]);
            }

            /* Store Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->storeAdditionalCharges($input['additional_charges'], $purchaseTransaction->id);
            }

            /* Store Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->storeAddLess($input['add_less'], $purchaseTransaction->id);
            }

            /* Store Payment Details And Create Receipt */
            if (isset($input['payment_details']) && count($input['payment_details']) != 0) {
                $this->storePaymentDetails($input['payment_details'], $purchaseTransaction);
            }

            /* Settle Advance Payment */
            if (isset($input['advance_payment']) && count($input['advance_payment']) != 0) {
                AdvancePaymentAction::run($input['advance_payment'], $purchaseTransaction);
            }

            /* Store Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                StoreTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::PURCHASE,
                    $purchaseTransaction->id,
                    PurchaseTransaction::class
                );
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($purchaseTransaction);

            /* Update Purchase Order Status */
            updateMultiplePurchaseOrderStatus($purchaseTransaction->purchase_order_no, $purchaseTransaction->id);

            DB::commit();

            return $purchaseTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    public function update($input, $purchaseTransaction)
    {
        try {
            DB::beginTransaction();

            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* Old purchase transaction */
            $this->oldPurchaseTransaction = clone $purchaseTransaction;

            $oldPurchaseOrderNo = $purchaseTransaction->purchase_order_no;

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['supplier_ledger_id'], Ledger::SUPPLIER);
                $input['supplier_ledger_id'] = $partyId;
            }

            /* Prepare purchase transaction */
            $input['created_at'] = $this->oldPurchaseTransaction->created_at;
            $purchaseTransactionData = $this->preparePurchaseData($input);

            /* Update purchase transaction */
            /** @var PurchaseTransaction $purchaseTransaction */
            $purchaseTransaction->update($purchaseTransactionData);

            /* Update Purchase Document */
            if (isset($input['upload_purchase_invoice']) && ! empty($input['upload_purchase_invoice'])) {
                foreach ($input['upload_purchase_invoice'] as $image) {
                    $purchaseTransaction->addMedia($image)->toMediaCollection(PurchaseTransaction::PURCHASE_INVOICE, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Update purchase items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['purchase_item_type'] == PurchaseTransaction::ITEM_INVOICE && $this->oldPurchaseTransaction->purchase_item_type == $input['purchase_item_type']) {
                $this->updateItems($input['items'], $purchaseTransaction);
            } elseif ($input['purchase_item_type'] == PurchaseTransaction::ACCOUNTING_INVOICE && $this->oldPurchaseTransaction->purchase_item_type == $input['purchase_item_type']) {
                $this->updateLedgers($input['ledgers'], $purchaseTransaction);
            } elseif ($input['purchase_item_type'] == PurchaseTransaction::ITEM_INVOICE && $this->oldPurchaseTransaction->purchase_item_type != $input['purchase_item_type']) {
                $purchaseTransaction->purchaseTransactionLedger()->delete();
                $this->storeItems($input['items'], $purchaseTransaction);
            } elseif ($input['purchase_item_type'] == PurchaseTransaction::ACCOUNTING_INVOICE && $this->oldPurchaseTransaction->purchase_item_type != $input['purchase_item_type']) {
                $purchaseTransaction->purchaseTransactionItems()->delete();
                $this->storeLedgers($input['ledgers'], $purchaseTransaction);
            }

            /* Update Addresses */
            $this->updateAddresses($purchaseTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($purchaseTransaction->shipping_address_id) && ! empty($purchaseTransaction->dispatchAddress)) {
                $purchaseTransaction->update([
                    'shipping_address_id' => $purchaseTransaction->dispatchAddress->id, // in expense transaction dispatch address is shipping address
                ]);
            }

            /* Update or Create Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->updateAdditionalCharges($input['additional_charges'], $purchaseTransaction->id);
            } else {
                $purchaseTransaction->additionalCharges()->delete();
            }

            /* Update or Create Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->updateAddLess($input['add_less'], $purchaseTransaction->id);
            } else {
                $purchaseTransaction->addLess()->delete();
            }

            /* Update or Create Payment Details And Receipt */
            if (isset($input['payment_details']) && count($input['payment_details']) != 0) {
                $this->updatePaymentDetails($input['payment_details'], $purchaseTransaction);
            } else {
                $ledgerIdsForRemovePayments = $purchaseTransaction->paymentDetails->pluck('ledger_id')->toArray();

                PaymentTransaction::where('payment_voucher_number', 'purchase/'.$this->oldPurchaseTransaction->voucher_number)
                    ->where('ledger_id', $this->oldPurchaseTransaction->supplier_ledger_id)
                    ->whereIn('bank_cash_ledger_id', $ledgerIdsForRemovePayments)
                    ->financialYearDate()?->forceDelete();

                $purchaseTransaction->paymentDetails()->delete();
            }

            /* Settle Advance Payment */
            if (isset($input['advance_payment']) && count($input['advance_payment']) != 0) {
                (new AdvancePaymentAction)->updateAdvancePayment($input['advance_payment'], $purchaseTransaction);
            } else {
                SettleAdvancePayment::whereModelId($purchaseTransaction->id)->whereModelType(get_class($purchaseTransaction))->delete();
            }

            /* Update Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                UpdateTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::PURCHASE,
                    $purchaseTransaction->id,
                    PurchaseTransaction::class
                );
            } else {
                $purchaseTransaction->customFieldValues()->delete();
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($purchaseTransaction);

            /* Update Purchase Order Status */
            updateMultiplePurchaseOrderStatus($purchaseTransaction->purchase_order_no ?? $oldPurchaseOrderNo, $purchaseTransaction->id);

            DB::commit();

            return $purchaseTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    private function preparePurchaseData($input): array
    {
        /* Destructure input arrays for clarity */
        $brokerDetails = $input['broker_details'] ?? [];
        $transportDetails = $input['transport_details'] ?? [];
        $tcsDetails = $input['tcs_details'] ?? [];
        $tdsDetails = $input['tds_details'] ?? [];

        $shippingName = $input['shipping_address']['shipping_name'] ?? $input['shipping_name'] ?? null;
        $shippingGSTIN = $input['shipping_address']['shipping_gstin'] ?? $input['shipping_gstin'] ?? null;
        $addressName = $input['shipping_address']['address_name'] ?? $input['address_name'] ?? null;
        $partyNameSameAsAddressName = $input['shipping_address']['party_name_same_as_address_name'] ?? $input['party_name_same_as_address_name'] ?? false;
        if ($partyNameSameAsAddressName) {
            $addressName = $shippingName;
        }

        return [
            'company_id' => $input['company']->id,
            'voucher_date' => Carbon::parse($input['voucher_date']),
            'voucher_number' => $input['voucher_number'] ?? null,
            'payment_mode' => null, // Removed field
            'payment_type_ledger_id' => null, // Removed field
            'sale_number' => $input['sale_number'] ?? null,
            'date_of_invoice' => Carbon::parse($input['date_of_invoice']) ?? null,
            'supplier_ledger_id' => $input['supplier_ledger_id'],
            'gstin' => $this->isGSTEnabled && isset($input['gstin']) ? $input['gstin'] : null,
            'broker_id' => $brokerDetails['broker_id'] ?? null,
            'brokerage_for_sale' => $brokerDetails['brokerage_for_sale'] ?? null,
            'brokerage_on_value_type' => $brokerDetails['brokerage_on_value_type'] ?? null,
            'transport_id' => $transportDetails['transport_id'] ?? null,
            'transporter_document_number' => $transportDetails['transporter_document_number'] ?? null,
            'transporter_document_date' => isset($transportDetails['transporter_document_date']) ? Carbon::parse($transportDetails['transporter_document_date']) : null,
            'transporter_vehicle_number' => $transportDetails['transporter_vehicle_number'] ?? null,
            'shipping_name' => $shippingName,
            'shipping_gstin' => $shippingGSTIN,
            'address_name' => $addressName,
            'party_name_same_as_address_name' => $partyNameSameAsAddressName,
            'purchase_item_type' => $input['purchase_item_type'],
            'tcs_tax_id' => $tcsDetails['tcs_tax_id'] ?? null,
            'tcs_rate' => $tcsDetails['tcs_rate'] ?? null,
            'tcs_amount' => $tcsDetails['tcs_amount'] ?? null,
            'ledger_of_tds' => $tdsDetails['tds_tax_id'] ?? null,
            'tds_rate' => $tdsDetails['tds_rate'] ?? null,
            'tds_amount' => $tdsDetails['tds_amount'] ?? null,
            'cgst' => $this->isGSTEnabled ? $input['cgst'] : 0,
            'sgst' => $this->isGSTEnabled ? $input['sgst'] : 0,
            'igst' => $this->isGSTEnabled ? $input['igst'] : 0,
            'rounding_amount' => $input['rounding_amount'] ?? null,
            'total' => $input['grand_total'],
            'grand_total' => $input['grand_total'],
            'taxable_value' => $input['taxable_value'],
            'gross_value' => $input['gross_value'],
            'narration' => $input['narration'] ?? null,
            'term_and_condition' => $input['term_and_condition'] ?? null,
            'shipping_address_id' => $input['shipping_address_id'] ?? null,
            'same_as_billing' => $input['same_as_billing'] ?? false,
            'is_gst_enabled' => $this->isGSTEnabled,
            'cess' => $this->isGSTEnabled ? $input['cess'] : 0,
            'is_cgst_sgst_igst_calculated' => $input['is_cgst_sgst_igst_calculated'],
            'is_gst_na' => $input['is_gst_na'],
            'is_rcm_applicable' => $input['is_rcm_applicable'] ?? false,
            'created_by' => getLoginUser()->id,
            'via_api' => $input['via_api'] ?? false,
            'is_import' => false,
            'purchase_order_no' => $input['purchase_order_no'] ?? null,
            'is_round_off_not_changed' => $input['is_round_off_not_changed'] ?? true,
            'ocr_id' => $input['ocr_id'] ?? null,
            'created_at' => $input['created_at'] ?? Carbon::now(), // this is for import excel
            'updated_at' => $input['updated_at'] ?? Carbon::now(), // this is for import excel
            'round_off_method' => $input['round_off_method'],
        ];
    }

    private function storeItems($items, $purchaseTransaction)
    {
        try {
            foreach ($items as $key => $item) {
                /* Prepare item data */
                $purchaseTransactionItemData = $this->prepareItemData($item, $purchaseTransaction);

                /* Store item */
                $purchaseTransactionItem = PurchaseItemTransaction::create($purchaseTransactionItemData);

                /* Store Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    StoreCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::PURCHASE,
                        $purchaseTransactionItem->id,
                        PurchaseItemTransaction::class
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateItems($items, $purchaseTransaction)
    {
        try {
            /* Get item ids to be removed */
            $itemIds = PurchaseItemTransaction::wherePurchaseTransactionId($purchaseTransaction->id)->pluck('id')->toArray();
            $editPurchaseItemIds = Arr::pluck($items, value: 'id') ?? [];
            $removeItemIds = array_diff($itemIds, $editPurchaseItemIds);

            /* Delete removed items */
            PurchaseItemTransaction::whereIn('id', array_values($removeItemIds))?->delete();

            foreach ($items as $key => $item) {
                /* Prepare item data */
                $purchaseTransactionItemData = $this->prepareItemData($item, $purchaseTransaction);

                /* Update or create item */
                if (! isset($item['id']) || $item['id'] == null) {
                    $purchaseTransactionItem = PurchaseItemTransaction::create($purchaseTransactionItemData);
                } else {
                    $purchaseTransactionItem = PurchaseItemTransaction::whereId($item['id'])->first();
                    if (! empty($purchaseTransactionItem)) {
                        $purchaseTransactionItem->update($purchaseTransactionItemData);
                    }
                }

                /* Update Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    UpdateCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::PURCHASE,
                        $purchaseTransactionItem->id,
                        PurchaseItemTransaction::class
                    );
                } else {
                    $purchaseTransactionItem->customFieldTransactionItemsValues()->delete();
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareItemData($item, $purchaseTransaction)
    {
        $cessRate = 0;
        $cessAmount = 0;
        if ($this->isGSTEnabled) {
            $itemMaster = ItemMaster::with(['model.gstGstCessRate'])->whereId($item['item_id'])->first();
            $cessRate = $itemMaster->model->gstGstCessRate->rate ?? 0;
            $taxableValue = ($item['quantity'] * $item['rpu_without_gst']) - $item['total_discount_amount'];
            $cessAmount = ($taxableValue * $cessRate) / 100;
        }

        return [
            'purchase_transaction_id' => $purchaseTransaction->id,
            'item_id' => $item['item_id'],
            'additional_description' => $item['additional_description'] ?? null,
            'ledger_id' => $item['ledger_id'],
            'unit_id' => $item['unit_id'],
            'hsn_code' => $item['hsn_code'] ?? null,
            'quantity' => $item['quantity'],
            'free_quantity' => $item['free_quantity'] ?? null,
            'mrp' => $item['mrp'] ?? null,
            'with_tax' => $item['with_tax'] ?? false,
            'rpu_with_gst' => $item['rpu_with_gst'],
            'rpu_without_gst' => $item['rpu_without_gst'],
            'discount_type' => $item['discount_type'] ?? null,
            'discount_value' => $item['discount_value'] ?? null,
            'discount_type_2' => $item['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $item['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $item['total_discount_amount'] ?? 0,
            'gst_id' => $item['gst_tax'],
            'gst_tax_percentage' => $item['gst_tax_percentage'],
            'total' => $item['total'],
            'classification_nature_type' => $this->classificationNatureType,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_is_itc_applicable' => $item['classification_is_itc_applicable'] ?? true,
            'classification_igst_tax' => $this->isGSTEnabled ? $item['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $item['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $item['classification_sgst_tax'] : null,
            'classification_cess_tax' => $this->isGSTEnabled && isset($item['cess']) ? $item['cess'] : null,
            'consolidating_items_to_invoice' => $item['consolidating_items_to_invoice'] ?? false,
            'cess_rate' => $cessRate,
            'cess_amount' => $cessAmount,
            'taxable_amount' => $item['taxable_amount'] ?? 0,
            'decimal_places_for_quantity' => $item['decimal_places'] ?? 2,
            'decimal_places_for_rate' => $item['decimal_places_for_rate'] ?? 2,
        ];
    }

    private function storeLedgers($ledgers, $purchaseTransaction)
    {
        try {
            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $purchaseTransactionLedgerData = $this->prepareLedgerData($ledger, $purchaseTransaction);

                /* Store ledger */
                $purchaseTransactionLedger = PurchaseLedgerTransaction::create($purchaseTransactionLedgerData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateLedgers($ledgers, $purchaseTransaction)
    {
        try {

            $ledgerIds = PurchaseLedgerTransaction::wherePurchaseTransactionId($purchaseTransaction->id)->pluck('id')->toArray();
            $editPurchaseLedgerIds = Arr::pluck($ledgers, 'id') ?? [];
            $removeLedgerIds = array_diff($ledgerIds, $editPurchaseLedgerIds);

            /* Delete removed ledgers */
            PurchaseLedgerTransaction::whereIn('id', array_values($removeLedgerIds))?->delete();

            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $purchaseTransactionLedgerData = $this->prepareLedgerData($ledger, $purchaseTransaction);

                /* Update or create ledger */
                if (! isset($ledger['id']) || $ledger['id'] == null) {
                    $purchaseTransactionLedger = PurchaseLedgerTransaction::create($purchaseTransactionLedgerData);
                } else {
                    $purchaseTransactionLedger = PurchaseLedgerTransaction::whereId($ledger['id'])->first();
                    if (! empty($purchaseTransactionLedger)) {
                        $purchaseTransactionLedger->update($purchaseTransactionLedgerData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareLedgerData($ledger, $purchaseTransaction)
    {
        return [
            'purchase_transaction_id' => $purchaseTransaction->id,
            'ledger_id' => $ledger['ledger_id'],
            'additional_description' => $ledger['additional_description'] ?? null,
            'with_tax' => $ledger['with_tax'] ?? false,
            'rpu_with_gst' => $ledger['rpu_with_gst'],
            'rpu_without_gst' => $ledger['rpu_without_gst'],
            'discount_type' => $ledger['discount_type'] ?? null,
            'discount_value' => $ledger['discount_value'] ?? null,
            'discount_type_2' => $ledger['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $ledger['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $ledger['total_discount_amount'] ?? 0,
            'gst_id' => $ledger['gst_tax'],
            'gst_tax_percentage' => $ledger['gst_tax_percentage'],
            'total' => $ledger['total'],
            'classification_igst_tax' => $this->isGSTEnabled ? $ledger['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $ledger['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $ledger['classification_sgst_tax'] : null,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_nature_type' => $this->classificationNatureType,
            'classification_is_itc_applicable' => $ledger['classification_is_itc_applicable'] ?? true,
            'taxable_amount' => $ledger['taxable_amount'] ?? 0,
        ];
    }

    private function storeAddresses($purchaseTransaction, $input)
    {
        try {
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            $addresses = [];

            if ($billingAddress) {
                $addresses[] = $this->prepareAddressData($purchaseTransaction, $billingAddress, PurchaseTransaction::BILLING_ADDRESS);
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $addresses[] = $this->prepareAddressData($purchaseTransaction, $shippingAddress, PurchaseTransaction::SHIPPING_ADDRESS);
                }
            }

            if (! empty($addresses)) {
                $purchaseTransaction->addresses()->createMany($addresses);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddresses($purchaseTransaction, $input)
    {
        try {
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            if ($billingAddress) {
                $purchaseTransaction->addresses()->updateOrCreate(
                    ['address_type' => PurchaseTransaction::BILLING_ADDRESS, 'model_id' => $purchaseTransaction->id],
                    $this->prepareAddressData($purchaseTransaction, $billingAddress, PurchaseTransaction::BILLING_ADDRESS)
                );
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $purchaseTransaction->addresses()->updateOrCreate(
                        ['address_type' => PurchaseTransaction::SHIPPING_ADDRESS, 'model_id' => $purchaseTransaction->id],
                        $this->prepareAddressData($purchaseTransaction, $shippingAddress, PurchaseTransaction::SHIPPING_ADDRESS)
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddressData($purchaseTransaction, $address, $addressType)
    {
        return [
            'address_1' => $address['address_1'] ?? null,
            'address_2' => $address['address_2'] ?? null,
            'country_id' => $address['country_id'] ?? null,
            'state_id' => $address['state_id'] ?? null,
            'city_id' => $address['city_id'] ?? null,
            'pin_code' => $address['pin_code'] ?? null,
            'model_id' => $purchaseTransaction->id,
            'model_type' => PurchaseTransaction::class,
            'address_type' => $addressType,
        ];
    }

    private function storeAdditionalCharges($additionChargesData, $purchaseTransactionId)
    {
        try {
            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $purchaseTransactionId);

                /* Store additional charge */
                AdditionalChargesForPurchaseTransaction::create($additionChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAdditionalCharges($additionChargesData, $purchaseTransactionId)
    {
        try {
            $additionalChargesIds = AdditionalChargesForPurchaseTransaction::where('purchase_transaction_id', $purchaseTransactionId)->pluck('id')->toArray();
            $editedAdditionalChargesIds = Arr::pluck($additionChargesData, 'id');
            $removeAdditionalChargesIds = array_diff($additionalChargesIds, $editedAdditionalChargesIds);

            /* Delete additional charges */
            AdditionalChargesForPurchaseTransaction::whereIn('id', array_values($removeAdditionalChargesIds))?->delete();

            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $purchaseTransactionId);

                /* Update additional charge */
                if (! isset($additionCharge['id']) || $additionCharge['id'] == null) {
                    AdditionalChargesForPurchaseTransaction::create($additionChargeData);
                } else {
                    $additionalChargeRecord = AdditionalChargesForPurchaseTransaction::where('id', $additionCharge['id'])->first();
                    if (! empty($additionalChargeRecord)) {
                        $additionalChargeRecord->update($additionChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAdditionalChargesData($additionCharge, $purchaseTransactionId)
    {
        if ($this->isGSTEnabled) {
            $gst = null;
            if (isset($additionCharge['ac_gst_rate_id'])) {
                $gst = GstTax::whereId($additionCharge['ac_gst_rate_id'])->first();
            }
            $gstTaxPercentage = ! empty($gst) ? $gst->tax_rate : null;
        }

        return [
            'purchase_transaction_id' => $purchaseTransactionId,
            'ledger_id' => $additionCharge['ac_ledger_id'],
            'charge_type' => $additionCharge['ac_type'],
            'value' => $additionCharge['ac_value'],
            'gst_rate_id' => $this->isGSTEnabled && ! empty($gst) ? $gst->id : null,
            'gst_percentage' => $this->isGSTEnabled ? $gstTaxPercentage : null,
            'total_without_tax' => $additionCharge['ac_total_without_tax'],
            'total' => $additionCharge['ac_total'],
        ];
    }

    private function storeAddLess($addLessData, $purchaseTransactionId)
    {
        try {

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $purchaseTransactionId);

                /* Store add less */
                AddLessForPurchaseTransaction::create($addLessChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddLess($addLessData, $purchaseTransactionId)
    {
        try {
            $addLessIds = AddLessForPurchaseTransaction::where('purchase_transaction_id', $purchaseTransactionId)->pluck('id')->toArray();
            $editedAddLessIds = Arr::pluck($addLessData, 'id');
            $removeAddLessIds = array_diff($addLessIds, $editedAddLessIds);

            /* Delete add less */
            AddLessForPurchaseTransaction::whereIn('id', array_values($removeAddLessIds))?->delete();

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $purchaseTransactionId);

                /* Update add less */
                if (! isset($addLessCharge['id']) || $addLessCharge['id'] == null) {
                    AddLessForPurchaseTransaction::create($addLessChargeData);
                } else {
                    $addLessRecord = AddLessForPurchaseTransaction::where('id', $addLessCharge['id'])->first();
                    if (! empty($addLessRecord)) {
                        $addLessRecord->update($addLessChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddLessData($addLessCharge, $purchaseTransactionId)
    {
        return [
            'purchase_transaction_id' => $purchaseTransactionId,
            'ledger_id' => $addLessCharge['al_ledger_id'],
            'is_show_in_print' => $addLessCharge['al_is_show_in_print'] ?? false,
            'type' => $addLessCharge['al_type'],
            'value' => $addLessCharge['al_value'],
            'total' => $addLessCharge['al_total'],
        ];
    }

    private function storePaymentDetails($paymentDetails, $purchaseTransaction)
    {
        try {
            foreach ($paymentDetails as $paymentDetail) {
                /* Prepare payment detail data */
                $paymentDetailData = $this->preparePaymentDetailData($paymentDetail, $purchaseTransaction);

                /* Store payment detail */
                $paymentDetailRecord = PaymentDetailsForPurchaseTransaction::create($paymentDetailData);

                $paymentTransaction = PaymentTransaction::create([
                    'company_id' => $purchaseTransaction->company_id,
                    'date' => $paymentDetailData['date'],
                    'payment_voucher_number' => 'purchase/'.$purchaseTransaction->voucher_number,
                    'is_default_created_by_transaction' => true,
                    'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                    'ledger_id' => $purchaseTransaction->supplier_ledger_id,
                    'total_paid_amount' => $paymentDetailData['amount'],
                    'narration' => $purchaseTransaction->narration ?? null,
                    'payment_mode' => $paymentDetailData['mode'],
                    'reference_number' => $paymentDetailData['reference_no'],
                    'created_by' => $purchaseTransaction->created_by,
                ]);

                PaymentTransactionItem::create([
                    'pc_transaction_id' => $paymentTransaction->id,
                    'purchase_id' => $purchaseTransaction->id,
                    'invoice_number' => $purchaseTransaction->voucher_number,
                    'bill_type' => PaymentTransaction::PURCHASE_BILL_TYPE,
                    'paid_amount' => $paymentDetailData['amount'],
                    'discount' => 0,
                    'round_off' => 0,
                ]);

                $paymentDetailRecord->update(['payment_id' => $paymentTransaction->id]);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updatePaymentDetails($paymentDetails, $purchaseTransaction)
    {
        try {
            $paymentDetailsIds = PaymentDetailsForPurchaseTransaction::where('purchase_transaction_id', $purchaseTransaction->id)->pluck('id')->toArray();
            $editedPaymentDetailsIds = Arr::pluck($paymentDetails, 'pd_id');
            $removePaymentDetailsIds = array_diff($paymentDetailsIds, $editedPaymentDetailsIds);

            /* Delete payment details */
            foreach ($removePaymentDetailsIds as $removePaymentDetailsId) {
                $removePaymentDetails = PaymentDetailsForPurchaseTransaction::where('id', $removePaymentDetailsId)->first();

                $paymentTransaction = PaymentTransaction::where('payment_voucher_number', 'purchase/'.$this->oldPurchaseTransaction->voucher_number)
                    ->where('ledger_id', $this->oldPurchaseTransaction->supplier_ledger_id)
                    ->where('bank_cash_ledger_id', $removePaymentDetails->ledger_id)
                    ->whereId($removePaymentDetails->payment_id)
                    ->financialYearDate()?->forceDelete();

                $removePaymentDetails->delete();
            }

            foreach ($paymentDetails as $paymentDetail) {
                /* Prepare payment details data */
                $paymentDetailData = $this->preparePaymentDetailData($paymentDetail, $purchaseTransaction);

                /* Update payment details */
                if (! isset($paymentDetail['pd_id']) || $paymentDetail['pd_id'] == null) {
                    $paymentDetailRecord = PaymentDetailsForPurchaseTransaction::create($paymentDetailData);

                    $paymentTransaction = PaymentTransaction::create([
                        'company_id' => $purchaseTransaction->company_id,
                        'date' => $paymentDetailData['date'],
                        'payment_voucher_number' => 'purchase/'.$purchaseTransaction->voucher_number,
                        'is_default_created_by_transaction' => true,
                        'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                        'ledger_id' => $purchaseTransaction->supplier_ledger_id,
                        'total_paid_amount' => $paymentDetailData['amount'],
                        'narration' => $purchaseTransaction->narration ?? null,
                        'payment_mode' => $paymentDetailData['mode'],
                        'reference_number' => $paymentDetailData['reference_no'],
                        'created_by' => $purchaseTransaction->created_by,
                    ]);

                    PaymentTransactionItem::create([
                        'pc_transaction_id' => $paymentTransaction->id,
                        'purchase_id' => $purchaseTransaction->id,
                        'invoice_number' => $purchaseTransaction->voucher_number,
                        'bill_type' => PaymentTransaction::PURCHASE_BILL_TYPE,
                        'paid_amount' => $paymentDetailData['amount'],
                        'discount' => 0,
                        'round_off' => 0,
                    ]);
                } else {
                    $paymentDetailRecord = PaymentDetailsForPurchaseTransaction::where('id', $paymentDetail['pd_id'])->first();
                    if (! empty($paymentDetailRecord)) {
                        $paymentDetailRecord->update($paymentDetailData);
                    }

                    $paymentTransaction = PaymentTransaction::where('payment_voucher_number', 'purchase/'.$this->oldPurchaseTransaction->voucher_number)
                        ->where('ledger_id', $this->oldPurchaseTransaction->supplier_ledger_id)
                        ->whereId($paymentDetailRecord->payment_id)
                        ->first();

                    if (! empty($paymentTransaction)) {
                        $paymentTransaction->update([
                            'payment_voucher_number' => 'purchase/'.$purchaseTransaction->voucher_number,
                            'date' => $paymentDetailData['date'],
                            'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                            'ledger_id' => $purchaseTransaction->supplier_ledger_id,
                            'total_received_amount' => $paymentDetailData['amount'],
                            'narration' => $purchaseTransaction->narration ?? null,
                            'payment_mode' => $paymentDetailData['mode'],
                            'reference_number' => $paymentDetailData['reference_no'],
                        ]);

                        $paymentTransactionItem = PaymentTransactionItem::where('pc_transaction_id', $paymentTransaction->id)->first();
                        if (! empty($paymentTransactionItem)) {
                            $paymentTransactionItem->update([
                                'invoice_number' => $purchaseTransaction->voucher_number,
                                'paid_amount' => $paymentDetailData['amount'],
                            ]);
                        }
                    }
                }

                $paymentDetailRecord->update(['payment_id' => $paymentTransaction->id]);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function preparePaymentDetailData($paymentDetail, $purchaseTransaction)
    {
        return [
            'purchase_transaction_id' => $purchaseTransaction->id,
            'ledger_id' => $paymentDetail['pd_ledger_id'],
            'date' => Carbon::parse($paymentDetail['pd_date']),
            'amount' => $paymentDetail['pd_amount'],
            'mode' => $paymentDetail['pd_mode'] ?? null,
            'reference_no' => $paymentDetail['pd_reference_number'] ?? null,
        ];
    }
}
