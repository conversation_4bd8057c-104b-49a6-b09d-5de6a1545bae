<?php

namespace App\Repositories\v1;

use App\Actions\CommonAction\AdvancePaymentAction;
use App\Actions\CommonAction\CheckPartyLedgerMobileNumberAction;
use App\Actions\CommonAction\DirectCustomerSupplierCreate;
use App\Actions\CustomFields\StoreTransactionCustomFieldsAction;
use App\Actions\CustomFields\UpdateTransactionCustomFieldsAction;
use App\Actions\CustomFieldsItemMaster\Transaction\StoreCFTransactionWiseAction;
use App\Actions\CustomFieldsItemMaster\Transaction\UpdateCFTransactionWiseAction;
use App\Models\AdditionalChargesForSalesTransaction;
use App\Models\AddLessForSalesTransaction;
use App\Models\GstTax;
use App\Models\ItemCustomField;
use App\Models\Ledger;
use App\Models\Master\Customer;
use App\Models\Master\ItemMaster;
use App\Models\PaymentDetailsForSalesTransaction;
use App\Models\ReceiptTransaction;
use App\Models\ReceiptTransactionItem;
use App\Models\SaleTransaction;
use App\Models\SaleTransactionItem;
use App\Models\SaleTransactionLedger;
use App\Models\SettleAdvancePayment;
use App\Models\TaxClassificationDetails;
use App\Models\TransactionCustomField;
use App\Repositories\API\v1\BaseAPIRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Interface SaleTransactionRepository.
 */
class SaleTransactionRepository extends BaseAPIRepository
{
    public $classificationNatureType = null;

    public $isRCMApplicable = false;

    public $isGSTEnabled = null;

    public $oldSaleTransaction = null;

    public function model()
    {
        return SaleTransaction::class;
    }

    public function store($input)
    {
        try {
            DB::beginTransaction();
            unset($input['_token']);

            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* If is_create_party is true then create party */
            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['customer_ledger_id'], Ledger::CUSTOMER); //$this->createParty($input['customer_ledger_id']);
                $input['customer_ledger_id'] = $partyId;
            }

            /* Ledger mobile number */
            if (isset($input['party_phone_number'])) {
                $ledgerData = [
                    'ledger_id' => $input['customer_ledger_id'],
                    'phone_1' => $input['party_phone_number'],
                    'region_iso_1' => $input['region_iso'],
                    'region_code_1' => $input['region_code'],
                ];
                CheckPartyLedgerMobileNumberAction::run($ledgerData);
            }

            /* Prepare sale transaction */
            $saleTransactionData = $this->prepareSaleData($input);
            $saleTransactionData['template_id'] = $input['template_id'] ?? null;
            $saleTransactionData['is_recurring_approve'] = $input['is_recurring_approve'] ?? null;

            /* Create sale */
            $saleTransaction = SaleTransaction::create($saleTransactionData);

            /* Store Sale Document */
            if (isset($input['sale_document']) && ! empty($input['sale_document'])) {
                foreach ($input['sale_document'] as $image) {
                    $saleTransaction->addMedia($image)->toMediaCollection(SaleTransaction::SALE_DOCUMENT, config('app.media_disc'));
                }
            }

            /* Get & Store Recurring Template Document */
            if (isset($input['recurring_template_documents']) && ! empty($input['recurring_template_documents'])) {
                foreach ($input['recurring_template_documents'] as $mediaUrl) {
                    $saleTransaction->addMediaFromUrl($mediaUrl)->toMediaCollection(SaleTransaction::SALE_DOCUMENT, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Store sale items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['sales_item_type'] == SaleTransaction::ITEM_INVOICE) {
                $this->storeItems($input['items'], $saleTransaction);
            }
            if ($input['sales_item_type'] == SaleTransaction::ACCOUNTING_INVOICE) {
                $this->storeLedgers($input['ledgers'], $saleTransaction);
            }

            /* Store Addresses */
            $this->storeAddresses($saleTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($saleTransaction->shipping_address_id) && ! empty($saleTransaction->shippingAddress)) {
                $saleTransaction->update([
                    'shipping_address_id' => $saleTransaction->shippingAddress->id,
                ]);
            }

            /* Store Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->storeAdditionalCharges($input['additional_charges'], $saleTransaction->id);
            }

            /* Store Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->storeAddLess($input['add_less'], $saleTransaction->id);
            }

            /* Store Payment Details And Create Receipt */
            if (isset($input['payment_details']) && count($input['payment_details']) != 0) {
                $this->storePaymentDetails($input['payment_details'], $saleTransaction);
            }

            /* Settle Advance Payment */
            if (isset($input['advance_payment']) && count($input['advance_payment']) != 0) {
                AdvancePaymentAction::run($input['advance_payment'], $saleTransaction);
            }

            /* Store Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                StoreTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::SALE,
                    $saleTransaction->id,
                    SaleTransaction::class
                );
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($saleTransaction);

            /* Update Estimate Quote Transaction Status Column Value */
            updateMultipleEstimateQuoteStatus($saleTransaction['estimate_quote_no'], $saleTransaction->id);

            /* Update Delivery Challan Transaction Status Column Value */
            updateMultipleDeliveryChallanStatus($saleTransaction['delivery_challan_no'], $saleTransaction->id);

            DB::commit();

            return $saleTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    public function update($input, $saleTransaction)
    {
        try {
            DB::beginTransaction();

            $this->isGSTEnabled = $input['is_gst_enabled'];

            /* Old sale transaction */
            $this->oldSaleTransaction = clone $saleTransaction;

            $oldEstimateQuoteNo = $saleTransaction->estimate_quote_no;

            $oldDeliveryChallanNo = $saleTransaction->delivery_challan_no;

            if (isset($input['is_create_party']) && $input['is_create_party']) {
                if (! getLoginUser()->can('mobile_company_add_new_account_masters')) {
                    throw new UnprocessableEntityHttpException('You do not have permission to create new party.');
                }
                $partyId = DirectCustomerSupplierCreate::run($input['customer_ledger_id'], Ledger::CUSTOMER); //$this->createParty($input['customer_ledger_id']);
                $input['customer_ledger_id'] = $partyId;
            }

            /* Ledger mobile number */
            if (isset($input['party_phone_number'])) {
                $ledgerData = [
                    'ledger_id' => $input['customer_ledger_id'],
                    'phone_1' => $input['party_phone_number'],
                    'region_iso_1' => $input['region_iso'],
                    'region_code_1' => $input['region_code'],
                ];
                CheckPartyLedgerMobileNumberAction::run($ledgerData);
            }

            /* Prepare sale transaction */
            $input['created_at'] = $this->oldSaleTransaction->created_at;
            $input['vastra_delivery_challan_id'] = $this->oldSaleTransaction->vastra_delivery_challan_id ? $this->oldSaleTransaction->vastra_delivery_challan_id : null;

            $saleTransactionData = $this->prepareSaleData($input);
            unset($saleTransactionData['invoice_number']);
            $saleTransactionData['via_api'] = $this->oldSaleTransaction->via_api;

            /* Update sale transaction */
            /** @var SaleTransaction $saleTransaction */
            $saleTransaction->update($saleTransactionData);
            /* Update Sale Document */
            if (isset($input['sale_document']) && ! empty($input['sale_document'])) {
                foreach ($input['sale_document'] as $image) {
                    $saleTransaction->addMedia($image)->toMediaCollection(SaleTransaction::SALE_DOCUMENT, config('app.media_disc'));
                }
            }

            if ($this->isGSTEnabled) {
                /* Update sale items or ledgers based on invoice type */
                $this->classificationNatureType = TaxClassificationDetails::whereName($input['main_classification_nature_type'] ?? null)->first()->id ?? null;
                $this->isRCMApplicable = $input['is_rcm_applicable'] ?? false;
            }

            if ($input['sales_item_type'] == SaleTransaction::ITEM_INVOICE && $this->oldSaleTransaction->sales_item_type == $input['sales_item_type']) {
                $this->updateItems($input['items'], $saleTransaction);
            } elseif ($input['sales_item_type'] == SaleTransaction::ACCOUNTING_INVOICE && $this->oldSaleTransaction->sales_item_type == $input['sales_item_type']) {
                $this->updateLedgers($input['ledgers'], $saleTransaction);
            } elseif ($input['sales_item_type'] == SaleTransaction::ITEM_INVOICE && $this->oldSaleTransaction->sales_item_type != $input['sales_item_type']) {
                $saleTransaction->saleLedgers()->delete();
                $this->storeItems($input['items'], $saleTransaction);
            } elseif ($input['sales_item_type'] == SaleTransaction::ACCOUNTING_INVOICE && $this->oldSaleTransaction->sales_item_type != $input['sales_item_type']) {
                $saleTransaction->saleItems()->delete();
                $this->storeLedgers($input['ledgers'], $saleTransaction);
            }

            /* Update Addresses */
            $this->updateAddresses($saleTransaction, $input);

            /* This is update shipping address id bcz when excel import & frontside local shipping address */
            if (empty($saleTransaction->shipping_address_id) && ! empty($saleTransaction->shippingAddress)) {
                $saleTransaction->update([
                    'shipping_address_id' => $saleTransaction->shippingAddress->id,
                ]);
            }

            /* Update or Create Additional Charges */
            if (isset($input['additional_charges']) && count($input['additional_charges']) != 0) {
                $this->updateAdditionalCharges($input['additional_charges'], $saleTransaction->id);
            } else {
                $saleTransaction->additionalCharges()->delete();
            }

            /* Update or Create Add Less */
            if (isset($input['add_less']) && count($input['add_less']) != 0) {
                $this->updateAddLess($input['add_less'], $saleTransaction->id);
            } else {
                $saleTransaction->addLess()->delete();
            }

            /* Update or Create Payment Details And Receipt */
            if (isset($input['payment_details']) && count($input['payment_details']) != 0) {
                $this->updatePaymentDetails($input['payment_details'], $saleTransaction);
            } else {
                $ledgerIdsForRemoveReceipts = $saleTransaction->paymentDetails->pluck('ledger_id')->toArray();

                ReceiptTransaction::where('receipt_number', 'sale/'.$this->oldSaleTransaction->full_invoice_number)
                    ->where('ledger_id', $this->oldSaleTransaction->customer_ledger_id)
                    ->whereIn('bank_cash_ledger_id', $ledgerIdsForRemoveReceipts)
                    ->financialYearDate()?->forceDelete();

                $saleTransaction->paymentDetails()->delete();
            }

            /* Settle Advance Payment */
            if (isset($input['advance_payment']) && count($input['advance_payment']) != 0) {
                (new AdvancePaymentAction)->updateAdvancePayment($input['advance_payment'], $saleTransaction);
            } else {
                SettleAdvancePayment::whereModelId($saleTransaction->id)->whereModelType(get_class($saleTransaction))->delete();
            }

            /* Update or Create Custom Fields */
            if (isset($input['custom_fields']) && count($input['custom_fields']) != 0) {
                UpdateTransactionCustomFieldsAction::run(
                    $input['custom_fields'],
                    TransactionCustomField::SALE,
                    $saleTransaction->id,
                    SaleTransaction::class
                );
            } else {
                $saleTransaction->customFieldValues()->delete();
            }

            /* Update Taxable Value, Payment Status And Due Amount Column Value */
            updateFieldsValue($saleTransaction);

            /* Update Estimate Quote Transaction Status Column Value */
            updateMultipleEstimateQuoteStatus($saleTransaction->estimate_quote_no ?? $oldEstimateQuoteNo, $saleTransaction->id);

            /* Update Delivery Challan Transaction Status Column Value */
            updateMultipleDeliveryChallanStatus($saleTransaction->delivery_challan_no ?? $oldDeliveryChallanNo, $saleTransaction->id);

            DB::commit();

            return $saleTransaction;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw $e;
        }
    }

    private function prepareSaleData($input): array
    {
        /* Destructure input arrays for clarity */
        $otherDetails = $input['other_details'] ?? [];
        $brokerDetails = $input['broker_details'] ?? [];
        $transportDetails = $input['transport_details'] ?? [];
        $ewayBillDetails = $input['eway_bill_details'] ?? [];
        $tcsDetails = $input['tcs_details'] ?? [];
        $tdsDetails = $input['tds_details'] ?? [];

        /* calculate credit period date */
        $creditPeriod = $otherDetails['credit_period'] ?? null;
        $creditPeriodType = $otherDetails['credit_period_type'] ?? null;
        $invoiceDate = Carbon::parse($input['date']);
        $creditPeriodDueDate = $creditPeriod != null && $creditPeriodType != null ? calculateCreditPeriodDate($creditPeriod, $creditPeriodType, $invoiceDate) : null;

        $shippingName = $input['shipping_address']['shipping_name'] ?? $input['shipping_name'] ?? null;
        $shippingGSTIN = $input['shipping_address']['shipping_gstin'] ?? $input['shipping_gstin'] ?? null;
        $addressName = $input['shipping_address']['address_name'] ?? $input['address_name'] ?? null;
        $partyNameSameAsAddressName = $input['shipping_address']['party_name_same_as_address_name'] ?? $input['party_name_same_as_address_name'] ?? false;
        if ($partyNameSameAsAddressName) {
            $addressName = $shippingName;
        }

        return [
            'company_id' => $input['company']->id,
            'payment_mode' => null, // Removed field
            'payment_type_ledger_id' => null, // Removed field
            'full_invoice_number' => $input['full_invoice_number'],
            'invoice_number' => $input['invoice_number'],
            'date' => Carbon::parse($input['date']),
            'customer_ledger_id' => $input['customer_ledger_id'],
            'party_phone_number' => $input['party_phone_number'] ?? null,
            'region_iso' => $input['region_iso'] ?? null,
            'region_code' => $input['region_code'] ?? null,
            'gstin' => $this->isGSTEnabled && isset($input['gstin']) ? $input['gstin'] : null,
            'broker_id' => $brokerDetails['broker_id'] ?? null,
            'brokerage_for_sale' => $brokerDetails['brokerage_for_sale'] ?? null,
            'brokerage_on_value_type' => $brokerDetails['brokerage_on_value_type'] ?? null,
            'credit_period' => $creditPeriod,
            'credit_period_type' => $creditPeriodType,
            'credit_period_due_date' => $creditPeriodDueDate,
            'transport_id' => $transportDetails['transport_id'] ?? null,
            'transporter_document_number' => $transportDetails['transporter_document_number'] ?? null,
            'transporter_document_date' => isset($transportDetails['transporter_document_date']) ? Carbon::parse($transportDetails['transporter_document_date']) : null,
            'transporter_vehicle_number' => $transportDetails['transporter_vehicle_number'] ?? null,
            'shipping_name' => $shippingName,
            'shipping_gstin' => $shippingGSTIN,
            'address_name' => $addressName,
            'party_name_same_as_address_name' => $partyNameSameAsAddressName,
            'po_no' => $otherDetails['po_no'] ?? null,
            'po_date' => isset($otherDetails['po_date']) ? Carbon::parse($otherDetails['po_date']) : null,
            'eway_bill_number' => $ewayBillDetails['eway_bill_number'] ?? null,
            'eway_bill_date' => isset($ewayBillDetails['eway_bill_date']) ? Carbon::parse($ewayBillDetails['eway_bill_date']) : null,
            'sales_item_type' => $input['sales_item_type'],
            'tcs_tax_id' => $tcsDetails['tcs_tax_id'] ?? null,
            'tcs_rate' => $tcsDetails['tcs_rate'] ?? null,
            'tcs_amount' => $tcsDetails['tcs_amount'] ?? null,
            'tds_tax_id' => $tdsDetails['tds_tax_id'] ?? null,
            'tds_rate' => $tdsDetails['tds_rate'] ?? null,
            'tds_amount' => $tdsDetails['tds_amount'] ?? null,
            'cgst' => $this->isGSTEnabled ? $input['cgst'] : 0,
            'sgst' => $this->isGSTEnabled ? $input['sgst'] : 0,
            'igst' => $this->isGSTEnabled ? $input['igst'] : 0,
            'rounding_amount' => $input['rounding_amount'] ?? null,
            'total' => $input['grand_total'],
            'grand_total' => $input['grand_total'],
            'taxable_value' => $input['taxable_value'],
            'gross_value' => $input['gross_value'],
            'narration' => $input['narration'] ?? null,
            'term_and_condition' => $input['term_and_condition'] ?? null,
            'dispatch_address_id' => $input['dispatch_address_id'] ?? null,
            'shipping_address_id' => $input['shipping_address_id'] ?? null,
            'same_as_billing' => $input['same_as_billing'] ?? false,
            'is_gst_enabled' => $this->isGSTEnabled,
            'cess' => $this->isGSTEnabled ? $input['cess'] : 0,
            'is_cgst_sgst_igst_calculated' => $input['is_cgst_sgst_igst_calculated'],
            'is_gst_na' => $input['is_gst_na'],
            'created_by' => isset($input['created_by']) ? $input['created_by'] : getLoginUser()->id,
            'via_api' => $input['via_api'] ?? false,
            'estimate_quote_no' => $input['estimate_quote_no'] ?? null,
            'delivery_challan_no' => $input['delivery_challan_no'] ?? null,
            'is_import' => $input['is_import'] ?? false,
            'is_round_off_not_changed' => $input['is_round_off_not_changed'] ?? true,
            'vastra_delivery_challan_id' => isset($input['vastra_delivery_challan_id']) ? $input['vastra_delivery_challan_id'] : null,
            'created_at' => $input['created_at'] ?? Carbon::now(),
            'updated_at' => $input['updated_at'] ?? Carbon::now(),
            'round_off_method' => $input['round_off_method'],
            'bank_id' => $input['bank_id'] ?? null,
        ];
    }

    private function storeItems($items, $saleTransaction)
    {
        try {
            foreach ($items as $key => $item) {
                /* Prepare item data */
                $saleTransactionItemData = $this->prepareItemData($item, $saleTransaction);

                /* Store item */
                $saleTransactionItem = SaleTransactionItem::create($saleTransactionItemData);

                /* Store Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    StoreCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::SALE,
                        $saleTransactionItem->id,
                        SaleTransactionItem::class
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateItems($items, $saleTransaction)
    {
        try {
            /* Get item ids to be removed */
            $itemIds = SaleTransactionItem::whereSaleTransactionsId($saleTransaction->id)->pluck('id')->toArray();
            $editSaleItemIds = Arr::pluck($items, 'id') ?? [];
            $removeItemIds = array_diff($itemIds, $editSaleItemIds);

            /* Delete removed items */
            SaleTransactionItem::whereIn('id', array_values($removeItemIds))?->delete();

            foreach ($items as $key => $item) {
                /* Prepare item data */
                $saleTransactionItemData = $this->prepareItemData($item, $saleTransaction);

                /* Update or create item */
                if (! isset($item['id']) || $item['id'] == null) {
                    $saleTransactionItem = SaleTransactionItem::create($saleTransactionItemData);
                } else {
                    $saleTransactionItem = SaleTransactionItem::whereId($item['id'])->first();
                    if (! empty($saleTransactionItem)) {
                        $saleTransactionItem->update($saleTransactionItemData);
                    }
                }

                /* Update Custom Fields for Items */
                if (isset($item['custom_fields']) && count($item['custom_fields']) != 0) {
                    UpdateCFTransactionWiseAction::run(
                        $item['custom_fields'],
                        ItemCustomField::SALE,
                        $saleTransactionItem->id,
                        SaleTransactionItem::class
                    );
                } else {
                    $saleTransactionItem->customFieldTransactionItemsValues()->delete();
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareItemData($item, $saleTransaction)
    {
        $cessRate = 0;
        $cessAmount = 0;
        if ($this->isGSTEnabled) {
            $itemMaster = ItemMaster::with(['model.gstGstCessRate'])->whereId($item['item_id'])->first();
            $cessRate = $itemMaster->model->gstGstCessRate->rate ?? 0;
            $taxableValue = ($item['quantity'] * $item['rpu_without_gst']) - $item['total_discount_amount'];
            $cessAmount = ($taxableValue * $cessRate) / 100;
        }

        return [
            'sale_transactions_id' => $saleTransaction->id,
            'item_id' => $item['item_id'],
            'additional_description' => $item['additional_description'] ?? null,
            'ledger_id' => $item['ledger_id'],
            'unit_id' => $item['unit_id'],
            'hsn_code' => $item['hsn_code'] ?? null,
            'quantity' => $item['quantity'],
            'free_quantity' => $item['free_quantity'] ?? null,
            'mrp' => $item['mrp'] ?? null,
            'with_tax' => $item['with_tax'] ?? false,
            'rpu_with_gst' => $item['rpu_with_gst'],
            'rpu_without_gst' => $item['rpu_without_gst'],
            'discount_type' => $item['discount_type'] ?? null,
            'discount_value' => $item['discount_value'] ?? null,
            'discount_type_2' => $item['discount_type_2'] ?? null,
            'discount_value_2' => $item['discount_value_2'] ?? null,
            'total_discount_amount' => $item['total_discount_amount'] ?? 0,
            'gst_id' => $item['gst_tax'],
            'gst_tax_percentage' => $item['gst_tax_percentage'],
            'total' => $item['total'],
            'classification_nature_type' => $this->classificationNatureType,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_igst_tax' => $this->isGSTEnabled ? $item['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $item['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $item['classification_sgst_tax'] : null,
            'classification_cess_tax' => $this->isGSTEnabled && isset($item['cess']) ? $item['cess'] : null,
            'consolidating_items_to_invoice' => $item['consolidating_items_to_invoice'] ?? false,
            'cess_rate' => $cessRate,
            'cess_amount' => $cessAmount,
            'decimal_places_for_quantity' => $item['decimal_places'] ?? 2,
            'decimal_places_for_rate' => $item['decimal_places_for_rate'] ?? 2,
        ];
    }

    private function storeLedgers($ledgers, $saleTransaction)
    {
        try {
            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $saleTransactionLedgerData = $this->prepareLedgerData($ledger, $saleTransaction);

                /* Store ledger */
                $saleTransactionLedger = SaleTransactionLedger::create($saleTransactionLedgerData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateLedgers($ledgers, $saleTransaction)
    {
        try {

            $ledgerIds = SaleTransactionLedger::whereSaleTransactionsId($saleTransaction->id)->pluck('id')->toArray();
            $editSaleLedgerIds = Arr::pluck($ledgers, 'id') ?? [];
            $removeLedgerIds = array_diff($ledgerIds, $editSaleLedgerIds);

            /* Delete removed ledgers */
            SaleTransactionLedger::whereIn('id', array_values($removeLedgerIds))?->delete();

            foreach ($ledgers as $key => $ledger) {
                /* Prepare ledger data */
                $saleTransactionLedgerData = $this->prepareLedgerData($ledger, $saleTransaction);

                /* Update or create ledger */
                if (! isset($ledger['id']) || $ledger['id'] == null) {
                    $saleTransactionLedger = SaleTransactionLedger::create($saleTransactionLedgerData);
                } else {
                    $saleTransactionLedger = SaleTransactionLedger::whereId($ledger['id'])->first();
                    if (! empty($saleTransactionLedger)) {
                        $saleTransactionLedger->update($saleTransactionLedgerData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareLedgerData($ledger, $saleTransaction)
    {
        return [
            'sale_transactions_id' => $saleTransaction->id,
            'ledger_id' => $ledger['ledger_id'],
            'additional_description' => $ledger['additional_description'] ?? null,
            'with_tax' => $ledger['with_tax'] ?? false,
            'rpu_with_gst' => $ledger['rpu_with_gst'],
            'rpu_without_gst' => $ledger['rpu_without_gst'],
            'discount_type' => $ledger['discount_type'] ?? null,
            'discount_value' => $ledger['discount_value'] ?? null,
            'discount_type_2' => $ledger['discount_type_2'] ?? null, // will be added
            'discount_value_2' => $ledger['discount_value_2'] ?? null, // will be added
            'total_discount_amount' => $ledger['total_discount_amount'] ?? 0,
            'gst_id' => $ledger['gst_tax'],
            'gst_tax_percentage' => $ledger['gst_tax_percentage'],
            'total' => $ledger['total'],
            'classification_igst_tax' => $this->isGSTEnabled ? $ledger['classification_igst_tax'] : null,
            'classification_cgst_tax' => $this->isGSTEnabled ? $ledger['classification_cgst_tax'] : null,
            'classification_sgst_tax' => $this->isGSTEnabled ? $ledger['classification_sgst_tax'] : null,
            // 'classification_cess_tax' => $this->isGSTEnabled ? $ledger['cess'] : null,
            'classification_is_rcm_applicable' => $this->isRCMApplicable,
            'classification_nature_type' => $this->classificationNatureType,
        ];
    }

    private function storeAddresses($saleTransaction, $input)
    {
        try {
            $dispatchAddress = $input['dispatch_address'] ?? [];
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            $addresses = [];

            if ($dispatchAddress) {
                $addresses[] = $this->prepareAddressData($saleTransaction, $dispatchAddress, SaleTransaction::DISPATCH_ADDRESS);
            }

            if ($billingAddress) {
                $addresses[] = $this->prepareAddressData($saleTransaction, $billingAddress, SaleTransaction::BILLING_ADDRESS);
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $addresses[] = $this->prepareAddressData($saleTransaction, $shippingAddress, SaleTransaction::SHIPPING_ADDRESS);
                }
            }

            if (! empty($addresses)) {
                $saleTransaction->addresses()->createMany($addresses);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddresses($saleTransaction, $input)
    {
        try {
            $dispatchAddress = $input['dispatch_address'] ?? [];
            $billingAddress = $input['billing_address'] ?? [];
            $shippingAddress = $input['shipping_address'] ?? [];

            if ($dispatchAddress) {
                $saleTransaction->addresses()->updateOrCreate(
                    ['address_type' => SaleTransaction::DISPATCH_ADDRESS, 'model_id' => $saleTransaction->id],
                    $this->prepareAddressData($saleTransaction, $dispatchAddress, SaleTransaction::DISPATCH_ADDRESS)
                );
            }

            if ($billingAddress) {
                $saleTransaction->addresses()->updateOrCreate(
                    ['address_type' => SaleTransaction::BILLING_ADDRESS, 'model_id' => $saleTransaction->id],
                    $this->prepareAddressData($saleTransaction, $billingAddress, SaleTransaction::BILLING_ADDRESS)
                );
            }

            if (! (isset($input['shipping_address_id']) && $input['shipping_address_id'] != null)) { // This is for if shipping address is not selected or address from local storage
                if ($shippingAddress) {
                    $saleTransaction->addresses()->updateOrCreate(
                        ['address_type' => SaleTransaction::SHIPPING_ADDRESS, 'model_id' => $saleTransaction->id],
                        $this->prepareAddressData($saleTransaction, $shippingAddress, SaleTransaction::SHIPPING_ADDRESS)
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddressData($saleTransaction, $address, $addressType)
    {
        return [
            'address_1' => $address['address_1'] ?? null,
            'address_2' => $address['address_2'] ?? null,
            'country_id' => $address['country_id'] ?? null,
            'state_id' => $address['state_id'] ?? null,
            'city_id' => $address['city_id'] ?? null,
            'pin_code' => $address['pin_code'] ?? null,
            'model_id' => $saleTransaction->id,
            'model_type' => SaleTransaction::class,
            'address_type' => $addressType,
        ];
    }

    private function storeAdditionalCharges($additionChargesData, $saleTransactionId)
    {
        try {
            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $saleTransactionId);

                /* Store additional charge */
                AdditionalChargesForSalesTransaction::create($additionChargeData);
            }

            return true;
        } catch (\Exception $e) {

            throw new \Exception($e->getMessage());
        }
    }

    private function updateAdditionalCharges($additionChargesData, $saleTransactionId)
    {
        try {
            $additionalChargesIds = AdditionalChargesForSalesTransaction::where('sale_id', $saleTransactionId)->pluck('id')->toArray();
            $editedAdditionalChargesIds = Arr::pluck($additionChargesData, 'id');
            $removeAdditionalChargesIds = array_diff($additionalChargesIds, $editedAdditionalChargesIds);

            /* Delete additional charges */
            AdditionalChargesForSalesTransaction::whereIn('id', array_values($removeAdditionalChargesIds))?->delete();

            foreach ($additionChargesData as $additionCharge) {
                /* Prepare additional charge data */
                $additionChargeData = $this->prepareAdditionalChargesData($additionCharge, $saleTransactionId);

                /* Update additional charge */
                if (! isset($additionCharge['id']) || $additionCharge['id'] == null) {
                    AdditionalChargesForSalesTransaction::create($additionChargeData);
                } else {
                    $additionalChargeRecord = AdditionalChargesForSalesTransaction::where('id', $additionCharge['id'])->first();
                    if (! empty($additionalChargeRecord)) {
                        $additionalChargeRecord->update($additionChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAdditionalChargesData($additionCharge, $saleTransactionId)
    {
        if ($this->isGSTEnabled) {
            $gst = null;
            if (isset($additionCharge['ac_gst_rate_id'])) {
                $gst = GstTax::whereId($additionCharge['ac_gst_rate_id'])->first();
            }
            $gstTaxPercentage = ! empty($gst) ? $gst->tax_rate : null;
        }

        return [
            'sale_id' => $saleTransactionId,
            'ledger_id' => $additionCharge['ac_ledger_id'],
            'charge_type' => $additionCharge['ac_type'],
            'value' => $additionCharge['ac_value'],
            'gst_rate_id' => $this->isGSTEnabled && ! empty($gst) ? $gst->id : null,
            'gst_percentage' => $this->isGSTEnabled ? $gstTaxPercentage : null,
            'total_without_tax' => $additionCharge['ac_total_without_tax'],
            'total' => $additionCharge['ac_total'],
        ];
    }

    private function storeAddLess($addLessData, $saleTransactionId)
    {
        try {

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $saleTransactionId);

                /* Store add less */
                AddLessForSalesTransaction::create($addLessChargeData);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updateAddLess($addLessData, $saleTransactionId)
    {
        try {
            $addLessIds = AddLessForSalesTransaction::where('sale_id', $saleTransactionId)->pluck('id')->toArray();
            $editedAddLessIds = Arr::pluck($addLessData, 'id');
            $removeAddLessIds = array_diff($addLessIds, $editedAddLessIds);

            /* Delete add less */
            AddLessForSalesTransaction::whereIn('id', array_values($removeAddLessIds))?->delete();

            foreach ($addLessData as $addLessCharge) {
                /* Prepare add less data */
                $addLessChargeData = $this->prepareAddLessData($addLessCharge, $saleTransactionId);

                /* Update add less */
                if (! isset($addLessCharge['id']) || $addLessCharge['id'] == null) {
                    AddLessForSalesTransaction::create($addLessChargeData);
                } else {
                    $addLessRecord = AddLessForSalesTransaction::where('id', $addLessCharge['id'])->first();
                    if (! empty($addLessRecord)) {
                        $addLessRecord->update($addLessChargeData);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function prepareAddLessData($addLessCharge, $saleTransactionId)
    {
        return [
            'sale_id' => $saleTransactionId,
            'ledger_id' => $addLessCharge['al_ledger_id'],
            'is_show_in_print' => $addLessCharge['al_is_show_in_print'] ?? false,
            'type' => $addLessCharge['al_type'],
            'value' => $addLessCharge['al_value'],
            'total' => $addLessCharge['al_total'],
        ];
    }

    private function storePaymentDetails($paymentDetails, $saleTransaction)
    {
        try {
            foreach ($paymentDetails as $paymentDetail) {
                /* Prepare payment detail data */
                $paymentDetailData = $this->preparePaymentDetailData($paymentDetail, $saleTransaction);

                /* Store payment detail */
                $paymentDetailRecord = PaymentDetailsForSalesTransaction::create($paymentDetailData);

                $receiptTransaction = ReceiptTransaction::create([
                    'company_id' => $saleTransaction->company_id,
                    'date' => $paymentDetailData['date'],
                    'receipt_number' => 'sale/'.$saleTransaction->full_invoice_number,
                    'is_default_created_by_transaction' => true,
                    'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                    'ledger_id' => $saleTransaction->customer_ledger_id,
                    'total_received_amount' => $paymentDetailData['amount'],
                    'narration' => $saleTransaction->narration ?? null,
                    'payment_mode' => $paymentDetailData['mode'],
                    'reference_number' => $paymentDetailData['reference_no'],
                    'created_by' => $saleTransaction->created_by,
                ]);

                ReceiptTransactionItem::create([
                    'rc_transaction_id' => $receiptTransaction->id,
                    'sale_id' => $saleTransaction->id,
                    'invoice_number' => $saleTransaction->full_invoice_number,
                    'bill_type' => ReceiptTransaction::SALE_BILL_TYPE,
                    'received_amount' => $paymentDetailData['amount'],
                    'discount' => 0,
                    'round_off' => 0,
                ]);

                $paymentDetailRecord->update([
                    'receipt_id' => $receiptTransaction->id,
                ]);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function updatePaymentDetails($paymentDetails, $saleTransaction)
    {

        try {

            $paymentDetailsIds = PaymentDetailsForSalesTransaction::where('sale_id', $saleTransaction->id)->pluck('id')->toArray();
            $editedPaymentDetailsIds = Arr::pluck($paymentDetails, 'pd_id');
            $removePaymentDetailsIds = array_diff($paymentDetailsIds, $editedPaymentDetailsIds);

            /* Delete payment details */
            foreach ($removePaymentDetailsIds as $removePaymentDetailsId) {
                $removePaymentDetails = PaymentDetailsForSalesTransaction::where('id', $removePaymentDetailsId)->first();

                $receiptTransaction = ReceiptTransaction::where('receipt_number', 'sale/'.$this->oldSaleTransaction->full_invoice_number)
                    ->where('ledger_id', $this->oldSaleTransaction->customer_ledger_id)
                    ->where('bank_cash_ledger_id', $removePaymentDetails->ledger_id)
                    ->whereId($removePaymentDetails->receipt_id)
                    ->financialYearDate()?->forceDelete();

                $removePaymentDetails->delete();
            }

            foreach ($paymentDetails as $paymentDetail) {
                /* Prepare payment details data */
                $paymentDetailData = $this->preparePaymentDetailData($paymentDetail, $saleTransaction);

                /* Update payment details */
                if (! isset($paymentDetail['pd_id']) || $paymentDetail['pd_id'] == null) {
                    $paymentDetailRecord = PaymentDetailsForSalesTransaction::create($paymentDetailData);

                    $receiptTransaction = ReceiptTransaction::create([
                        'company_id' => $saleTransaction->company_id,
                        'date' => $paymentDetailData['date'],
                        'receipt_number' => 'sale/'.$saleTransaction->full_invoice_number,
                        'is_default_created_by_transaction' => true,
                        'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                        'ledger_id' => $saleTransaction->customer_ledger_id,
                        'total_received_amount' => $paymentDetailData['amount'],
                        'narration' => $saleTransaction->narration ?? null,
                        'payment_mode' => $paymentDetailData['mode'],
                        'reference_number' => $paymentDetailData['reference_no'],
                        'created_by' => $saleTransaction->created_by,
                    ]);

                    ReceiptTransactionItem::create([
                        'rc_transaction_id' => $receiptTransaction->id,
                        'sale_id' => $saleTransaction->id,
                        'invoice_number' => $saleTransaction->full_invoice_number,
                        'bill_type' => ReceiptTransaction::SALE_BILL_TYPE,
                        'received_amount' => $paymentDetailData['amount'],
                        'discount' => 0,
                        'round_off' => 0,
                    ]);
                } else {
                    $paymentDetailRecord = PaymentDetailsForSalesTransaction::where('id', $paymentDetail['pd_id'])->first();

                    if (! empty($paymentDetailRecord)) {
                        $paymentDetailRecord->update($paymentDetailData);
                    }

                    $receiptTransaction = ReceiptTransaction::where('receipt_number', 'sale/'.$this->oldSaleTransaction->full_invoice_number)
                        ->where('ledger_id', $this->oldSaleTransaction->customer_ledger_id)
                        ->whereId($paymentDetailRecord->receipt_id)
                        ->first();

                    if (! empty($receiptTransaction)) {
                        $receiptTransaction->update([
                            'receipt_number' => 'sale/'.$saleTransaction->full_invoice_number,
                            'date' => $paymentDetailData['date'],
                            'bank_cash_ledger_id' => $paymentDetailData['ledger_id'],
                            'ledger_id' => $saleTransaction->customer_ledger_id,
                            'total_received_amount' => $paymentDetailData['amount'],
                            'narration' => $saleTransaction->narration ?? null,
                            'payment_mode' => $paymentDetailData['mode'],
                            'reference_number' => $paymentDetailData['reference_no'],
                        ]);

                        $receiptTransactionItem = ReceiptTransactionItem::where('rc_transaction_id', $receiptTransaction->id)->first();
                        if (! empty($receiptTransactionItem)) {
                            $receiptTransactionItem->update([
                                'invoice_number' => $saleTransaction->full_invoice_number,
                                'received_amount' => $paymentDetailData['amount'],
                            ]);
                        }
                    }
                }

                $paymentDetailRecord->update([
                    'receipt_id' => $receiptTransaction->id,
                ]);
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function preparePaymentDetailData($paymentDetail, $saleTransaction)
    {

        return [
            'sale_id' => $saleTransaction->id,
            'ledger_id' => $paymentDetail['pd_ledger_id'],
            'date' => Carbon::parse($paymentDetail['pd_date']),
            'amount' => $paymentDetail['pd_amount'],
            'mode' => $paymentDetail['pd_mode'] ?? null,
            'reference_no' => $paymentDetail['pd_reference_number'] ?? null,
        ];
    }
}
