import React, { useContext, useEffect, useState } from 'react';
import { Button, Col, Form, Modal, Row } from 'react-bootstrap';
import Close from '../../../assets/images/svg/close';
import { useSelector } from 'react-redux';
import { FormInput } from '../../../components/ui/Input';
import { StateContext } from '../../../context/StateContext';
import ReactSelect from '../../../components/ui/ReactSelect';

const CustomFieldBatchModal = (props) => {
    const { show, handleClose, openClassificationModel } = props;
    const { items, setItems } = useContext(StateContext);
    const { configuration, company, item } = useSelector(state => state);
    const [customFields, setcustomFields] = useState(item?.getSingleItemById?.[0]?.custom_fields?.filter(field => field?.status) || [])
    const saleConfiguration = configuration?.configuration;
    const [totalRows, setTotalRows] = useState(10)

    useEffect(() => {
        const customFields = item?.getSingleItemById?.[0]?.custom_fields?.filter(field => field?.status);
        setcustomFields(customFields || [])
    }, [item?.getSingleItemById?.[0]?.custom_fields])

    const handleCloseModel = () => {
        handleClose();
        if (
            saleConfiguration?.header?.is_change_gst_details &&
            company?.company?.is_gst_applicable
        ) {
            openClassificationModel();
        }
        setcustomFields([]);
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        handleCloseModel();
    };

    const handleChange = (e) => {
    const { name, value } = e.target;

    const [_, rowIndexStr, colIndexStr] = name.split('_');
    const rowIndex = parseInt(rowIndexStr, 10);
    const colIndex = parseInt(colIndexStr, 10);

    const itemList = [...items];
    const findIndex = itemList.findIndex(item_detail =>
        item?.getSingleItemById?.some(
            price_list => price_list?.item_master?.id === item_detail?.selectedItem
        )
    );

    if (findIndex === -1) return;

    const existingFields = itemList[findIndex]?.custom_fields_item_master || [];

    while (existingFields.length <= rowIndex) {
        existingFields.push([]);
    }

    const fieldId = customFields[colIndex]?.id;
    const fieldObj = { custom_field_item_master_id: fieldId, value };

    const existingIndex = existingFields[rowIndex].findIndex(
        f => f.custom_field_item_master_id === fieldId
    );

    if (value?.toString().trim() === '') {
        // If empty value, remove the field if it exists
        if (existingIndex > -1) {
            existingFields[rowIndex].splice(existingIndex, 1);
        }
    } else {
        if (existingIndex > -1) {
            existingFields[rowIndex][existingIndex] = fieldObj;
        } else {
            existingFields[rowIndex].push(fieldObj);
        }
    }

    // Clean up empty rows
    const cleanedFields = existingFields.filter(row => row.length > 0);

    itemList[findIndex].custom_fields_item_master = cleanedFields;
    setItems(itemList);
};


    const handleCheckRequired = (rowIndex) => {
    const itemList = [...items];
    const findIndex = itemList.findIndex(item_detail =>
        item?.getSingleItemById?.some(
            price_list => price_list?.item_master?.id === item_detail?.selectedItem
        )
    );

    if (findIndex === -1) return false;

    const row = itemList[findIndex]?.custom_fields_item_master?.[rowIndex] || [];
    return row.length > 0;
};

const handleDeleteRow = (field) => {
    setTotalRows(totalRows - 1);
}

    return (
        // className='custome-field-modal'
        <Modal show={show} onHide={handleCloseModel} size="md" centered backdrop={true}>
            <div className="modal-header py-3">
                <h5 className="modal-title">Add Custom field</h5>
                <div className="d-flex justify-content-end align-items-center">
                    <button
                        className="btn btn-primary btn-sm"
                        type="button"
                        onClick={() => setTotalRows(totalRows + 1)}
                        style={{ padding: "6px 14px 6px 10px" }}
                    >
                        ADD <span className="font-semibold fs-4">+</span>
                    </button>
                    <button
                        type="button"
                        className="btn btn-icon btn-sm btn-active-light-primary ms-2"
                        onClick={handleCloseModel}
                    >
                        <Close />
                    </button>
                </div>
            </div>
            <Modal.Body className="m-2 mt-1">
                <div className="w-100 overflow-auto pt-1">
                    {/* <form onSubmit={handleSubmit}>
                    {Array.from({ length: totalRows }).map((_, rowIndex) => (
                        <div key={rowIndex} className="mb-4 d-flex gap-3 align-items-center position-relative">
                            {customFields.map((field, colIndex) => (
                                // <div className='d-flex justify-content-center' key={colIndex}>
                                <>
                                    <div key={colIndex}>
                                        <Form.Group className="position-relative form-floating-group h-40px">
                                            <FormInput
                                                type={field.input_type}
                                                name={`customField_${rowIndex}_${colIndex}`}
                                                className="floating-label-input required capitalize h-40px pe-6 w-input-300px"
                                                value={field.value}
                                                onChange={e =>
                                                    handleChange(e, field, colIndex, rowIndex)
                                                }
                                                placeholder=""
                                                required={handleCheckRequired(rowIndex)}
                                            />
                                            <Form.Label
                                                className={
                                                    handleCheckRequired(rowIndex) ? "required" : ""
                                                }
                                            >
                                                {field.label_name}
                                            </Form.Label>
                                        </Form.Group>
                                    </div>

                                </>
                            ))}
                            <button
                                        type="button"
                                        className="bg-white delete-icon-modal border-0 p-0 ps-2 pe-2 focus-shadow"
                                        onClick={() => handleDeleteRow(field)}
                                    >
                                        <i className="fas fs-4 fa-trash-alt text-danger mx-auto my-3"></i>
                                    </button>
                        </div>
                    ))}
                </form> */}
                    {/* <div className="custome-field-table-modal mb-5 overflow-auto">
                    <table className='w-100'>
                        <thead>
                            <tr>
                                <th></th>
                                <th>Sr no.</th>
                                <th>Label Name</th>
                                <th>Manufacturing Date</th>
                                <th>Expiry Date</th>
                                <th>Warranty</th>
                                <th>Qty </th>
                                <th>Rate</th>
                                <th>Sale Qty </th>
                            </tr>
                            <tr>
                                <th></th>
                                <th>
                                <input type="text" placeholder="Search" className="form-control search-box" />
                                </th>
                                <th>
                                <input type="text" placeholder="Search" className="form-control search-box" />
                                </th>
                                <th>
                                <input type="text" placeholder="Search" className="form-control search-box" />
                                </th>
                                <th>
                                <input type="text" placeholder="Search" className="form-control search-box" />
                                </th>
                                <th>
                                <input type="text" placeholder="Search" className="form-control search-box" />
                                </th>
                                <th>
                                <input type="text" placeholder="Search" className="form-control search-box" />
                                </th>
                                <th>
                                <input type="text" placeholder="Search" className="form-control search-box" />
                                </th>
                                <th>
                                <input type="text" placeholder="Search" className="form-control search-box" />
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div className="text-center">
                                        <input type="checkbox" className="form-check-input check-box" id="selectAllCustomFieldBatch1" />
                                    </div>
                                </td>
                                <td>1</td>
                                <td>1234567890123456</td>
                                <td>03-10-2025</td>
                                <td>03-10-2025</td>
                                <td>6 months</td>
                                <td>10</td>
                                <td>xxxxxx</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>
                                    <div className="text-center">
                                        <input type="checkbox" className="form-check-input check-box" id="selectAllCustomFieldBatch1" />
                                    </div>
                                </td>
                                <td>2</td>
                                <td>1234567890123456</td>
                                <td>03-10-2025</td>
                                <td>03-10-2025</td>
                                <td>6 months</td>
                                <td>10</td>
                                <td>xxxxxx</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>
                                    <div className="text-center">
                                        <input type="checkbox" className="form-check-input check-box" id="selectAllCustomFieldBatch1" />
                                    </div>
                                </td>
                                <td>3</td>
                                <td>1234567890123456</td>
                                <td>03-10-2025</td>
                                <td>03-10-2025</td>
                                <td>6 months</td>
                                <td>10</td>
                                <td>xxxxxx</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>
                                    <div className="text-center">
                                        <input type="checkbox" className="form-check-input check-box" id="selectAllCustomFieldBatch1" />
                                    </div>
                                </td>
                                <td>4</td>
                                <td>1234567890123456</td>
                                <td>03-10-2025</td>
                                <td>03-10-2025</td>
                                <td>6 months</td>
                                <td>10</td>
                                <td>xxxxxx</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>
                                    <div className="text-center">
                                        <input type="checkbox" className="form-check-input check-box" id="selectAllCustomFieldBatch1" />
                                    </div>
                                </td>
                                <td>5</td>
                                <td>1234567890123456</td>
                                <td>03-10-2025</td>
                                <td>03-10-2025</td>
                                <td>6 months</td>
                                <td>10</td>
                                <td>xxxxxx</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>
                                    <div className="text-center">
                                        <input type="checkbox" className="form-check-input check-box" id="selectAllCustomFieldBatch1" />
                                    </div>
                                </td>
                                <td>6</td>
                                <td>1234567890123456</td>
                                <td>03-10-2025</td>
                                <td>03-10-2025</td>
                                <td>6 months</td>
                                <td>10</td>
                                <td>xxxxxx</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>
                                    <div className="text-center">
                                        <input type="checkbox" className="form-check-input check-box" id="selectAllCustomFieldBatch1" />
                                    </div>
                                </td>
                                <td>7</td>
                                <td>1234567890123456</td>
                                <td>03-10-2025</td>
                                <td>03-10-2025</td>
                                <td>6 months</td>
                                <td>10</td>
                                <td>xxxxxx</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>
                                    <div className="text-center">
                                        <input type="checkbox" className="form-check-input check-box" id="selectAllCustomFieldBatch1" />
                                    </div>
                                </td>
                                <td>8</td>
                                <td>1234567890123456</td>
                                <td>03-10-2025</td>
                                <td>03-10-2025</td>
                                <td>6 months</td>
                                <td>10</td>
                                <td>xxxxxx</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>
                                    <div className="text-center">
                                        <input type="checkbox" className="form-check-input check-box" id="selectAllCustomFieldBatch1" />
                                    </div>
                                </td>
                                <td>9</td>
                                <td>1234567890123456</td>
                                <td>03-10-2025</td>
                                <td>03-10-2025</td>
                                <td>6 months</td>
                                <td>10</td>
                                <td>xxxxxx</td>
                                <td>10</td>
                            </tr>
                            <tr>
                                <td>
                                    <div className="text-center">
                                        <input type="checkbox" className="form-check-input check-box" id="selectAllCustomFieldBatch1" />
                                    </div>
                                </td>
                                <td>9</td>
                                <td>1234567890123456</td>
                                <td>03-10-2025</td>
                                <td>03-10-2025</td>
                                <td>6 months</td>
                                <td>10</td>
                                <td>xxxxxx</td>
                                <td>10</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="overflow-auto justify-content-center d-flex">
            <nav>
                <ul class="pagination cursor-pointer">
                    <li class="page-item"
                        aria-disabled="true" aria-label="Previous">
                        <span class="page-link custom-fs-20" aria-hidden="true">&lsaquo;</span>
                    </li>
                            <li class="page-item active" aria-current="page">
                                <span class="page-link">1</span>
                            </li>

                    <li class="page-item"
                        aria-disabled="true" aria-label="Next">
                        <span class="page-link custom-fs-20" aria-hidden="true">&rsaquo;</span>
                    </li>
                </ul>
            </nav>
        </div> */}
                    <div className="mb-4">
                        <Form.Group className="position-relative form-floating-group">
                            <FormInput
                                // ref={labelRef}
                                className="floating-label-input capitalize"
                                type="text"
                                placeholder=""
                                required
                                // value={fieldName}
                                // onChange={e => setFieldName(e.target.value)}
                            />
                            <Form.Label className="required">Label Name</Form.Label>
                        </Form.Group>
                    </div>
                    <div className="mb-4">
                        <Form.Group>
                            <div className="input-group flex-nowrap">
                                <div className="position-relative h-40px w-100 focus-shadow">
                                    <ReactSelect
                                        placeholder="Field Type"
                                        // options={customFieldList}
                                        // value={fieldType}
                                        // onChange={handleChangeCustomFieldType}
                                        // required
                                        // isDisabled={getItemCustomFieldId?.is_field_used}
                                    />
                                </div>
                            </div>
                        </Form.Group>
                    </div>
                    <div className="custome-field-table-modal mb-5 overflow-auto">
                    <table className='w-100'>
                        <thead>
                            <tr>
                                <th>Transaction Type</th>
                                <th>Print</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Estimate Quote</td>
                                <td>checkbox</td>
                            </tr>
                            <tr>
                                <td>Delivery Challan</td>
                                <td>checkbox</td>
                            </tr>
                            <tr>
                                <td>Sale</td>
                                <td>checkbox</td>
                            </tr>
                            <tr>
                                <td>Sale Return</td>
                                <td>checkbox</td>
                            </tr>
                            <tr>
                                <td>Income Debit Note</td>
                                <td>checkbox</td>
                            </tr>
                            <tr>
                                <td>Income Credit Note</td>
                                <td>checkbox</td>
                            </tr>
                            <tr>
                                <td>Purchase Order</td>
                                <td>checkbox</td>
                            </tr>
                            <tr>
                                <td>Purchase</td>
                                <td>checkbox</td>
                            </tr>
                            <tr>
                                <td>Purchase Return</td>
                                <td>checkbox</td>
                            </tr>
                            <tr>
                                <td>Expense Debit Note</td>
                                <td>checkbox</td>
                            </tr>
                            <tr>
                                <td>Expense Credit Note</td>
                                <td>checkbox</td>
                            </tr>
                            <tr>
                                <td>Recurring Invoice</td>
                                <td>checkbox</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                </div>
                <div className="d-flex gap-3">
                    <Button variant="primary" type="submit">
                        Save
                    </Button>
                    <Button variant="secondary" onClick={handleCloseModel} type="button">
                        Close
                    </Button>
                </div>
            </Modal.Body>
        </Modal>
    );
};

export default CustomFieldBatchModal;
