import { useContext, useEffect, useState } from "react";
import { Col, Form, Modal, OverlayTrigger, Row, Tooltip } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import Setting from "../../../assets/images/svg/setting";
import { StateContext } from "../../../context/StateContext";
import { replaceIdsWithLabels } from "../../../shared/calculation";
import {
    deleteItemMasterCustomField,
    fetchItemMasterCustomFieldList,
    getItemMasterCustomField,
    updateItemCustomFieldConfiguration,
    updateItemCustomFieldStatus
} from "../../../store/configuration/configurationSlice";
import WarningModal from "../../common/WarningModal";
import AddItemMasterCustomFieldModal from "./AddItemMasterCustomField";
import CustomFieldCalculation from "./CustomFieldCalculation";
import ReactSelect from "../../../components/ui/ReactSelect";
import useDropdownOption from "../../../shared/dropdownList";
import { updateItemConfiguration } from "../../../store/item/itemSlice";
import { errorToast } from "../../../store/actions/toastAction";
import { toastType } from "../../../constants";
import { closestCenter, DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

const ItemMasterConfigurationModal = (props) => {
    const { formik, id } = props;
    const dispatch = useDispatch();
    const {item} = useSelector(state => state);
    const tableHeader = useSelector(state => state.table?.tableHeader);
    const configuration = useSelector(state => state.configuration);
    const [isDeleteModal, setIsDeleteModal] = useState(false);
    const [isCustomFieldCalculationQty, setIsCustomFieldCalculationQty] = useState(false)
    const [selectedCustomFieldFormula, setSelectedCustomFieldFormula] = useState("")
    const [itemUnitData, setItemUnitData] = useState({
        primary_uom_id: "",
        primary_unit_of_measurement_name: "",
        secondary_uom_id: "",
        secondary_unit_of_measurement_name: "",
        conversion_rate: 1
    });

    const {
        isSaleModel,
        openSaleModel,
        closeSaleModel,
        openCustomFieldModel,
        closeCustomFieldModel,
        isCustomFieldModel,
        customFieldItemMaster,
        setCustomFieldItemMaster,
        setIsEditModel,
        setItemTableCustomFieldList,
        customItemConfigurationList,
        setCustomItemConfigurationList,
        setBackendFormula,
        setDisplayFormula,
        isEditModel
    } = useContext(StateContext);

    const { unitOfMeasurement } = useDropdownOption();

    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 5,
            },
        }),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleClose = () => {
        closeCustomFieldModel();
    }

    const handleDeleteCustomHeaderField = () => {
        dispatch(
            deleteItemMasterCustomField(
                (customFieldItemMaster?.id),
                setIsDeleteModal,
                setCustomItemConfigurationList
            )
        );
        setIsCustomFieldCalculationQty(false);
        setSelectedCustomFieldFormula(null);
        setCustomFieldItemMaster(null);
        setBackendFormula("");
        setDisplayFormula("");
    };

    const handleSubmit = e => {
        // submit
        e.preventDefault();
        handleClose();
    };

    const onUpdateCustomField = (type, data) => {
        const response = {
            custom_field_id: data.id,
            status: type
        }
        dispatch(updateItemCustomFieldStatus(response));
        const customField = customItemConfigurationList?.map(item => {
            if (item.id === data.id) {
                return {
                    ...item,
                    status: type,
                    local_status: item?.enable_for_all ? type : false,
                };
            }
            return item;
        });
        setCustomItemConfigurationList(customField);
        formik.setFieldValue("custom_fields", customField?.filter(item => item?.status)?.map(item => item?.id))
    };

    const handleOpenConfigurationModel = () => {
        openSaleModel();
    }

    const handleOpenDeleteModal = item => {
        setIsDeleteModal(true);
        setCustomFieldItemMaster(item);
    };

    const handleOpenEditModel = async (item) => {
        openCustomFieldModel();
        setIsEditModel(true);
        const customField = await dispatch(getItemMasterCustomField(item?.id));
        setCustomFieldItemMaster(customField);
        dispatch(fetchItemMasterCustomFieldList());
    };

    const openCustomField = type => {
        setIsEditModel(false);
        openCustomFieldModel();
        dispatch(fetchItemMasterCustomFieldList());
    };

    useEffect(() => {
        setItemUnitData({
            primary_uom_id: item?.itemConfigurationData?.primary_uom_id,
            primary_unit_of_measurement_name: item?.itemConfigurationData?.primary_uom?.full_name,
            secondary_uom_id: item?.itemConfigurationData?.secondary_uom_id,
            secondary_unit_of_measurement_name:
                item?.itemConfigurationData?.secondary_uom?.full_name,
            conversion_rate: item?.itemConfigurationData?.conversion_rate,
        });
    }, [item?.itemConfigurationData]);

    useEffect(() => {
        const filteredTableList = tableHeader?.length > 0 ? tableHeader?.filter(
            table =>
                table?.header !== "Item" && table?.header !== "Ledger" && table?.header !== "Total" && (table?.transaction_type || table?.name === "Quantity")
        ) : [];
        setItemTableCustomFieldList(filteredTableList);
    }, [tableHeader]);

    const handleOpenCustomFieldQtyCalculation = (item) => {
            setIsCustomFieldCalculationQty(true);
            setSelectedCustomFieldFormula(item);
            setCustomFieldItemMaster(item);
            setBackendFormula(item?.default_formula?.formula);
            const DisplayFormula = replaceIdsWithLabels(item?.default_formula?.formula, customItemConfigurationList);
            setDisplayFormula(DisplayFormula ?? "");
    }

    const handleCloseCalculationModel = () => {
        setIsCustomFieldCalculationQty(false);
        setSelectedCustomFieldFormula(null);
        // setCustomFieldItemMaster(null);
        // setBackendFormula("");
        // setDisplayFormula("");
    }

    const changePrimarydetail = e => {
        if (itemUnitData?.secondary_uom_id !== null && itemUnitData.secondary_uom_id == e.value) {
            return dispatch(
                errorToast({
                    text: "Secondary and Primary Unit can't be same",
                    type: toastType.ERROR,
                })
            );
        } else {
            setItemUnitData({
                ...itemUnitData,
                primary_uom_id: e?.value,
                primary_unit_of_measurement_name: e.label,
            });
        }
    };
    const changeSecondarydetail = e => {
        if (itemUnitData?.primary_uom_id !== null && itemUnitData?.primary_uom_id == e?.value) {
            return dispatch(
                errorToast({
                    text: "Secondary and Primary Unit can't be same",
                    type: toastType.ERROR,
                })
            );
        } else {
            setItemUnitData({
                ...itemUnitData,
                secondary_uom_id: e?.value,
                secondary_unit_of_measurement_name: e?.label,
            });
        }
    };

    const handleConversionRateChange = (e) => {
        setItemUnitData({
            ...itemUnitData,
            conversion_rate: e.target.value,
        });
    };

    const handleItemUnitSave = async () => {
        const params = {
            primary_uom_id: itemUnitData?.primary_uom_id,
            secondary_uom_id: itemUnitData?.secondary_uom_id,
            conversion_rate: itemUnitData?.conversion_rate
        }
        if(itemUnitData?.primary_uom_id && itemUnitData?.secondary_uom_id){
            if (itemUnitData?.primary_uom_id == itemUnitData?.secondary_uom_id) {
                return dispatch(
                    errorToast({
                        text: "Secondary and Primary Unit can't be same",
                        type: toastType.ERROR,
                    })
                );
            }
        }
        await dispatch(updateItemConfiguration(params));
        closeSaleModel();
        if(!formik.values.id){
            if(itemUnitData?.primary_uom_id){
                formik.setFieldValue("primary_unit_of_measurement", itemUnitData?.primary_uom_id),
                formik.setFieldValue("primary_unit_of_measurement_name", itemUnitData?.primary_unit_of_measurement_name);
                formik.setFieldValue("re_order_uom", itemUnitData?.primary_uom_id);
            }
            if(itemUnitData?.secondary_uom_id){
                formik.setFieldValue("secondary_unit_of_measurement", itemUnitData?.secondary_uom_id);
                formik.setFieldValue("secondary_unit_of_measurement_name", itemUnitData?.secondary_unit_of_measurement_name);
                formik.setFieldValue("re_order_uom", itemUnitData?.secondary_uom_id);
            }
            if(itemUnitData?.conversion_rate){
                formik.setFieldValue("conversion_rate", itemUnitData?.conversion_rate);
            }
        }
    };

    const SortableItem = ({ key, id, custom }) => {
        const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
            id: id,
            disabled: custom?.label_name === "Quantity" // Disable drag for Quantity items
        });

        const style = {
            transform: CSS.Transform.toString(transform),
            transition,
            background: "#fff",
            borderBottom: custom?.label_name !== "Quantity" ? "1px solid #eee" : 'none',
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            opacity: isDragging ? 0.5 : 1,
            zIndex: isDragging ? 1000 : 1,
        };

        return (
            <div ref={setNodeRef} key={key} style={style} {...attributes} {...listeners} onClick={(e) => e.stopPropagation()}>
                <div className="d-flex align-items-center justify-content-between my-1 gap-3 w-100 ml-auto">
                    {/* Left side: Drag icon + Label */}
                    <div className="d-flex align-items-center gap-3 my-1">
                        {custom?.label_name !== "Quantity" ? (
                            <>
                                <div
                                    className="btn p-0"
                                    style={{ cursor: "grab", touchAction: "none" }}
                                >
                                    <svg
                                        width="18"
                                        height="18"
                                        fill="#73757D"
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 448 512"
                                    >
                                        <path d="M0 96C0 78.3 14.3 64 32 64l384 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 128C14.3 128 0 113.7 0 96zM0 256c0-17.7 14.3-32 32-32l384 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 288c-17.7 0-32-14.3-32-32zM448 416c0 17.7-14.3 32-32 32L32 448c-17.7 0-32-14.3-32-32s14.3-32 32-32l384 0c17.7 0 32 14.3 32 32z" />
                                    </svg>
                                </div>
                                <p className="mb-0 h5 fw-medium">{custom?.label_name}</p>
                            </>
                        ) : ""}
                    </div>

                    {/* Right side: Action buttons */}
                    <div className="d-flex align-items-center gap-2 ms-auto">
                        {custom?.label_name !== "Quantity" && (
                            <>
                                <button
                                    type="button"
                                    className="bg-transparent border-0 p-0 ps-2 pe-2 focus-shadow"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        handleOpenEditModel(custom);
                                    }}
                                    style={{ pointerEvents: 'auto', touchAction: 'manipulation' }}
                                >
                                    <i className="fas fs-4 fa-edit text-primary"></i>
                                </button>
                                <button
                                    type="button"
                                    className="bg-transparent border-0 p-0 ps-2 pe-2 focus-shadow"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        handleOpenDeleteModal(custom);
                                    }}
                                    style={{ pointerEvents: 'auto', touchAction: 'manipulation' }}
                                >
                                    <i className="fas fs-4 fa-trash-alt text-danger"></i>
                                </button>
                                <div className="form-check form-switch m-0">
                                    <input
                                        className="form-check-input"
                                        type="checkbox"
                                        checked={custom?.status || false}
                                        onChange={(e) => {
                                            e.stopPropagation();
                                            onUpdateCustomField(e.target.checked, custom);
                                        }}
                                        style={{ pointerEvents: 'auto', touchAction: 'manipulation' }}
                                    />
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    const handleDragEnd = (event) => {
        const { active, over } = event;
        if (!over || active.id === over.id) return;

        const draggableItems = customItemConfigurationList.filter(item => item?.label_name !== "Quantity");
        const oldIndex = draggableItems.findIndex(item => item.id === active.id);
        const newIndex = draggableItems.findIndex(item => item.id === over.id);

        if (oldIndex !== -1 && newIndex !== -1) {
            const reorderedDraggableItems = arrayMove(draggableItems, oldIndex, newIndex);
            const quantityItems = customItemConfigurationList.filter(item => item?.label_name === "Quantity");

            // Combine reordered draggable items with quantity items
            const newList = [...reorderedDraggableItems, ...quantityItems];

            setCustomItemConfigurationList(newList);
            formik.setFieldValue("custom_fields", newList?.filter(item => item?.status)?.map(item => item?.id));
            dispatch(updateItemCustomFieldConfiguration(newList?.filter(item => item?.id)?.map(item => item?.id)));
        }
    };

    return (
        <>
            <div className="nav-item">
                <button className="nav-link" type="button" onClick={handleOpenConfigurationModel}>
                    <Setting />
                </button>
            </div>
            <Modal
                show={isSaleModel}
                onHide={closeSaleModel}
                className={`settings-modal custom-field-modal custom-offcanvas-modal fade p-0 `}
            >
                <form>
                    <div className="offcanvas-header bg-white" style={{ height: "68px" }}>
                        <h3 className="mb-0">Item Configuration</h3>
                        <button
                            className="btn close-button p-0"
                            onClick={closeSaleModel}
                            type="button"
                        >
                            &times;
                        </button>
                    </div>
                    <div className="offcanvas-body">
                        <div className="desc-box mb-5">
                            <div className="d-flex align-items-center justify-content-between">
                                <h3 className="mb-0">Custom Field</h3>
                                <button
                                    className="btn btn-primary btn-sm"
                                    type="button"
                                    onClick={openCustomField}
                                    style={{ padding: "6px 14px 6px 10px" }}
                                >
                                    ADD <span className="font-semibold fs-4">+</span>
                                </button>
                                <AddItemMasterCustomFieldModal
                                    show={isCustomFieldModel}
                                    handleClose={handleClose}
                                    id={id}
                                />
                            </div>
                            <div className="mt-2">
                                {customItemConfigurationList?.length == 0 ? (
                                    <div className="text-center">No custom field found</div>
                                ) : (
                                    ""
                                )}
                            </div>
                            <DndContext
                                sensors={sensors}
                                collisionDetection={closestCenter}
                                onDragEnd={handleDragEnd}
                            >
                                <SortableContext
                                    items={customItemConfigurationList?.filter(item => item?.label_name !== "Quantity")?.map(item => item?.id)}
                                    strategy={verticalListSortingStrategy}
                                >
                                    {customItemConfigurationList?.length > 0 &&
                                        customItemConfigurationList
                                            .filter(item => item?.label_name !== "Quantity")
                                            .map((item, index) => (
                                                <SortableItem key={item?.id || index} id={item?.id} custom={item} />
                                            ))}
                                </SortableContext>
                            </DndContext>
                            {customItemConfigurationList?.filter(item => item?.label_name === "Quantity")?.map((custom, index) => (
                                <div
                                    key={index}
                                    className="d-flex align-items-center justify-content-between"
                                >
                                    <p className="mb-0 h5 fw-medium">{custom?.label_name}</p>
                                    <div className="d-flex justify-content-end align-items-center">
                                        {custom?.label_name === "Quantity" ? (
                                            <button
                                                type="button"
                                                className="bg-transparent border-0 p-0 ps-2 pe-2 focus-shadow"
                                                onClick={() =>
                                                    handleOpenCustomFieldQtyCalculation(custom)
                                                }
                                            >
                                                <OverlayTrigger
                                                    placement="top"
                                                    overlay={<Tooltip id={`tooltip`}>{replaceIdsWithLabels(custom?.default_formula?.formula, customItemConfigurationList)}</Tooltip>}
                                                >
                                                    <i className="fa-solid fs-4 fa-calculator mx-auto my-3 text-black cursor-pointer ps-1"></i>
                                                </OverlayTrigger>
                                            </button>
                                        ) : (
                                            ""
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                        <div className="desc-box" style={customItemConfigurationList?.length < 6 ? {} : { marginBottom: "140px" }}>
                            <Form.Group>
                                <div className="input-group flex-nowrap form-group-select mb-4">
                                    <div className="position-relative h-40 w-100  focus-shadow">
                                        <ReactSelect
                                            name="primary_unit_of_measurement"
                                            // required={true}
                                            value={itemUnitData?.primary_uom_id}
                                            options={
                                                unitOfMeasurement
                                            }
                                            onChange={
                                                changePrimarydetail
                                            }
                                            radius={true}
                                            islabel={true}
                                            placeholder="Default Primary Unit of Measurement"
                                            defaultLabel="Select Primary Unit"
                                            position={customItemConfigurationList?.length < 8 ? "bottom" : "top"}
                                        />
                                    </div>
                                </div>
                            </Form.Group>

                            <Form.Group>
                                <div className="input-group flex-nowrap form-group-select mb-4">
                                    <div className="position-relative h-40 w-100 focus-shadow">
                                        <ReactSelect
                                            name="secondaryUOM"
                                            value={itemUnitData?.secondary_uom_id}
                                            onChange={changeSecondarydetail}
                                            options={unitOfMeasurement}
                                            radius={true}
                                            islabel={true}
                                            placeholder="Default Secondary Unit of Measurement"
                                            defaultLabel="Select Secondary Unit"
                                            position={customItemConfigurationList?.length < 7 ? "bottom" : "top"}
                                        />
                                    </div>
                                </div>
                            </Form.Group>

                            <div className=" w-100 flex flex-col gap-2 ">
                                <label className="form-label fs-6 fw-medium text-gray-900 mb-0">
                                    Conversion Rate
                                </label>
                                <div className="d-flex gap-2 align-items-center w-100">
                                    {itemUnitData?.primary_uom_id && <p className="mb-0">
                                        1 ({itemUnitData?.primary_unit_of_measurement_name}) =</p>}
                                    <input
                                        type="text"
                                        name="conversion_rate"
                                        placeholder="Conversion Rate"
                                        value={itemUnitData?.conversion_rate}
                                        onChange={handleConversionRateChange}
                                        className="form-control w-25 h-40px"
                                    />
                                    {itemUnitData?.secondary_uom_id && <p className="mb-0">
                                        ({itemUnitData?.secondary_unit_of_measurement_name})
                                    </p>}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="offcanvas-footer">
                        <div className="d-flex gap-4 fixed-buttons rounded-0">
                            <button
                                onClick={handleItemUnitSave}
                                type="button"
                                className="btn btn-primary"
                            >
                                Save
                            </button>
                        </div>
                    </div>
                </form>
            </Modal>
            {isDeleteModal && (
                <WarningModal
                    show={isDeleteModal}
                    title="Delete!"
                    message="Are you sure want to delete this custom field?"
                    showCancelButton
                    showConfirmButton
                    confirmText="Yes, Delete"
                    cancelText="No, Cancel"
                    handleClose={() => setIsDeleteModal(false)}
                    handleSubmit={handleDeleteCustomHeaderField}
                />
            )}
            {isCustomFieldCalculationQty && (
                <CustomFieldCalculation
                    show={isCustomFieldCalculationQty}
                    handleCloseModel={handleCloseCalculationModel}
                    isEditModel={isEditModel}
                    selectedCustomFieldFormula={selectedCustomFieldFormula}
                    type='qty'
                    is_default_configuration={true}
                />
            )}
        </>
    );
};

export default ItemMasterConfigurationModal;
