import React, { useContext, useEffect, useMemo, useRef } from "react";
import { Modal } from "react-bootstrap";
import Close from "../../../assets/images/svg/close";
import { Form , Row , Col } from "react-bootstrap";
import { StateContext } from "../../../context/StateContext";
import { IsFormulaValid } from "../../../shared/calculation";
import { errorToast } from "../../../store/actions/toastAction";
import { toastType } from "../../../constants";
import { useDispatch } from "react-redux";
import { addItemMasterQtyCustomField, deleteCustomFieldItemFormula } from "../../../store/configuration/configurationSlice";

const CustomFieldCalculation = ({ show, handleCloseModel, selectedCustomFieldFormula, type, is_default_configuration }) => {
    const { customItemConfigurationList, setCustomItemConfigurationList, displayFormula, setDisplayFormula, backendFormula, setBackendFormula, storeAddedVariableId, setStoreAddedVariableId, customFieldItemMaster } = useContext(StateContext);
    const dispatch = useDispatch();
    const textareaRef = useRef(null);
    const filteredCustomItemConfigurationList = customItemConfigurationList?.filter(item => item?.input_type === "number")

    const labelToIdMap = useMemo(() => {
        const map = {};
        filteredCustomItemConfigurationList?.forEach(item => {
            map[item.label_name] = item.id;
        });
        return map;
    }, [filteredCustomItemConfigurationList]);

    const handleChange = e => {
        setDisplayFormula(e.target.value);
        const updatedBackendFormula = e.target.value.replace(/\{ *([^}]+) *\}/g, (_, label) => {
            const id = labelToIdMap[label.trim()];
            return id ? `{ ${id} }` : `{ ${label} }`; // fallback to label if not found
        });
        setBackendFormula(updatedBackendFormula);
    };

   const insertAtCursor = (valueToInsert, backendValueToInsert = null) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    const safeDisplay = displayFormula || ""; // Fallback to empty string
    const newDisplay = safeDisplay.slice(0, start) + valueToInsert + safeDisplay.slice(end);
    const newBackend = newDisplay.replace(/\{ *([^}]+) *\}/g, (_, label) => {
        const id = labelToIdMap[label.trim()];
        return id ? `{ ${id} }` : `{ ${label} }`;
    });
    setDisplayFormula(newDisplay);
    setBackendFormula(newBackend);

    // Set cursor after inserted value
    setTimeout(() => {
        textarea.focus();
        const newPos = valueToInsert === "Multiple()" ? start + valueToInsert.length - 1 : start + valueToInsert.length;
        textarea.setSelectionRange(newPos, newPos);
    }, 0);
};

    useEffect(() => {
        if (textareaRef.current) {
            const textarea = textareaRef.current;
            // Wait until modal is fully rendered
            setTimeout(() => {
                textarea.focus();
                const length = textarea.value.length;
                textarea.setSelectionRange(length, length);
            }, 100);
        }
    }, []);


const insertToFormula = (label, id) => {
    const display = `{ ${label} }`;
    const backend = `{ ${id} }`;

    insertAtCursor(display, backend);

    setStoreAddedVariableId(
        prev => ((prev ? prev + "," : "") || "") + `${id}`
    );

    setCustomItemConfigurationList(prev =>
        prev.map(item => {
            if (item?.id === selectedCustomFieldFormula?.id) {
                const defaultFormula = item.default_formula || {};
                const usedIds = defaultFormula.used_cf_ids_for_formula || [];
                return {
                    ...item,
                    default_formula: {
                        ...defaultFormula,
                        used_cf_ids_for_formula: [...usedIds, id].join(","),
                    },
                };
            }
            return item;
        })
    );
};

const insertOperator = (value) => {
    insertAtCursor(value);
};

const handleBackspace = () => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;

    if (start === 0 && end === 0) return; // Nothing to delete

    const newDisplay =
        displayFormula.slice(0, start - 1) + displayFormula.slice(end);
    const newBackend =
        backendFormula.slice(0, start - 1) + backendFormula.slice(end);

    setDisplayFormula(newDisplay);
    setBackendFormula(newBackend);

    // Move cursor one step back
    setTimeout(() => {
        textarea.focus();
        const newPos = start - 1;
        textarea.setSelectionRange(newPos, newPos);
    }, 0);
};

    const clearFormula = () => {
        setDisplayFormula("");
        setBackendFormula("");
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        if(!backendFormula){
            if(selectedCustomFieldFormula?.default_formula?.id && is_default_configuration){
                dispatch(deleteCustomFieldItemFormula(selectedCustomFieldFormula?.default_formula?.id, handleCloseModel));
            }else{
                setCustomItemConfigurationList(prev => prev.map(item => {
                    if (item?.id === selectedCustomFieldFormula?.id) {
                        return { ...item, item_custom_field_default_formula: {...item.item_custom_field_default_formula, formula: null}, default_formula: {...item.default_formula, formula: null} }; //default_formula: {...item.default_formula, formula: backendFormula},
                    }else{
                        return { ...item, item_custom_field_default_formula: {...item.item_custom_field_default_formula, formula: null}, default_formula: {...item.default_formula, formula: null} };
                    }
                }))
                return handleCloseModel();
            }
        }
        const isValid = IsFormulaValid(backendFormula)
        if (!isValid && backendFormula) {
             dispatch(
                 errorToast({
                     text: "Invalid formula",
                     type: toastType.ERROR,
                 })
             );
            return;
        }
        else{
            if(type === 'qty') {
                const response = {
                    formula : backendFormula,
                    is_system_field: 1,
                    system_field_name: 'Quantity',
                    used_cf_ids_for_formula: storeAddedVariableId || "",
                }
                if(backendFormula){
                    dispatch(addItemMasterQtyCustomField(response, handleCloseModel, customFieldItemMaster?.id));
                }
                setCustomItemConfigurationList(prev => prev.map(item => {
                    if (item?.id === selectedCustomFieldFormula?.id) {
                        return { ...item, default_formula: {...item.default_formula, formula: backendFormula}}; //item_custom_field_default_formula: {...item.item_custom_field_default_formula, formula: backendFormula}
                    }
                    return item;
                }))
                handleCloseModel();
            }else{
                setCustomItemConfigurationList(prev => prev.map(item => {
                    if (item?.id === selectedCustomFieldFormula?.id) {
                        return { ...item, item_custom_field_default_formula: {...item.item_custom_field_default_formula, formula: backendFormula} }; //default_formula: {...item.default_formula, formula: backendFormula},
                    }
                    return item;
                }))
                handleCloseModel();
            }
        }
    }

    return (
        <Modal show={show} onHide={handleCloseModel} size="md" centered backdrop={true}>
            <div className="modal-header py-3">
                <h5 className="modal-title">
                    {displayFormula ? "Edit Formula" : "Add Formula"}
                </h5>
                <button
                    type="button"
                    className="btn btn-icon btn-sm btn-active-light-primary ms-2"
                    onClick={handleCloseModel}
                >
                    <Close />
                </button>
            </div>
            <Modal.Body className="calculation_body">
                <form className="calculation_form">
                    <Form.Group className="position-relative form-floating-group w-100 mb-2">
                        <textarea
                            ref={textareaRef}
                            className="form-control floating-label-input"
                            placeholder=""
                            value={displayFormula}
                            // required
                            onChange={handleChange}
                        ></textarea>
                        <Form.Label htmlFor="note" className="bg-transparent">
                            Set Formula
                        </Form.Label>
                    </Form.Group>
                    <Row className="mb-4">
                        <Col sm={6}>
                            <div className="calculation_result">
                                {filteredCustomItemConfigurationList?.filter(item => item?.id !== customFieldItemMaster?.id && item?.status)?.map((item, index) => (
                                    <div key={index} className="fs-16 text-primary mb-1 cursor-pointer" onClick={() => insertToFormula(item.label_name, item.id)}>
                                        {`${"{ " + item?.label_name + " }"}`}
                                    </div>
                                ))}
                            </div>
                        </Col>
                        <Col sm={6}>
                            <div className="calculation_button-all">
                                <table className="d-flex justify-content-end">
                                    <tbody>
                                       <tr>
                                            <td><button type="button" onClick={() => insertOperator("%")}>%</button></td>
                                            <td><button type="button" onClick={() => insertOperator("/")}>÷</button></td>
                                            <td><button type="button" onClick={clearFormula}>C</button></td>
                                            <td><button type="button" onClick={handleBackspace}>⌫</button></td>
                                        </tr>
                                        <tr>
                                            {[7, 8, 9].map((n) => (
                                                <td key={n}>
                                                    <button type="button" onClick={() => insertOperator(n.toString())}>{n}</button>
                                                </td>
                                            ))}
                                            <td><button type="button" onClick={() => insertOperator("*")}>*</button></td>
                                        </tr>
                                        <tr>
                                            {[4, 5, 6].map((n) => (
                                                <td key={n}>
                                                    <button type="button" onClick={() => insertOperator(n.toString())}>{n}</button>
                                                </td>
                                            ))}
                                            <td><button type="button" onClick={() => insertOperator("-")}>-</button></td>
                                        </tr>
                                        <tr>
                                            {[1, 2, 3].map((n) => (
                                                <td key={n}>
                                                    <button type="button" onClick={() => insertOperator(n.toString())}>{n}</button>
                                                </td>
                                            ))}
                                            <td><button type="button" onClick={() => insertOperator("+")}>+</button></td>
                                        </tr>
                                        <tr>
                                            <td><button type="button" onClick={() => insertOperator("0")}>0</button></td>
                                            <td><button type="button" onClick={() => insertOperator(".")}>.</button></td>
                                            <td><button type="button" onClick={() => insertOperator("(")}>(</button></td>
                                            <td><button type="button" onClick={() => insertOperator(")")}>)</button></td>
                                        </tr>
                                        <tr>
                                            <td colSpan={2} className="w-100" ><button className="w-100" type="button" onClick={() => insertOperator("Multiple()")}>Multiple</button></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </Col>
                    </Row>
                    <div className="d-flex gap-3">
                        <button type="button" className="btn btn-primary w-50" onClick={handleSubmit}>
                            Save
                        </button>
                        <button type="button" className="btn btn-secondary w-50" onClick={handleCloseModel}>
                            Close
                        </button>
                    </div>
                </form>
            </Modal.Body>
        </Modal>
    );
};

export default CustomFieldCalculation;
