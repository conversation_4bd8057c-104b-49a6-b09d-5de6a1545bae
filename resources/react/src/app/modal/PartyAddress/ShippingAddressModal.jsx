import React, { useContext, useEffect, useRef, useState } from "react";
import { Col, Form, Modal, Row } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { StateContext } from "../../../context/StateContext";
import CountrySelect from "../../../shared/CountrySelect";
import { FormInput } from "../../../components/ui/Input";
import Close from "../../../assets/images/svg/close";
import {
    addShippingAddressDetail,
    updateShippingAddressDetail,
} from "../../../store/shippingAddress/shippingAddressSlice";
import { CheckGstValidate } from "../../../shared/calculation";
import { fetchGstData } from "../../../store/gst/gstSlice";
import { convertNameIntoCamelCase, fetchGstDetail } from "../../../shared/prepareData";
import { addToast } from "../../../store/actions/toastAction";
import { toastType } from "../../../constants";
import { useParams } from "react-router-dom";

const ShippingAddressModel = ({
    show,
    handleClose,
    setLocalDispatchAddress,
    data,
    setUpdateAddress,
    localDispatchAddress,
    name,
    isEditable,
    isLedgerModel,
    isLedgeScreen,
    shipping_address_type,
    setSelectedAddress,
}) => {
    const dispatch = useDispatch();
    const shippingRef = useRef(null);
    const {
        countryId,
        setCountryId,
        stateId,
        setStateId,
        cityId,
        setCityId,
        gstQuote,
        isInitialDataLoaded,
        setIsInitialDataLoaded,
        stateName,
        setStateName,
        cityName,
        setCityName,
        partyAddress,
        setPartyAddress,
        isNewAddressforLedgerMaster,
        setIsNewAddressforLedgerMaster,
        saveFeature,
        setSaveFeature
    } = useContext(StateContext);

    const { id } = useParams();
    const company = useSelector(state => state?.company?.company);
    const [GSTError, setGSTError] = useState("");

    const { Country, State, City } = CountrySelect({
        countryId,
        setCountryId,
        stateId,
        setStateId,
        cityId,
        setCityId,
        stateName,
        setStateName,
        cityName,
        setCityName,
    });
    const [localGSTData, setLocalGSTData] = useState({});

    useEffect(() => {
        if (show && shippingRef.current) {
            shippingRef.current.focus();
        }
    }, [show]);

    useEffect(() => {
        const dispatchData = data;
        setUpdateAddress(prev => ({
            ...prev,
            address_1: dispatchData?.address_1 || "",
            address_name: dispatchData?.address_name || "",
            address_2: dispatchData?.address_2 || "",
            party_name_same_as_address_name: dispatchData?.party_name_same_as_address_name ?? true,
            country_id: dispatchData?.country_id || "",
            state_id: dispatchData?.state_id || "",
            city_id: dispatchData?.city_id || "",
            pin_code: dispatchData?.pin_code || "",
            gstin: dispatchData?.shipping_gstin || "",
            party: dispatchData?.shipping_name || "",
            state_name: stateName || "",
            city_name: cityName || "",
        }));
        setCountryId(dispatchData?.country_id || 101);
        setStateId(dispatchData?.state_id);
        setCityId(dispatchData?.city_id);
        setStateName(dispatchData?.state_name);
        setCityName(dispatchData?.city_name);
    }, [name]);

    useEffect(() => {
        setUpdateAddress(prev => ({
            ...prev,
            country_id: countryId,
            state_id: stateId,
            city_id: cityId,
            state_name: stateName || "",
            city_name: cityName || "",
        }));
    }, [countryId, stateId, cityId]);

    const handleSubmit = e => {
        e.preventDefault();
        // if(saveFeature){
        //     setIsInitialDataLoaded((prev) => [...prev, ...data])
        // }
        if (
            !data?.address_1 &&
            !data?.address_2 &&
            !data?.country_id &&
            !data?.state_id &&
            !data?.city_id &&
            !data?.pin_code
        ) {
            handleCloseModal();
            return;
        }
        const formData = new FormData();
        formData.append("address_1", data?.address_1?.trim() ?? "");
        formData.append("address_name", data?.address_name?.trim() ?? "");
        formData.append("party_name_same_as_address_name", data?.party_name_same_as_address_name == true ? 1 : 0);
        formData.append("address_2", data?.address_2?.trim() ?? "");
        formData.append("country_id", data?.country_id);
        formData.append("state_id", data?.state_id ?? "");
        formData.append("city_id", data?.city_id ?? "");
        formData.append("pin_code", data?.pin_code?.trim() ?? "");
        formData.append("shipping_gstin", data?.gstin?.trim() ?? "");
        formData.append("shipping_name", data?.party?.trim() ?? "");
        formData.append("customer_id", gstQuote?.party_ledger_id || id);
        if (isEditable) {
            const updateData = [...localDispatchAddress];
            updateData[data?.index] = {
                ...data,
                address_1: data?.address_1?.trim(),
                address_2: data?.address_2?.trim(),
                address_name: data?.address_name?.trim(),
                gstin: data?.gstin?.trim(),
                shipping_name: data?.party,
                state_name: stateName || "",
                city_name: cityName || "",
                party_name_same_as_address_name: data?.party_name_same_as_address_name,
            };
            setPartyAddress({
                ...partyAddress,
                shippingAddress: {
                    ...partyAddress?.shippingAddress,
                    address_1: data?.address_1,
                    address_name: data?.address_name,
                    address_2: data?.address_2,
                    gstin: data?.gstin,
                    shipping_gstin: data?.gstin,
                    shipping_name: data?.party,
                    country_id: data?.country_id || 101,
                    state_id: data?.state_id,
                    city_id: data?.city_id,
                    state_name: stateName || "",
                    city_name: cityName || "",
                    party_name_same_as_address_name: data?.party_name_same_as_address_name,
                },
            });
            setLocalDispatchAddress(updateData);
            dispatch(
                addToast({
                    text: "Shipping Address Updated Successfully",
                    type: toastType.ADD_TOAST,
                })
            );
            handleCloseModal();
            if (data?.id) {
                data.customer_id = gstQuote?.party_ledger_id;
                dispatch(
                    updateShippingAddressDetail(
                        data?.id,
                        {
                            ...data,
                            address_1: data?.address_1?.trim(),
                            address_2: data?.address_2?.trim(),
                            address_name: data?.address_name?.trim(),
                            shipping_gstin: data?.gstin,
                            shipping_name: data?.party,
                            state_name: stateName || "",
                            city_name: cityName || "",
                            customer_id: gstQuote?.party_ledger_id || id,
                            party_name_same_as_address_name: data?.party_name_same_as_address_name ? 1 : 0
                        },
                        "",
                        isLedgerModel ? "" : shipping_address_type,
                        isLedgerModel ? "" : id
                    )
                );
            }
        } else if (isLedgerModel || saveFeature || isLedgeScreen) {
            if (gstQuote?.party_ledger_id || isLedgeScreen) {
                dispatch(
                    addShippingAddressDetail(
                        formData,
                        handleCloseModal,
                        "",
                        gstQuote?.party_ledger_id,
                        id,
                        isLedgeScreen,
                        isLedgerModel ? "" : shipping_address_type,
                        isLedgerModel ? "" : id,
                        partyAddress,
                        setPartyAddress,
                        data,
                        stateName,
                        cityName
                    )
                );
            }

            handleCloseModal();
            setSelectedAddress(localDispatchAddress?.length);
            if(isLedgeScreen){
                setIsNewAddressforLedgerMaster(true)
            }
        } else {
            dispatch(
                addToast({
                    text: "Shipping Address Created Successfully",
                    type: toastType.ADD_TOAST,
                })
            );
            handleCloseModal();
            setPartyAddress({
                ...partyAddress,
                shippingAddress: {
                    address_1: data?.address_1,
                    address_name: data?.address_name,
                    address_2: data?.address_2,
                    gstin: data?.gstin,
                    shipping_gstin: data?.gstin,
                    shipping_name: data?.party,
                    country_id: data?.country_id || 101,
                    state_id: data?.state_id,
                    city_id: data?.city_id,
                    state_name: stateName || "",
                    city_name: cityName || "",
                    party_name_same_as_address_name: data?.party_name_same_as_address_name,
                },
            });
            setSelectedAddress(localDispatchAddress?.length);
            setLocalDispatchAddress(prev => [
                ...prev,
                {
                    ...data,
                    shipping_address_id: localDispatchAddress?.length + 1,
                    address_1: data?.address_1?.trim(),
                    address_name: data?.address_name?.trim(),
                    address_2: data?.address_2?.trim(),
                    gstin: data?.gstin?.trim(),
                    shipping_gstin: data?.gstin?.trim(),
                    shipping_name: data?.party,
                    state_name: stateName || "",
                    city_name: cityName || "",
                    party_name_same_as_address_name: data?.party_name_same_as_address_name,
                },
            ]);
        }

        setUpdateAddress("");
        setCountryId(101);
        setStateId("");
        setCityId("");
    };

    const handleCloseModal = () => {
        handleClose();
        setUpdateAddress("");
        setCountryId(101);
        setStateId("");
        setCityId("");
    };

    const onChange = e => {
        const { name, value } = e.target;
        if (name === "party") {
            setUpdateAddress({ ...data, [name]: value, shipping_name: value });
        } else {
            setUpdateAddress({ ...data, [name]: value });
        }
    };

    const onChangeAdress = (e) =>{
        const {value} = e.target;
        setUpdateAddress((prevState) => {
            const updatedState = { ...prevState, shipping_name: value, party:value };
            if (prevState?.party_name_same_as_address_name) {
                updatedState.address_name = value;
            }
            return updatedState;
        });
    }

    useEffect(() => {
        if (localGSTData && localGSTData?.result) {
            let state = localGSTData?.result?.pradr?.addr?.stcd;
            let city = localGSTData?.result?.pradr?.addr?.dst;
            const response = fetchGstDetail({ state, city });
            setCityId(response.city_id);
            setStateId(response.state_id);
            setCountryId(response.country_id);
            setCityName(city);
            setStateName(state);
            setUpdateAddress(prev => ({
                ...prev,
                ...(data?.party_name_same_as_address_name ? {address_name: convertNameIntoCamelCase(localGSTData?.result?.tradeNam)} : {}),
                shipping_gstin: localGSTData?.result?.gstin || data?.gstin,
                party: convertNameIntoCamelCase(localGSTData?.result?.tradeNam),
                shipping_name: data?.shipping_name || data?.party || "",
                address_1: localGSTData
                    ? localGSTData?.result?.pradr?.addr?.bno +
                      ", " +
                      localGSTData?.result?.pradr?.addr?.flno +
                      ", " +
                      localGSTData?.result?.pradr?.addr?.bnm
                    : "",
                address_2: localGSTData?.result?.pradr?.addr?.st || "",
                country_id: response.country_id || "",
                state_id: response.state_id || "",
                city_id: response.city_id || "",
                pin_code: localGSTData?.result?.pradr?.addr?.pncd || "",
            }));
        }
    }, [localGSTData, setLocalGSTData]);

    const changeGstDetail = e => {
        const { value } = e.target;
        const upperValue = value.toUpperCase();
        setUpdateAddress({ ...data, gstin: upperValue });
        // Validation logic
        if (upperValue.length === 15 && CheckGstValidate(upperValue)) {
            dispatch(fetchGstData(upperValue, true, setLocalGSTData));
            setGSTError(""); // Clear error on valid GSTIN
        } else if (upperValue.length > 0 && upperValue.length !== 15) {
            setGSTError("GSTIN must be exactly 15 characters long");
        } else if (upperValue.length === 0) {
            setGSTError(""); // Clear error if input is cleared
        } else {
            setGSTError("Please enter a valid GSTIN");
        }
    };
    const handleSameAsAddressName = (e) =>{
        const {checked} = e.target;
        setUpdateAddress({
            ...data,
            party_name_same_as_address_name: checked, // Toggle value based on checkbox state
            address_name: checked ? data?.shipping_name || data?.party : "", // Reset if unchecked
        });
    }

    return (
        <Modal
            show={show}
            onHide={handleCloseModal}
            className={`modal fade`}
            centered
            size="xl"
            backdrop={true}
        >
            <div className="modal-dialog-centered modal-xl">
                <div className="modal-content">
                    <div className="modal-header py-3">
                        <h5 className="modal-title">Shipping Address</h5>
                        <button
                            type="button"
                            className="btn btn-icon btn-sm btn-active-light-primary ms-2"
                            onClick={handleCloseModal}
                        >
                            <Close />
                        </button>
                    </div>
                    <div className="modal-body item-transaction">
                        <form onSubmit={handleSubmit}>
                            <Row>
                                <Col xl={6} lg={6} md={12} sm={12} className="px-3 mb-4">
                                    <Form.Group className="position-relative form-floating-group">
                                        <FormInput
                                            ref={shippingRef}
                                            className="floating-label-input capitalize"
                                            type="text"
                                            placeholder=""
                                            name="address_name"
                                            value={data?.address_name}
                                            onChange={onChange}
                                            disabled={data?.party_name_same_as_address_name}
                                        />
                                        <Form.Label>Address Name</Form.Label>
                                    </Form.Group>
                                </Col>
                                <Col xl={6} lg={6} md={12} sm={12} className="px-3 mb-4">
                                    <div className="form-check mt-2">
                                        <input
                                            type="checkbox"
                                            id="same_as_party_name"
                                            className="form-check-input"
                                            value={
                                                data?.party_name_same_as_address_name
                                            }
                                            checked={
                                                Boolean(data?.party_name_same_as_address_name) == true
                                            }
                                            onChange={handleSameAsAddressName}
                                        />
                                        <label
                                            title=""
                                            for="same_as_party_name"
                                            className="form-check-label"
                                        >
                                            Same as Party Name
                                        </label>
                                    </div>
                                </Col>
                            </Row>
                            <Row className="justify-content-between">
                                {company?.is_gst_applicable ? (
                                    <>
                                        <Col xl={6} lg={6} md={12} sm={12} className="px-3 mb-4">
                                            <Form.Group className="position-relative form-floating-group">
                                                <FormInput
                                                    className="floating-label-input text-uppercase"
                                                    type="text"
                                                    placeholder=""
                                                    name="gstin"
                                                    value={data?.gstin}
                                                    onChange={changeGstDetail}
                                                    minLength="15"
                                                    maxLength="15" // Restrict input length
                                                />
                                                <Form.Label>GSTIN</Form.Label>
                                                {GSTError && (
                                                    <span className="text-danger">{GSTError}</span>
                                                )}
                                            </Form.Group>
                                        </Col>
                                        <Col xl={6} lg={6} md={12} sm={12} className="px-3 mb-4">
                                            <Form.Group className="position-relative form-floating-group">
                                                <FormInput
                                                    className="floating-label-input capitalize"
                                                    type="text"
                                                    placeholder=""
                                                    name="party"
                                                    value={data?.party || data?.shipping_name}
                                                    onChange={onChangeAdress}
                                                />
                                                <Form.Label>Party Name</Form.Label>
                                            </Form.Group>
                                        </Col>
                                    </>
                                ) : (
                                    <>
                                        <Col xl={6} lg={6} md={12} sm={12} className="px-3 mb-4">
                                            <Form.Group className="position-relative form-floating-group">
                                                <FormInput
                                                    className="floating-label-input capitalize"
                                                    type="text"
                                                    placeholder=""
                                                    name="party"
                                                    value={data?.party || data?.shipping_name}
                                                    onChange={onChangeAdress}
                                                />
                                                <Form.Label>Party Name</Form.Label>
                                            </Form.Group>
                                        </Col>
                                        <Col></Col>
                                    </>
                                )}
                                <Col xl={6} lg={6} md={12} sm={12} className="px-3 mb-4">
                                    <Form.Group className="position-relative form-floating-group">
                                        <FormInput
                                            className="floating-label-input capitalize"
                                            type="text"
                                            placeholder=""
                                            name="address_1"
                                            value={data?.address_1}
                                            onChange={onChange}
                                        />
                                        <Form.Label>Address Line 1</Form.Label>
                                    </Form.Group>
                                </Col>
                                <Col xl={6} lg={6} md={12} sm={12} className="px-3 mb-4">
                                    <Form.Group className="position-relative form-floating-group">
                                        <FormInput
                                            className="floating-label-input capitalize"
                                            type="text"
                                            placeholder=""
                                            name="address_2"
                                            value={data.address_2}
                                            onChange={onChange}
                                        />
                                        <Form.Label>Address Line 2</Form.Label>
                                    </Form.Group>
                                </Col>
                                <Col xl={3} lg={6} md={12} sm={12} className="ps-3 mb-4">
                                    <Form.Group>
                                        <div className="input-group flex-nowrap">
                                            <div className="position-relative h-40px w-100 focus-shadow">
                                                <Country
                                                    name="country_id"
                                                    required={
                                                        company?.is_gst_applicable ? true : false
                                                    }
                                                />
                                            </div>
                                        </div>
                                    </Form.Group>
                                </Col>
                                <Col xl={3} lg={6} md={12} sm={12} className="ps-3 mb-4">
                                    <Form.Group>
                                        <div className="input-group flex-nowrap">
                                            <div className="position-relative h-40px w-100 focus-shadow">
                                                <State
                                                    name="state_id"
                                                    city_name="city_id"
                                                    required={
                                                        company?.is_gst_applicable ? true : false
                                                    }
                                                />
                                            </div>
                                        </div>
                                    </Form.Group>
                                </Col>
                                <Col xl={3} lg={6} md={12} sm={12} className="ps-3 mb-4">
                                    <Form.Group>
                                        <div className="input-group flex-nowrap">
                                            <div className="position-relative h-40px w-100 focus-shadow">
                                                <City name="city_id" />
                                            </div>
                                        </div>
                                    </Form.Group>
                                </Col>
                                <Col xl={3} lg={6} md={12} sm={12} className="px-3 mb-4">
                                    <Form.Group className="position-relative form-floating-group">
                                        <FormInput
                                            className="floating-label-input"
                                            type="text"
                                            placeholder=""
                                            name="pin_code"
                                            value={data?.pin_code}
                                            minLength="6"
                                            maxLength="6"
                                            onChange={onChange}
                                        />
                                        <Form.Label>Pincode</Form.Label>
                                    </Form.Group>
                                </Col>
                            </Row>
                            {!isEditable && gstQuote?.party_ledger_id && !isLedgerModel && (
                                <Form.Group>
                                    <div>
                                        <Form.Check
                                            type="checkbox"
                                            label="Save for Future"
                                            value={saveFeature}
                                            onChange={e => setSaveFeature(e.target.checked)}
                                        />
                                    </div>
                                </Form.Group>
                            )}
                            <div className="mt-4">
                                <button
                                    type="submit"
                                    name="action"
                                    className="btn btn-primary me-2"
                                >
                                    Save
                                </button>
                                <button
                                    type="button"
                                    onClick={handleCloseModal}
                                    name="action"
                                    className="btn btn-secondary me-2"
                                >
                                    Back
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </Modal>
    );
};

export default ShippingAddressModel;
