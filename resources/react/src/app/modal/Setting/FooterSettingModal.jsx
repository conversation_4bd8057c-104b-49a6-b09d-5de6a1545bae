import React, { useContext, useEffect, useMemo, useState } from "react";
import { Form, Row, Col, Modal } from "react-bootstrap";
import { StateContext } from "../../../context/StateContext";
import { FormInput } from "../../../components/ui/Input";
import EditModal from "../../modal/Setting/PrintSettingToggleEditModal";
import editIcon from "../../../assets/images/edit.svg";
import {
    deletePrintSettingCompanySignature,
    fetchPrintFooterSetting,
    getPdfCompanySetting,
    updatePrintFooterSetting,
    updatePrintToggle,
} from "../../../store/setting/settingSlice";
import { useDispatch, useSelector } from "react-redux";
import { UPDATE_SETTING_TYPE } from "../../../constants";
import WarningModal from "../../common/WarningModal";
import ReactSelect from "../../../components/ui/ReactSelect";
const FooterSettingModal = ({ type, pdfType }) => {
    const dispatch = useDispatch();
    const { setting, company } = useSelector(selector => selector);
    const is_gst_applicable = company?.company?.is_gst_applicable
    const { printFooterSetting } = setting;
    const { settings, print_setting } = printFooterSetting;
    const [isDeleteCompanySignature, setIsDeleteCompanySignature] = useState(false);
    const {
        isFooterSettingModel,
        openFooterSettingModel,
        closeFooterSettingModel,
        printSettingLabel,
        setPrintSettingLabel,
        storeSettingEditDetail,
        setStoreSettingEditDetail,
        isSloganEditModal,
        openSloganEditModal,
        closeSloganEditModal,
        userPermission
    } = useContext(StateContext);
    const transaction_type =
        type == 1
            ? "income_transaction"
            : type == 2
            ? "estimate_quote"
            : type == 3
            ? "delivery_challan"
            : "expense";
    const tableFooterLabel = printSettingLabel[transaction_type]?.find(pdf => pdf.type == pdfType);

    useEffect(() => {
        const printSettingMap = new Map(print_setting?.map(s => [s.key, s]));
        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                          ...item,
                          footer: {
                              ...item.footer,
                              invoice_labels: item.footer.invoice_labels
                              .filter(field =>
                                  is_gst_applicable == 1
                                      ? true
                                      : !field.hasOwnProperty("is_gst_applicable")
                              ).map(field => {
                                  const setting = printSettingMap.get(field.name) || {};
                                  return {
                                      ...field,
                                      value: setting.value ?? "",
                                      check_value: setting?.enable_disable_value,
                                  };
                              }),
                              settings: item.footer.settings.map(field => ({
                                  ...field,
                                  value: settings?.[field.name] ?? 0,
                                  qr_code_with_amount: settings?.qr_code_with_amount ?? 0,
                              })),
                          },
                      }
                    : item
            ),
        }));
    }, [settings, print_setting, pdfType]);

    useEffect(() => {
        if (isFooterSettingModel) {
            document.body.classList.add("enable-scroll");
        } else {
            document.body.classList.remove("enable-scroll");
        }
    }, [isFooterSettingModel]);

    const handleSubmit = e => {
        e.preventDefault();
        const InvoiceLabel = tableFooterLabel?.footer?.invoice_labels.reduce((acc, item) => {
            acc[item.name] = item.value;
            return acc;
        }, {});
        const InvoicePrintSeting = tableFooterLabel?.footer?.invoice_labels.reduce((acc, item) => {
            acc[item.check_key] = item.check_value;
            return acc;
        }, {});
        const InvoiceSettings = tableFooterLabel?.footer?.settings.reduce((acc, item) => {
            acc[item.name] = item.value;
            acc.qr_code_with_amount = item.qr_code_with_amount;
            return acc;
        }, {});
        const response = {
            invoice_labels: InvoiceLabel,
            settings: InvoiceSettings,
            print_setting: InvoicePrintSeting,
            ...(pdfType == 3 && { thermal_setting: true }),
        };
        dispatch(updatePrintFooterSetting(type, pdfType, response));
        closeFooterSettingModel();
    };

    const handleTableSettingModal = () => {
        if(!userPermission?.edit_print_setting) return
        openFooterSettingModel();
        dispatch(fetchPrintFooterSetting(type));
    };

    const handleToggle = e => {
        const { name, checked } = e.target;
        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                          ...item,
                          footer: {
                              ...item.footer,
                              settings: item.footer.settings.map(field => ({
                                  ...field,
                                  value: field.name === name ? (checked ? 1 : 0) : field.value,
                              })),
                          },
                      }
                    : item
            ),
        }));
        dispatch(
            updatePrintToggle(
                UPDATE_SETTING_TYPE.COMPANY_SETTING,
                {
                    key: name,
                    value: checked ? 1 : 0,
                },
                type,
                pdfType
            )
        );
    };

    const onChange = e => {
        const { name, value } = e.target;
        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                          ...item,
                          footer: {
                              ...item.footer,
                              invoice_labels: item.footer.invoice_labels.map(field => ({
                                  ...field,
                                  value: field.name === name ? value : field.value,
                              })),
                          },
                      }
                    : item
            ),
        }));
    };
    const onChangeCheckBox = e => {
        const { name, value, checked } = e.target;
        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                          ...item,
                          footer: {
                              ...item.footer,
                              invoice_labels: item.footer.invoice_labels.map(field => {
                                  return {
                                      ...field,
                                      check_value:
                                          field.check_key === name
                                              ? checked
                                                  ? 1
                                                  : 0
                                              : field.check_value,
                                  };
                              }),
                          },
                      }
                    : item
            ),
        }));
    };
    const onChangeQrCodeAmount = e => {
        const { value } = e;
        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                          ...item,
                          footer: {
                              ...item.footer,
                              settings: item.footer.settings.map(field => {
                                  return {
                                      ...field,
                                      qr_code_with_amount: value,
                                  };
                              }),
                          },
                      }
                    : item
            ),
        }));
    };

    const openEditModel = detail => {
        setStoreSettingEditDetail(detail);
        openSloganEditModal();
    };

    useEffect(() => {
            dispatch(getPdfCompanySetting(2));
    }, [isSloganEditModal]);

    const handleDeleteSignature = () => {
        dispatch(deletePrintSettingCompanySignature(type, pdfType));
        setIsDeleteCompanySignature(false);
    };

    const handleOpenDeleteLogoModel = () => {
        setIsDeleteCompanySignature(true);
        closeSloganEditModal();
    };

    return (
        <>
            <a
                href="javascript:void(0)"
                onClick={handleTableSettingModal}
                data-modal="footer"
                className={`${!userPermission?.edit_print_setting && "disabled"} d-flex gap-5 justify-content-between mb-2`}
            >
                <p className="mb-0 text-primary fw-5 fs-15">Footer Settings</p>
                <i className="fas fa-chevron-circle-right text-primary ms-2 fs-3"></i>
            </a>
            <Modal
                show={isFooterSettingModel}
                onHide={closeFooterSettingModel}
                className={`settings-modal custom-offcanvas-modal fade p-0 `}
            >
                <form>
                    <div className="offcanvas-header bg-white border-bottom">
                        <h3 className="mb-0">Footer Settings</h3>
                        <div className="d-flex gap-5 align-items-center">
                            <button
                                type="submit"
                                onClick={handleSubmit}
                                className="btn btn-primary"
                            >
                                Save
                            </button>
                            <button
                                className="btn close-button p-0"
                                onClick={closeFooterSettingModel}
                                type="button"
                            >
                                &times;
                            </button>
                        </div>
                    </div>
                    <div className="offcanvas-body bg-white">
                        <div className="pb-2">
                            {tableFooterLabel?.footer?.settings?.length > 0 ? (
                                <h4 className="fs-16 fw-semibold mb-4">Show / Hide</h4>
                            ) : (
                                ""
                            )}
                            <div className="w-100 d-flex flex-column gap-3 mb-5">
                                {tableFooterLabel?.footer?.settings?.filter(detail => !(is_gst_applicable == 0 && detail?.label === "HSN Summary"))?.map(detail => (
                                    <div className="d-flex justify-content-between align-items-center">
                                        <div
                                            className="d-flex align-items-center gap-10px"
                                            for="flexSwitchCheckChecked4"
                                        >
                                            <p className="fs-15 fw-medium mb-0">{detail?.label}</p>
                                            {detail?.is_edit ? (
                                                <>
                                                    <a
                                                        href="javascript:void(0)"
                                                        onClick={() => openEditModel(detail)}
                                                        className="d-flex gap-5 justify-content-between mb-1"
                                                        data-modal="table"
                                                    >
                                                        <img src={editIcon} alt="edit-icon" />
                                                    </a>
                                                </>
                                            ) : (
                                                ""
                                            )}
                                            {detail?.is_dropdown ? (
                                                <div className="min-w-150px focus-shadow">
                                                    <ReactSelect
                                                        options={[
                                                            {
                                                                label: "With Amount",
                                                                value: 1,
                                                            },
                                                            {
                                                                label: "Without Amount",
                                                                value: 0,
                                                            },
                                                        ]}
                                                        defaultValue={1}
                                                        value={detail?.qr_code_with_amount}
                                                        name="qr_code_with_amount"
                                                        onChange={onChangeQrCodeAmount}
                                                        className="h-40px border-0"
                                                        customLabel="modal"
                                                        portal={true}
                                                    />
                                                </div>
                                            ) : (
                                                ""
                                            )}
                                        </div>
                                        <div className="form-check form-switch">
                                            <input
                                                className="form-check-input m-0"
                                                type="checkbox"
                                                role="switch"
                                                id="flexSwitchCheckChecked4"
                                                name={detail?.name}
                                                checked={detail.value == 1}
                                                value={detail.value}
                                                onChange={handleToggle}
                                            />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                        <div className="pb-2">
                            <h4 className="fs-16 fw-semibold mb-5">Footer Labels</h4>
                            <Row>
                                {tableFooterLabel?.footer?.invoice_labels?.map(label => (
                                    <>
                                        <Col sm={6} className="mb-6">
                                            <Form.Group className="position-relative form-floating-group">
                                                <FormInput
                                                    className="floating-label-input"
                                                    type="text"
                                                    placeholder=""
                                                    name={label.name}
                                                    value={label.value}
                                                    onChange={onChange}
                                                />
                                                <Form.Label>{is_gst_applicable && label?.label == "Sub Total" ? "Taxable Value" : label?.label}</Form.Label>
                                                {label?.check_key ? (
                                                    <Form.Check
                                                        type="checkbox"
                                                        label=""
                                                        checked={label?.check_value == 1}
                                                        name={label.check_key}
                                                        value={label.value}
                                                        onChange={onChangeCheckBox}
                                                    />
                                                ) : (
                                                    ""
                                                )}
                                            </Form.Group>
                                        </Col>
                                    </>
                                ))}
                            </Row>
                        </div>
                    </div>
                    <div className="offcanvas-footer">
                        <div className="d-flex gap-4 fixed-buttons rounded-0">
                            <button
                                onClick={handleSubmit}
                                type="submit"
                                className="btn btn-primary"
                            >
                                Save
                            </button>
                            <button
                                type="button"
                                onClick={closeFooterSettingModel}
                                className="btn btn-secondary"
                            >
                                Back
                            </button>
                        </div>
                    </div>
                </form>
            </Modal>
            <WarningModal
                show={isDeleteCompanySignature}
                title="Delete!"
                message='Are you sure want to delete this "Signature" ?'
                showCancelButton
                showConfirmButton
                confirmText="Yes, Delete"
                cancelText="No, Cancel"
                handleClose={() => setIsDeleteCompanySignature(false)}
                handleSubmit={handleDeleteSignature}
            />
            <EditModal
                name={storeSettingEditDetail?.is_edit}
                editName={storeSettingEditDetail?.name}
                inputLabel={storeSettingEditDetail?.placeholder}
                show={isSloganEditModal}
                open={openSloganEditModal}
                handleClose={closeSloganEditModal}
                handleOpenDeleteLogoModel={handleOpenDeleteLogoModel}
            />
        </>
    );
};

export default FooterSettingModal;
