import React, { useContext, useEffect, useState } from "react";
import { Form, Row, Col, Modal } from "react-bootstrap";
import { StateContext } from "../../../context/StateContext";
import { FormInput } from "../../../components/ui/Input";
import EditModal from "./PrintSettingToggleEditModal";
import editIcon from "../../../assets/images/edit.svg";
import {
    deletePrintSettingCompanyLogo,
    fetchPrintHeaderDuplicateData,
    fetchPrintHeaderSetting,
    fetchPrintHeaderTriplicateData,
    getPdfCompanySetting,
    getPdfPropSetting,
    updatePrintHeaderSetting,
    updatePrintHeaderToggle,
    updatePrintToggle,
} from "../../../store/setting/settingSlice";
import { useDispatch, useSelector } from "react-redux";
import { useFormik } from "formik";
import WarningModal from "../../common/WarningModal";
import { UPDATE_SETTING_TYPE } from "../../../constants";
const HeaderSettingModal = ({ type, pdfType }) => {
    const dispatch = useDispatch();
    const { setting } = useSelector(selector => selector);
    const { printHeaderSetting } = setting;
    const { settings, invoice_labels, customer_labels } = printHeaderSetting;
    const {
        isHeaderSettingModel,
        openHeaderSettingModel,
        closeHeaderSettingModel,
        isEmailEditModal,
        openEmailEditModal,
        closeEmailEditModal,
        // isSloganEditModal,
        // openSloganEditModal,
        // closeSloganEditModal,
        isLogoEditModal,
        openLogoEditModal,
        closeLogoEditModal,
        isMobileEditModal,
        openMobileEditModal,
        closeMobileEditModal,
        addCustomField,
        setAddCustomField,
        storeSettingEditDetail,
        setStoreSettingEditDetail,
        printSettingLabel,
        setPrintSettingLabel,
        userPermission
    } = useContext(StateContext);
    const transaction_type =
        type == 1
            ? "income_transaction"
            : type == 2
            ? "estimate_quote"
            : type == 3
            ? "delivery_challan"
            : "expense";
    const tableHeaderLabel = printSettingLabel[transaction_type]?.find(pdf => pdf.type == pdfType);
    const [isDeleteCompanyLogo, setIsDeleteCompanyLogo] = useState(false);
    const [isSloganEditModal, setIsSloganEditModal] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const initialValues = {
        settings: {
            logo: "",
            mobile_number: "",
            email: "",
            po_number: "",
            pan_no: "",
            transport_details: "",
            ship_to_details: "",
            show_slogan: "",
            dispatch_from_details: "",
        },
        invoice_labels: {
            gstin: null,
            tel: null,
            bill_to: "",
            ship_to: null,
            invoice_details: null,
            dispatch_from: null,
            invoice_number: null,
            invoice_date: null,
            po_number_label: null,
            po_date: null,
            transport_name: null,
            transport_vehicle_number: "",
            e_way_bill_no: null,
            e_way_bill_date: null,
            ack_no: null,
            ack_date: null,
        },
        customer_labels: [],
    };

    const handleSubmit = () => {
        if (isSubmitting) return; // Prevent duplicate submission
        setIsSubmitting(true);
        const InvoiceLabel = tableHeaderLabel?.header?.invoice_labels.reduce((acc, item) => {
            acc[type == 2 ? (item.estimate_quote || name) : type == 3 ? (item.delivery_challan || item.name) : type == 4 ? (item.expense || item.name) : item.name] = item.value;
            return acc;
        }, {});
        const InvoiceCheckbox = tableHeaderLabel?.header?.invoice_labels.reduce((acc, item) => {
            acc[type == 2 ? (item.estimate_check_key || item?.check_key) : type == 4 ? (item.expense_check_key || item.check_key) : item.check_key] = item.check_value;
            return acc;
        }, {});
        const InvoiceSettings = tableHeaderLabel?.header?.settings.reduce((acc, item) => {
            acc[type == 2 ? (item.estimate_quote || item.name) : type == 3 ? (item.delivery_challan || item.name) : type == 4 ? (item.expense || item.name) : item.name] = item.value;
            return acc;
        }, {});
        const customLabel = addCustomField.reduce((acc, item) => {
            acc[item.name] = item.value;
            return acc;
        }, {});
        const response = {
            invoice_settings: {...InvoiceLabel, ...InvoiceSettings, ...InvoiceCheckbox},
            customer_labels: customLabel,
            ...(pdfType == 3 && {thermal_setting:true})
        };
        dispatch(updatePrintHeaderSetting(type, pdfType, response, closeHeaderSettingModel, setIsSubmitting));
    };

    const formik = useFormik({
        initialValues: initialValues,
        onSubmit: values => handleSubmit(values),
    });

    useEffect(() => {
        if (isHeaderSettingModel) {
            document.body.classList.add("enable-scroll");
        } else {
            document.body.classList.remove("enable-scroll");
        }
    }, [isHeaderSettingModel]);
    useEffect(() => {
        if(storeSettingEditDetail?.name == "prop_details"){
            dispatch(getPdfPropSetting());
        }else{
            dispatch(getPdfCompanySetting(isLogoEditModal ? 1 : 3));
        }
        if(storeSettingEditDetail?.placeholder == "Duplicate"){
            dispatch(fetchPrintHeaderDuplicateData());
        } else if(storeSettingEditDetail?.placeholder == "Triplicate"){
            dispatch(fetchPrintHeaderTriplicateData());
        }
    }, [isSloganEditModal, isLogoEditModal, isEmailEditModal, isMobileEditModal, storeSettingEditDetail]);

    useEffect(() => {
        const printLabelMap = new Map(invoice_labels?.map(s => [s.key, s]));
        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                          ...item,
                          header: {
                              //   ...item.detail,
                              invoice_labels: item.header.invoice_labels.map(field => {
                                const result =
                                    type == 2
                                        ? field.estimate_quote || field.name
                                        : type == 4
                                        ? field.expense || field.name
                                        : field.name;
                                    const invoice_label = printLabelMap.get(result || {})
                                  return {
                                      ...field,
                                      value: invoice_label?.value || "",
                                      check_value: settings?.[type == 2 ? (field.estimate_check_key) : type == 3 ? (field.delivery_challan || field.check_key) : type == 4 ? field.check_key : field.check_key] ?? 0,
                                  };
                              }),
                              settings: item.header.settings.map(field => {
                                  return {
                                    ...field,
                                    value: settings?.[type == 2 ? (field.estimate_quote || field.name) : type == 3 ? (field.delivery_challan || field.name) : type == 4 ? (field.expense ? field.expense : field.name) : field.name] ?? 0,
                                  }
                                }),
                          },
                      }
                    : item
            ),
        }));
        const customFieldList = customer_labels
            ? Object.entries(customer_labels).map(([key, value]) => {
                  return {
                      name: key,
                      value: value,
                  };
              })
            : [];
        if (customFieldList?.length !== 0) {
            setAddCustomField(customFieldList);
        }
    }, [settings, pdfType]);

    const openHeaderSetting = () => {
        if(!userPermission?.edit_print_setting) return
        openHeaderSettingModel();
        dispatch(fetchPrintHeaderSetting(type));
    };
    const closeHeaderSetting = () => {
        closeHeaderSettingModel();
    };

    const handleToggle = e => {
        const { name, checked } = e.target;
        const mutuallyExclusive = ['duplicate', 'triplicate'];

        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                        ...item,
                        header: {
                            ...item.header,
                            // settings: item.header.settings.map(field => ({
                            //     ...field,
                            //     value: field.name === name ? (checked ? 1 : 0) : field.value,
                            // })),
                            settings: item.header.settings.map(field => {
                                if (field.name === name) {
                                    return { ...field, value: checked ? 1 : 0 };
                                }
        
                                // If checking one, uncheck the other in the mutually exclusive group
                                if (
                                    mutuallyExclusive.includes(name) &&
                                    mutuallyExclusive.includes(field.name) &&
                                    field.name !== name
                                ) {
                                    return { ...field, value: 0 };
                                }
        
                                return field;
                            }),
                        },
                    }
                    : item
            ),
        }));

        dispatch(
            updatePrintToggle(UPDATE_SETTING_TYPE.COMPANY_SETTING, {
                key: name,
                value: checked ? 1 : 0,
            }, type, pdfType)
        );
    };

    const onChange = e => {
        const { name, value } = e.target;
        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                          ...item,
                          header: {
                              ...item.header,
                              invoice_labels: item.header.invoice_labels.map(field => ({
                                  ...field,
                                  value: field.name === name ? value : field.value,
                              })),
                          },
                      }
                    : item
            ),
        }));
    };

    const handleOpenDeleteLogoModel = () => {
        setIsDeleteCompanyLogo(true);
        setIsSloganEditModal(false)
        closeLogoEditModal();
    };

    const handleDeleteLogo = () => {
        dispatch(deletePrintSettingCompanyLogo(type, pdfType));
        setIsDeleteCompanyLogo(false);
    };

    const handleAddCustomField = () => {
        setAddCustomField([...addCustomField, { name: "", value: "" }]);
    };
    const handleChangeCustomField = (e, index, type) => {
        const { value } = e.target;
        const customField = [...addCustomField];
        if(value !== " "){
            customField[index][type] = value;
        }
        setAddCustomField(customField);
    };
    const removeCustomField = index => {
        const filterData = addCustomField?.filter((item, i) => i !== index);
        setAddCustomField(filterData);
    };

    const openEditModel = detail => {
        setStoreSettingEditDetail(detail);
        setIsSloganEditModal(true)
        if(detail?.placeholder == "Logo"){
            openLogoEditModal();
        }
    };
    const handleCloseModel = () =>{
        setIsSloganEditModal(false)
        closeLogoEditModal()
        closeEmailEditModal()
        closeMobileEditModal()
    }

    const onChangeCheckBox = e => {
        const { name, value, checked } = e.target;
        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                          ...item,
                          header: {
                              ...item.header,
                              invoice_labels: item.header.invoice_labels.map(field => {
                                  return {
                                      ...field,
                                      check_value:
                                          field.check_key === name
                                              ? checked
                                                  ? 1
                                                  : 0
                                              : field.check_value,
                                  };
                              }),
                          },
                      }
                    : item
            ),
        }));
    };

    return (
        <>
            <a
                href="javascript:void(0)"
                onClick={openHeaderSetting}
                data-modal="header"
                className={`${!userPermission?.edit_print_setting && "disabled"} d-flex gap-5 justify-content-between mb-2`}
            >
                <p className="mb-0 text-primary fw-5 fs-15">Header Settings</p>
                <i className="fas fa-chevron-circle-right text-primary ms-2 fs-3"></i>
            </a>
            <Modal
                show={isHeaderSettingModel}
                onHide={closeHeaderSetting}
                className={`settings-modal custom-offcanvas-modal fade p-0 `}
            >
                <form onSubmit={formik.handleSubmit}>
                    <div className="offcanvas-header bg-white border-bottom">
                        <h3 className="mb-0">Header Settings</h3>
                        <div className="d-flex gap-5 align-items-center">
                            <button type="submit" className="btn btn-primary">
                                Save
                            </button>
                            <button
                                className="btn close-button p-0"
                                onClick={closeHeaderSettingModel}
                                type="button"
                            >
                                &times;
                            </button>
                        </div>
                    </div>
                    <div className="offcanvas-body bg-white">
                        <div className="pb-2">
                            {tableHeaderLabel?.header?.settings?.length > 0 ? (
                            <h4 className="fs-16 fw-semibold mb-4">Show / Hide</h4>
                            ) : ""}
                            <div className="w-100 d-flex flex-column gap-3 mb-5">
                                {tableHeaderLabel?.header?.settings?.map(detail => (
                                    <div className="d-flex justify-content-between align-items-center">
                                        <div
                                            className="d-flex align-items-center gap-10px"
                                            for="flexSwitchCheckChecked4"
                                        >
                                            <p className="fs-15 fw-medium mb-0">{detail?.label}</p>
                                            {detail?.is_edit ? (
                                                <>
                                                    <a
                                                        href="javascript:void(0)"
                                                        onClick={()=>openEditModel(detail)}
                                                        className="d-flex gap-5 justify-content-between mb-1"
                                                        data-modal="table"
                                                    >
                                                        <img src={editIcon} alt="edit-icon" />
                                                    </a>
                                                </>
                                            ) : (
                                                ""
                                            )}
                                        </div>
                                        <div className="form-check form-switch">
                                            <input
                                                className="form-check-input m-0"
                                                type="checkbox"
                                                role="switch"
                                                id="flexSwitchCheckChecked4"
                                                name={type == 4 ? (detail?.expense || detail?.name) : type == 2 ? (detail?.estimate_quote || detail?.name) : type == 3 ? (detail?.delivery_challan || detail?.name) : detail?.name}
                                                checked={detail.value == 1}
                                                value={detail.value}
                                                onChange={handleToggle}
                                            />
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                        <div className="pb-2">
                            <h4 className="fs-16 fw-semibold mb-5">Header Labels</h4>
                            <Row>
                                {tableHeaderLabel?.header?.invoice_labels?.map(label => (
                                    <>
                                        <Col sm={6} className="mb-6">
                                            <Form.Group className="position-relative form-floating-group">
                                                <FormInput
                                                    className="floating-label-input"
                                                    type="text"
                                                    placeholder=""
                                                    name={label.name}
                                                    value={label.value}
                                                    onChange={onChange}
                                                />
                                                <Form.Label>{label?.label}</Form.Label>
                                                {label?.check_key ? (
                                                    <Form.Check
                                                        type="checkbox"
                                                        label=""
                                                        checked={label?.check_value == 1}
                                                        name={label.check_key}
                                                        value={label.check_value}
                                                        onChange={onChangeCheckBox}
                                                    />
                                                ) : (
                                                    ""
                                                )}
                                            </Form.Group>
                                        </Col>
                                    </>
                                ))}
                            </Row>
                        </div>
                        {/* {pdfType != 3 ? ( */}
                            <div className="modal-content-box py-5 px-4">
                                <div className="d-flex  align-items-center justify-content-between w-100 mb-2">
                                    <h5 className="fs-18 text-primary fw-semibold mb-0">
                                        Custom field
                                    </h5>
                                    {addCustomField?.length <= 1 && (
                                        <button
                                            onClick={handleAddCustomField}
                                            type="button"
                                            className="btn btn-primary btn-sm"
                                        >
                                            Add+
                                        </button>
                                    )}
                                </div>
                                {addCustomField?.map((item, index) => (
                                    <Row className="mt-3">
                                        <Col sm={5} className="mb-6">
                                            <Form.Group className="position-relative form-floating-group">
                                                <FormInput
                                                    className="floating-label-input"
                                                    type="text"
                                                    placeholder=""
                                                    name="invoice_labels.gstin"
                                                    value={item?.name}
                                                    required
                                                    onChange={e =>
                                                        handleChangeCustomField(e, index, "name")
                                                    }
                                                />
                                                <Form.Label>Field Name</Form.Label>
                                            </Form.Group>
                                        </Col>
                                        <Col sm={5} className="mb-6">
                                            <Form.Group className="position-relative form-floating-group">
                                                <FormInput
                                                    className="floating-label-input"
                                                    type="text"
                                                    placeholder=""
                                                    name="invoice_labels.tel"
                                                    required
                                                    value={item?.value}
                                                    onChange={e =>
                                                        handleChangeCustomField(e, index, "value")
                                                    }
                                                />
                                                <Form.Label>Field Value</Form.Label>
                                            </Form.Group>
                                        </Col>
                                        <Col sm={2}>
                                            <button
                                                type="button"
                                                className="bg-transparent border-0 p-0 ps-3 w-100"
                                                data-id="2"
                                                onClick={() => removeCustomField(index)}
                                            >
                                                <i className="fas fs-2 fa-trash-alt text-danger mx-auto mt-2"></i>
                                            </button>
                                        </Col>
                                    </Row>
                                ))}
                            </div>
                        {/* ) : (
                            ""
                        )} */}
                    </div>
                    <div className="offcanvas-footer">
                        <div className="d-flex gap-4 fixed-buttons rounded-0">
                            <button type="submit" className="btn btn-primary">
                                Save
                            </button>
                            <button
                                type="button"
                                onClick={closeHeaderSettingModel}
                                className="btn btn-secondary"
                            >
                                Back
                            </button>
                        </div>
                    </div>
                </form>
            </Modal>
            <WarningModal
                show={isDeleteCompanyLogo}
                title="Delete!"
                message='Are you sure want to delete this "Logo" ?'
                showCancelButton
                showConfirmButton
                confirmText="Yes, Delete"
                cancelText="No, Cancel"
                handleClose={() => setIsDeleteCompanyLogo(false)}
                handleSubmit={handleDeleteLogo}
            />
            <EditModal
                name={storeSettingEditDetail?.is_edit}
                editName={storeSettingEditDetail?.name}
                inputLabel={storeSettingEditDetail?.placeholder}
                show={isSloganEditModal}
                handleClose={handleCloseModel}
                handleOpenDeleteLogoModel={handleOpenDeleteLogoModel}
            />
        </>
    );
};

export default HeaderSettingModal;
