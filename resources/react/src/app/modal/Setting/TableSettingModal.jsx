import React, { useContext, useEffect, useMemo, useState } from "react";
import { Form, Row, Col, Modal } from "react-bootstrap";
import { StateContext } from "../../../context/StateContext";
import { FormInput } from "../../../components/ui/Input";
import editIcon from "../../../assets/images/edit.svg";
import { useDispatch, useSelector } from "react-redux";
import {
    fetchPrintDetailSetting,
    updatePrintDetailSetting,
    updatePrintToggle,
} from "../../../store/setting/settingSlice";
import { useFormik } from "formik";
import { UPDATE_SETTING_TYPE } from "../../../constants";
const TableSettingModal = ({ isFetchData, type, pdfType }) => {
    const dispatch = useDispatch();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { setting, company } = useSelector(selector => selector);
    const is_gst_applicable = company?.company?.is_gst_applicable
    const { printDetailSetting } = setting;
    const { settings, print_setting } = printDetailSetting;
    const {
        isTableSettingModal,
        openTableSettingModal,
        closeTableSettingModal,
        printSettingLabel,
        setPrintSettingLabel,
        userPermission
    } = useContext(StateContext);
    const transaction_type =
        type == 1
            ? "income_transaction"
            : type == 2
            ? "estimate_quote"
            : type == 3
            ? "delivery_challan"
            : "expense";
    const tableDetailLabel = printSettingLabel[transaction_type]?.find(pdf => pdf.type == pdfType);

    const initialValues = {
        settings: {
            show_item_image: "0",
            currency_symbol: "0",
        },
        print_setting: {
            show_sale_hsn_sac: 0,
            show_sale_gst: 0,
            show_sale_qty: 0,
            show_sale_rate: 0,
            show_sale_rate_with_gst: 0,
            show_sale_mrp: 0,
            show_sale_discount: 0,
            show_sale_total_discount: 0,
            sale_uom_enable: 0,
            sale_dis_2_enable: 0,
        },
        invoice_labels: {
            sn: null,
            item_name: null,
            hsn_sac: null,
            gst: null,
            qty: null,
            rate: null,
            rate_with_gst: null,
            mrp: null,
            discount: null,
            total_discount: null,
            taxable_value: null,
            sale_uom_label: null,
            sale_dis_2_label: null,
        },
    };

    useEffect(() => {
        const printSettingMap = new Map(print_setting?.map(s => [s.key, s]));
        if(!print_setting) return
        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                          ...item,
                          detail: {
                              ...item.header,
                              total_labels: item.detail.total_labels.filter(field =>
                                is_gst_applicable == 1
                                    ? true
                                    : !field.hasOwnProperty("is_gst_applicable")
                            ).map(field => {
                                  const setting = printSettingMap.get(type == 4 ? (field?.expense || field?.name) : type == 2 ? (field?.estimate || field?.name) : pdfType == 3 ? field?.name : field.name) || {};
                                  return {
                                      ...field,
                                      value: setting.value ?? "",
                                      check_value: setting?.enable_disable_value,
                                      ...(setting?.extra_key ? {sub_check_value: setting?.extra_key} : {sub_check_value: 0}),
                                  };
                              }),
                              invoice_labels: item.detail.invoice_labels.filter(field =>
                                is_gst_applicable == 1
                                    ? true
                                    : !field.hasOwnProperty("is_gst_applicable")
                            ).map(field => {
                                  const setting = printSettingMap.get(type == 4 ? field?.expense : type == 2 ? field?.estimate : pdfType == 3 ? field?.thermal_name : field.name) || {};
                                  return {
                                      ...field,
                                      value: setting.value ?? "",
                                      check_value: setting?.enable_disable_value,
                                      ...(setting?.extra_key ? {sub_check_value: setting?.extra_key} : {sub_check_value: 0}),
                                  };
                              }),
                              settings: item.detail.settings.map(field => ({
                                  ...field,
                                  value: settings?.[field.name] ?? 0,
                              })),
                          },
                      }
                    : item
            ),
        }));
    }, [settings, print_setting, pdfType]);

    const handleSubmit = () => {
        if (isSubmitting) return; // Prevent duplicate submission
        setIsSubmitting(true);
        const InvoiceTotalLabel = tableDetailLabel?.detail?.total_labels.reduce((acc, item) => {
            acc[item.name] = item.value;
            return acc;
        }, {});
        const InvoiceTotalPrintSeting = tableDetailLabel?.detail?.total_labels.reduce((acc, item) => {
            acc[item.check_key] = item.check_value;
            return acc;
        }, {});
        const InvoiceLabel = tableDetailLabel?.detail?.invoice_labels.reduce((acc, item) => {
            acc[
                type == 4
                    ? item?.expense
                    : type == 2
                    ? item?.estimate
                    : pdfType == 3
                    ? item?.thermal_name
                    : item.name
            ] = item.value;
            return acc;
        }, {});
        const InvoicePrintSetting = tableDetailLabel?.detail?.invoice_labels.reduce((acc, item) => {
            acc[
                type == 4
                    ? item?.sub_expense_check_key
                    : type == 2
                    ? item?.sub_estimate_check_key
                    : item.sub_check_key
            ] = item.sub_check_value;
            acc[
                type == 4
                    ? item?.expense_check_key
                    : type == 2
                    ? item?.estimate_check_key
                    : item.check_key
            ] = item.check_value;
            return acc;
        }, {});
        const InvoiceSettings = tableDetailLabel?.detail?.settings.reduce((acc, item) => {
            acc[item.name] = item.value;
            return acc;
        }, {});
        const response = {
            invoice_labels: {...InvoiceTotalLabel, ...InvoiceTotalPrintSeting, ...InvoiceLabel, ...InvoicePrintSetting, ...InvoiceSettings},
            // invoice_labels: InvoiceLabel,
            settings: InvoiceSettings,
            print_setting: InvoicePrintSetting,
            ...(pdfType == 3 && { thermal_setting: true }),
        };
        dispatch(updatePrintDetailSetting(type, pdfType, response, setIsSubmitting));
        closeTableSettingModal();
    };

    const formik = useFormik({
        initialValues: initialValues,
        onSubmit: values => handleSubmit(values),
    });

    const handleToggle = e => {
        const { name, checked } = e.target;
        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                          ...item,
                          detail: {
                              ...item.detail,
                              settings: item.detail.settings.map(field => ({
                                  ...field,
                                  value: field.name === name ? (checked ? 1 : 0) : field.value,
                              })),
                          },
                      }
                    : item
            ),
        }));
        dispatch(
            updatePrintToggle(
                UPDATE_SETTING_TYPE.COMPANY_SETTING,
                {
                    key: name,
                    value: checked ? 1 : 0,
                },
                type,
                pdfType
            )
        );
    };
    const onChange = e => {
        const { name, value } = e.target;
        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                          ...item,
                          detail: {
                              ...item.detail,
                              invoice_labels: item.detail.invoice_labels.map(field => ({
                                  ...field,
                                  value: field.name === name ? value : field.value,
                              })),
                          },
                      }
                    : item
            ),
        }));
    };
    const onChangeTotalLabel = e => {
        const { name, value } = e.target;
        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                          ...item,
                          detail: {
                              ...item.detail,
                              total_labels: item.detail.total_labels.map(field => ({
                                  ...field,
                                  value: field.name === name ? value : field.value,
                              })),
                          },
                      }
                    : item
            ),
        }));
    };
    const onChangeCheckBox = e => {
        const { name, value, checked } = e.target;
        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                          ...item,
                          detail: {
                              ...item.detail,
                              invoice_labels: item.detail.invoice_labels.map(field => {
                                  return {
                                      ...field,
                                      check_value:
                                          field.check_key === name
                                              ? checked
                                                  ? 1
                                                  : 0
                                              : field.check_value,
                                      sub_check_value:
                                          field.sub_check_key === name
                                              ? checked
                                                  ? 1
                                                  : 0
                                              : field.sub_check_value,
                                  };
                              }),
                          },
                      }
                    : item
            ),
        }));
    };
    const onChangeTotalLabelCheckBox = e => {
        const { name, value, checked } = e.target;
        setPrintSettingLabel(prev => ({
            ...prev,
            [transaction_type]: prev[transaction_type]?.map(item =>
                item.type == pdfType
                    ? {
                          ...item,
                          detail: {
                              ...item.detail,
                              total_labels: item.detail.total_labels.map(field => {
                                  return {
                                      ...field,
                                      check_value:
                                          field.check_key === name
                                              ? checked
                                                  ? 1
                                                  : 0
                                              : field.check_value,
                                      sub_check_value:
                                          field.sub_check_key === name
                                              ? checked
                                                  ? 1
                                                  : 0
                                              : field.sub_check_value,
                                  };
                              }),
                          },
                      }
                    : item
            ),
        }));
    };

    useEffect(() => {
        if (isTableSettingModal) {
            document.body.classList.add("enable-scroll");
        } else {
            document.body.classList.remove("enable-scroll");
        }
    }, [isTableSettingModal]);

    const handleTableSettingModal = () => {
        if(!userPermission?.edit_print_setting) return
        openTableSettingModal();
        dispatch(fetchPrintDetailSetting(type));
    };

    return (
        <>
            <a
                href="javascript:void(0)"
                onClick={handleTableSettingModal}
                data-modal="table"
                className={`${!userPermission?.edit_print_setting && "disabled"} d-flex gap-5 justify-content-between mb-2`}
            >
                <p className="mb-0 text-primary fw-5 fs-15">Table Settings</p>
                <i className="fas fa-chevron-circle-right text-primary ms-2 fs-3"></i>
            </a>
            <Modal
                show={isTableSettingModal}
                onHide={closeTableSettingModal}
                className={`settings-modal custom-offcanvas-modal fade p-0 `}
            >
                <form onSubmit={formik.handleSubmit}>
                    <div className="offcanvas-header bg-white border-bottom">
                        <h3 className="mb-0">Table Settings</h3>
                        <div className="d-flex gap-5 align-items-center">
                            <button
                                type="submit"
                                onClick={handleSubmit}
                                className="btn btn-primary"
                            >
                                Save
                            </button>
                            <button
                                className="btn close-button p-0"
                                onClick={closeTableSettingModal}
                                type="button"
                            >
                                &times;
                            </button>
                        </div>
                    </div>
                    <div className="offcanvas-body bg-white">
                        {tableDetailLabel?.detail?.settings?.length > 0 ? (
                            <div className="pb-2">
                                <h4 className="fs-16 fw-semibold mb-4">Show / Hide</h4>

                                <div className="w-100 d-flex flex-column gap-3 mb-5">
                                    {tableDetailLabel?.detail?.settings?.map(detail => (
                                        <div className="d-flex justify-content-between align-items-center">
                                            <div
                                                className="d-flex align-items-center gap-10px"
                                                for="flexSwitchCheckChecked4"
                                            >
                                                <p className="fs-15 fw-medium mb-0">
                                                    {detail?.label}
                                                </p>
                                            </div>
                                            <div className="form-check form-switch">
                                                <input
                                                    className="form-check-input m-0"
                                                    type="checkbox"
                                                    role="switch"
                                                    id="flexSwitchCheckChecked4"
                                                    name={
                                                        pdfType == 3
                                                            ? detail?.thermal_name
                                                            : detail?.name
                                                    }
                                                    checked={detail.value == 1}
                                                    value={detail.value}
                                                    onChange={handleToggle}
                                                />
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ) : (
                            ""
                        )}
                        <div className="pb-2">
                            {tableDetailLabel?.detail?.invoice_labels?.length > 0 ? (
                                <h4 className="fs-16 fw-semibold mb-5">Table Headers</h4>
                            ) : (
                                ""
                            )}
                            <Row>
                                {tableDetailLabel?.detail?.invoice_labels?.map(label => (
                                    <>
                                        <Col sm={6} className="mb-6">
                                            <Form.Group className="position-relative form-floating-group">
                                                <FormInput
                                                    className="floating-label-input"
                                                    type="text"
                                                    placeholder=""
                                                    name={label.name}
                                                    value={label.value}
                                                    onChange={onChange}
                                                />
                                                <Form.Label>{label?.label}</Form.Label>
                                                {label?.check_key ? (
                                                    <>
                                                        <Form.Check
                                                            type="checkbox"
                                                            label=""
                                                            checked={label?.check_value == 1}
                                                            name={label.check_key}
                                                            value={label.value}
                                                            onChange={onChangeCheckBox}
                                                            style={
                                                                label?.label === "Qty"
                                                                    ? { right: "68px" }
                                                                    : {}
                                                            }
                                                        />
                                                        {label?.label === "Qty" && (
                                                            <span className="input-divider me-11">
                                                                |
                                                            </span>
                                                        )}
                                                        <div
                                                            className="d-flex gap-2 form-check me-3 ps-0"
                                                            style={{ top: "7px" }}
                                                        >
                                                            {label?.label === "Qty" ? (
                                                                <>
                                                                    {" "}
                                                                    <div className="me-9">UOM</div>
                                                                    <input
                                                                        name={label.sub_check_key}
                                                                        type="checkbox"
                                                                        checked={
                                                                            label?.sub_check_value ==
                                                                            1
                                                                        }
                                                                        className="form-check-input"
                                                                        value={
                                                                            label.sub_check_value
                                                                        }
                                                                        onChange={onChangeCheckBox}
                                                                    />
                                                                </>
                                                            ) : (
                                                                ""
                                                            )}
                                                        </div>
                                                    </>
                                                ) : (
                                                    ""
                                                )}
                                            </Form.Group>
                                        </Col>
                                    </>
                                ))}
                            </Row>
                        </div>
                        <div className="pb-2">
                            {tableDetailLabel?.detail?.total_labels?.length > 0 ? (
                                <h4 className="fs-16 fw-semibold mb-5">Total Labels</h4>
                            ) : (
                                ""
                            )}
                            <Row>
                                {tableDetailLabel?.detail?.total_labels?.map(label => (
                                    <>
                                        <Col sm={6} className="mb-6">
                                            <Form.Group className="position-relative form-floating-group">
                                                <FormInput
                                                    className="floating-label-input"
                                                    type="text"
                                                    placeholder=""
                                                    name={label.name}
                                                    value={label.value}
                                                    onChange={onChangeTotalLabel}
                                                />
                                                <Form.Label>{is_gst_applicable && label?.label == "Sub Total" ? "Taxable Value" : label?.label}</Form.Label>
                                                {label?.check_key ? (
                                                    <Form.Check
                                                        type="checkbox"
                                                        label=""
                                                        checked={label?.check_value == 1}
                                                        name={label.check_key}
                                                        value={label.value}
                                                        onChange={onChangeTotalLabelCheckBox}
                                                    />
                                                ) : (
                                                    ""
                                                )}
                                            </Form.Group>
                                        </Col>
                                    </>
                                ))}
                            </Row>
                        </div>
                    </div>
                    <div className="offcanvas-footer">
                        <div className="d-flex gap-4 fixed-buttons rounded-0">
                            <button
                                onClick={handleSubmit}
                                type="submit"
                                className="btn btn-primary"
                            >
                                Save
                            </button>
                            <button
                                type="button"
                                onClick={closeTableSettingModal}
                                className="btn btn-secondary"
                            >
                                Back
                            </button>
                        </div>
                    </div>
                </form>
            </Modal>
        </>
    );
};

export default TableSettingModal;
