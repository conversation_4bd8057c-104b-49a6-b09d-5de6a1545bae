import React, { useEffect, useRef, useState } from "react";
import amico from "../../assets/images/amico.png";
import mobileApp from "../../assets/images/mobileApp.png";
import qrCode from "../../assets/images/qrCode.png";
import firstInvoice from "../../assets/images/first-invoice.png";
import { Button, Col, Row } from "react-bootstrap";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { DateRange } from "react-date-range";
import ReactSelect from "../../components/ui/ReactSelect";
import { requestBookDemo, salePurchaseData } from "../../store/dashboard/dashboardSlice";
import moment from "moment";
import { useDispatch, useSelector } from "react-redux";
import { getCurrencyFormat } from "../../shared/sharedFunction";
import BookDemo from "./modals/BookDemo";
import BookDemoSuccess from "./modals/BookDemoSuccess";
import WarningModal from "../common/WarningModal";
import { Link } from "react-router-dom";

const predefinedRanges = [
    { label: "Today", value: 1 },
    { label: "This Week", value: 2 },
    { label: "This Month", value: 3 },
    { label: "This Quarter", value: 4 },
    { label: "This Year", value: 5 },
    { label: "Yesterday", value: 6 },
    { label: "Last Week", value: 7 },
    { label: "Last Month", value: 8 },
    { label: "Last Quarter", value: 9 },
    { label: "Last Year", value: 10 },
    { label: "Custom", value: "Custom" },
];

const predefinedRangesPrevYear = [
    { label: "This FY", value: 1 },
    { label: "Custom", value: "Custom" },
];

const DashboardHeader = ({ loading, cardLoading }) => {
    const { dashboard, company } = useSelector(state => state);
    const dashboardData = dashboard?.dashboardData;
    const dashboardCardData = dashboard?.getDashboardCardData;
    const isCurrentFinancialYear =
        dashboardCardData?.saleAndPurchaseData?.is_current_financial_year;
    const currentFinancialYear = company?.company?.currentFinancialYear;

    const dispatch = useDispatch();
    const [selectedRange, setSelectedRange] = useState(1);
    const [selectedRangeLabel, setSelectedRangeLabel] = useState(isCurrentFinancialYear ? "Today" : "This FY");
    const [showCalendar, setShowCalendar] = useState(false);
    const [isShowYearBalance, setIsShowYearBalance] = useState(false)
    const [dateRange, setDateRange] = useState([
        {
            startDate: new Date(),
            endDate: new Date(),
            key: "selection",
        },
    ]);
    const [redirectDateRange, setRedirectDateRange] = useState([
        {
            startDate: new Date(),
            endDate: new Date(),
            key: "selection",
        },
    ]);

    const [salePurchase, setSalePurchase] = useState(dashboardCardData?.saleAndPurchaseData);
    const [receivableAndPayable, setReceivableAndPayable] = useState(
        dashboardCardData?.receivableAndPayableData
    );
    const [cashAndBank, setCashAndBank] = useState(dashboardCardData?.cashAndBankData);
    const [isBookDemo, setIsBookDemo] = useState(false);
    const [isBookSuccessfully, setIsBookSuccessfully] = useState(false);
    const calendarRef = useRef(null);
    const dropdownRef = useRef(null);
    const [salePurchaseLoading, setSalePurchaseLoading] = useState(false);
    const [isPaidAccount, setIsPaidAccount] = useState(true);

    useEffect(() => {
        setDateRange([
            {
                startDate: isCurrentFinancialYear ? new Date() : currentFinancialYear?.yearStartDate
                    ? new Date(currentFinancialYear.yearStartDate)
                    : new Date(),
                endDate: isCurrentFinancialYear ? new Date() : currentFinancialYear?.yearStartDate
                    ? new Date(currentFinancialYear.yearStartDate)
                    : new Date(),
                key: "selection",
            },
        ])
        setSelectedRangeLabel(isCurrentFinancialYear ? "Today" : "This FY")
    }, [currentFinancialYear, isCurrentFinancialYear])

    useEffect(() => {
        if (Object.keys(dashboardData)?.length > 0) {
            setIsPaidAccount(dashboardData?.isPaidAccount);
        }
        if(dashboardData?.is_closing_transferred !== undefined){
            setIsShowYearBalance(dashboardData?.is_closing_transferred ? true : false);
        }
    }, [dashboardData])

    useEffect(() => {
        if (dashboardCardData) {
            setSalePurchase(dashboardCardData?.saleAndPurchaseData);
            setReceivableAndPayable(dashboardCardData?.receivableAndPayableData);
            setCashAndBank(dashboardCardData?.cashAndBankData);
        }
    }, [dashboardCardData]);
    const handlesalePurchase = async (frequency, dateRangeArray = []) => {
        try {
            setSalePurchaseLoading(true);
            const response = await salePurchaseData(frequency, dateRangeArray);

            if (response.success) {
                setTimeout(() => {
                    setSalePurchaseLoading(false);
                    setShowCalendar(false);
                }, 500);
                setSalePurchase(response.data.saleAndPurchaseData);
            }
        } catch (error) {
            console.log(error);
            setSalePurchaseLoading(false);
            setShowCalendar(false);
        } finally {
            setSalePurchaseLoading(false);
            setShowCalendar(false);
        }
    };

    const handleDropdownChange = range => {
        setSelectedRange(range.value);
        setSelectedRangeLabel(range.label);

        if (range.value === "Custom") {
            setShowCalendar(true);
        } else {
            if (range.value === selectedRange) return;
            handlesalePurchase(range.value);
            setShowCalendar(false);
            setDateRange([
                {
                    startDate: isCurrentFinancialYear ? new Date() : currentFinancialYear?.yearStartDate
                        ? new Date(currentFinancialYear.yearStartDate)
                        : new Date(),
                    endDate: isCurrentFinancialYear ? new Date() : currentFinancialYear?.yearStartDate
                        ? new Date(currentFinancialYear.yearStartDate)
                        : new Date(),
                    key: "selection",
                },
            ])
        }
    };

    const handleSelect = ranges => {
        setDateRange([ranges.selection]);

        if (
            ranges.selection.startDate &&
            ranges.selection.endDate &&
            ranges.selection.startDate.getTime() !== ranges.selection.endDate.getTime()
        ) {
            const startDate = moment(ranges.selection.startDate).format("DD-MM-YYYY");
            const endDate = moment(ranges.selection.endDate).format("DD-MM-YYYY");

            const frequency = 11;

            const params = {
                startDate,
                endDate,
            };

            setRedirectDateRange([ranges.selection]);

            handlesalePurchase(frequency, params);
        }
    };

    useEffect(() => {
        function handleClickOutside(event) {
            if (
                calendarRef.current &&
                !calendarRef.current.contains(event.target) &&
                dropdownRef.current &&
                !dropdownRef.current.contains(event.target)
            ) {
                setShowCalendar(false);
            }
        }

        if (showCalendar) {
            document.addEventListener("mousedown", handleClickOutside);
        }
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [showCalendar]);

    const handleConfirmDemo = async () => {
        const response = await dispatch(requestBookDemo(company?.company?.user_id));
        if (response?.success) {
            setIsBookSuccessfully(true);
            setIsBookDemo(false);
        } else {
            setIsBookDemo(false);
        }
    };

    const handleBookSuccessfullyClose = () => setIsBookSuccessfully(false);
    const loadSkeltonLoader = () => {
        return <div className="skeleton"></div>;
    };

    const getDateRange = range => {
        const today = moment();
        const currentYear = today.year();
        const currentMonth = today.month() + 1;
        let startDate, endDate;

        let startYear = currentMonth < 4 ? currentYear - 1 : currentYear;
        let endYear = startYear + 1;

        const getMonday = (date) => date.clone().isoWeekday(1);
        const getSunday = (date) => date.clone().isoWeekday(7);

        switch (range) {
            case 1:
                startDate = endDate = today.format("YYYY-MM-DD");
                break;
            case 2:
                startDate = getMonday(today).format("YYYY-MM-DD");
                endDate = getSunday(today).format("YYYY-MM-DD");
                break;
            case 3:
                startDate = today.startOf("month").format("YYYY-MM-DD");
                endDate = today.endOf("month").format("YYYY-MM-DD");
                break;
            case 4:
                startDate = today.startOf("quarter").format("YYYY-MM-DD");
                endDate = today.endOf("quarter").format("YYYY-MM-DD");
                break;
            case 5:
                startDate = moment(`01-04-${startYear}`, "DD-MM-YYYY").format("YYYY-MM-DD");
                endDate = moment(`31-03-${endYear}`, "DD-MM-YYYY").format("YYYY-MM-DD");
                break;
            case 6:
                startDate = endDate = today.subtract(1, "days").format("YYYY-MM-DD");
                break;
            case 7:
                const lastWeek = today.clone().subtract(1, "weeks");
                startDate = getMonday(lastWeek).format("YYYY-MM-DD");
                endDate = getSunday(lastWeek).format("YYYY-MM-DD");
                break;
            case 8:
                startDate = today.subtract(1, "months").startOf("month").format("YYYY-MM-DD");
                endDate = today.endOf("month").format("YYYY-MM-DD");
                break;
            case 9:
                startDate = today.subtract(1, "quarters").startOf("quarter").format("YYYY-MM-DD");
                endDate = today.endOf("quarter").format("YYYY-MM-DD");
                break;
            case 10:
                startDate = moment(`01-04-${startYear - 1}`, "DD-MM-YYYY").format("YYYY-MM-DD");
                endDate = moment(`31-03-${startYear}`, "DD-MM-YYYY").format("YYYY-MM-DD");
                break;
            default:
                return null;
        }

        return { startDate, endDate };
    };

    const [saleURL, setSaleURL] = useState({
        startDate: new Date(),
        endDate: new Date(),
    })

    const handleRedirectSaleAndPurchases = () => {
        let startDate, endDate;

        if (selectedRange === "Custom") {
            startDate = moment(redirectDateRange[0].startDate).format("YYYY-MM-DD");
            endDate = moment(redirectDateRange[0].endDate).format("YYYY-MM-DD");
        } else if(selectedRangeLabel === "This FY") {
            startDate = currentFinancialYear?.yearStartDate;
            endDate = currentFinancialYear?.yearEndDate;
        }else{
            const dateRange = getDateRange(selectedRange);
            if (!dateRange) return;
            startDate = dateRange.startDate;
            endDate = dateRange.endDate;
        }

        setSaleURL({
            startDate: startDate,
            endDate: endDate,
        })

    };

    const handleConfirmTransfer = () =>{
        window.location.href = `${window.location.origin}/company/transfer-closing-balance?transfer=1`;
    }

    useEffect(() => {
        handleRedirectSaleAndPurchases()
    }, [redirectDateRange, selectedRange, company, selectedRangeLabel]);

    const financialYearStart = moment(currentFinancialYear?.yearStartDate, "YYYY-MM-DD");
    const financialYearEnd = moment(currentFinancialYear?.yearEndDate, "YYYY-MM-DD");

    const isRangeWithinFinancialYear = (start, end) => {
        return start.isSameOrAfter(financialYearStart) && end.isSameOrBefore(financialYearEnd);
    };

    const filteredRanges = predefinedRanges.filter(range => {
        if (range.label === "Last Week") {
            const startOfLastWeek = moment().subtract(1, 'weeks').startOf('isoWeek');
            const endOfLastWeek = moment().subtract(1, 'weeks').endOf('isoWeek');

            if (!isRangeWithinFinancialYear(startOfLastWeek, endOfLastWeek)) return false;
        }

        if (range.label === "Last Month") {
            const startOfLastMonth = moment().subtract(1, 'months').startOf('month');
            const endOfLastMonth = moment().subtract(1, 'months').endOf('month');

            if (!isRangeWithinFinancialYear(startOfLastMonth, endOfLastMonth)) return false;
        }

        if (range.label === "Last Quarter") {
            const startOfLastQuarter = moment().subtract(1, 'quarters').startOf('quarter');
            const endOfLastQuarter = moment().subtract(1, 'quarters').endOf('quarter');

            if (!isRangeWithinFinancialYear(startOfLastQuarter, endOfLastQuarter)) return false;
        }

        return true;
    });

    return (
        <>
            {dashboardData?.trialOrPlanMessageData?.isTrialActiveMessage && (
                <div className="content-box p-3 get_started " style={{ marginBottom: "15px" }}>
                    <div className="d-flex align-items-center gap-3">
                        <div>
                            <button type="button" className="btn get-started-btn gradient-primary">
                                get started
                            </button>
                        </div>
                        <p className="fs-14 mb-0 fw-7">
                            {dashboardData?.trialOrPlanMessageData?.message}
                        </p>
                    </div>
                </div>
            )}
            {dashboardData?.isRecurringInvoicePending && (
                <div className="content-box p-3 get_started " style={{ marginBottom: "15px" }}>
                    <div className="d-flex align-items-center gap-3">
                        <div>
                            <Link to={(`/company/recurring-invoices?tab=pendingApproval`)} type="button" className="btn get-started-recurring-btn gradient-recurring-btn">
                                Recurring Invoice
                            </Link>
                        </div>
                        <p className="fs-12 mb-0 fw-7">
                            Approve Your Invoice To Keep Things Moving!
                        </p>
                    </div>
                </div>
            )}

            {!isPaidAccount && <Row className="h-100 book-demo-content">
                <Col
                    xl={6}
                    sm={6}
                    className="h-168px d-flex align-items-center justify-content-center"
                    style={{ paddingLeft: "10px" }}
                >
                    <div className="p-2 px-5 border-0 h-100 d-flex align-items-center justify-content-center mobile_app_card flex-wrap flex-xl-nowrap gap-2">
                        <img src={qrCode} loading="lazy" alt="banner" className="mobile_app_card_img" />
                        <div className="d-flex flex-column align-items-start justify-content-start gap-4">
                            <span className=" mobile_app_card_demo ">
                                Accounting Made Easy: Access Anywhere with Our Mobile App!
                            </span>
                        </div>
                        <img src={mobileApp} loading="lazy" alt="banner" className="mobile_app_card_mobileApp" />
                    </div>
                </Col>
                <Col xl={6} sm={6} style={{ paddingRight: "10px" }}>
                    <div className="content-box overflow-hidden h-100 d-flex align-items-center justify-content-center">
                        <div className="d-flex align-items-center justify-content-center" style={{ cursor: "pointer" }}>
                            <img
                                loading="lazy"
                                src="/images/BookDemo.png"
                                alt="book-demo"
                                className="h-100"
                                onClick={() => setIsBookDemo(true)}
                            />
                        </div>
                    </div>
                </Col>
            </Row>}

            {dashboardData?.trialOrPlanMessageData?.isTrialActiveMessage && (
                <Col
                    xl={12}
                    md={12}
                    className="w-100 d-flex align-items-center justify-content-center first-invoice-contener "
                    style={{ position: "relative", height: "180px" }}
                >
                    <div
                        className=" w-100 mt-4 rounded-3 text-center rounded first-invoice-img overflow-hidden"
                        style={{
                            backgroundImage: `url(${firstInvoice})`,
                            backgroundSize: "contain",
                            backgroundSize: "cover",
                            backgroundPosition: "center",
                            backgroundRepeat: "no-repeat",
                            height: "168px",
                            width: "101.5%",
                            position: "absolute",
                            right: "-9px",
                        }}
                    >
                        {/* Content */}
                        <div className="position-relative first-invoice ">
                            <h5 className="fw-bold">YOU’RE ALMOST THERE!</h5>
                            <p className="mb-3">
                                Create your first invoice and unlock the full power of hisabkitab.
                            </p>
                            <Button
                                type="button"
                                className="btn get-first-invoice-btn gradient-primary"
                            >
                                Create First Invoice
                            </Button>
                        </div>
                    </div>
                </Col>
            )}

            <Row className="mt-2 business-glance" id="step1">
                <Col xl={4} md={6} className="mb-5 business-mb-0">
                    <div className="content-box p-5 position-relative">
                        <div className="d-flex align-items-center justify-content-between gap-3 mb-3">
                            <h6 className="text-uppercase fs-12 fw-7 mb-0">Sales and Purchase</h6>

                            {loading || salePurchaseLoading || cardLoading ? (
                                <div className="skeleton-filter"></div>
                            ) : (
                                <div className="position-relative" ref={dropdownRef}>
                                    <div className="input-group flex-nowrap">
                                        <div className="position-relative h-40px w-120px focus-shadow">
                                            <ReactSelect
                                                options={
                                                    isCurrentFinancialYear
                                                        ? filteredRanges
                                                        : predefinedRangesPrevYear
                                                }
                                                placeholder=""
                                                defaultLabel=""
                                                value={selectedRange}
                                                onChange={handleDropdownChange}
                                                className="h-40px border-0 focus-shadow cursor-pointer"
                                            />
                                        </div>
                                    </div>

                                    {showCalendar && selectedRange === "Custom" && (
                                        <div
                                            ref={calendarRef}
                                            className="position-absolute z-10 bg-white shadow rounded p-2"
                                            style={{ top: "100%", left: 0, zIndex: 10 }}
                                        >
                                            <DateRange
                                                editableDateInputs={true}
                                                onChange={handleSelect}
                                                moveRangeOnFirstSelection={false}
                                                ranges={dateRange}
                                                months={2}
                                                direction="horizontal"
                                                color="#4f158c"
                                                rangeColors={["#4f158c"]}
                                                minDate={new Date(currentFinancialYear?.yearStartDate)}
                                                maxDate={new Date(currentFinancialYear?.yearEndDate)}
                                                showDateDisplay={false}
                                            />
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                        {loading || salePurchaseLoading || cardLoading ? (
                            loadSkeltonLoader()
                        ) : (
                            <div className="details mb-3">
                                <a
                                    href={`${window.location.origin}/company/trading-profit-loss?start_date=${saleURL?.startDate}&end_date=${saleURL?.endDate}`}
                                    className="d-flex gap-3"
                                    style={{ cursor: "pointer" }}
                                >
                                    <div className="icon">
                                        <svg
                                            width="20"
                                            height="20"
                                            viewBox="0 0 20 20"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                d="M1.82292 1.89014H2.8912C3.09242 1.89014 3.19302 1.89014 3.27399 1.92714C3.34533 1.95974 3.4058 2.01218 3.44817 2.0782C3.49625 2.15312 3.51047 2.25272 3.53893 2.45191L3.92604 5.16165M3.92604 5.16165L4.78635 11.485C4.89553 12.2874 4.95012 12.6886 5.14195 12.9907C5.31099 13.2568 5.55333 13.4684 5.83981 13.6C6.16492 13.7494 6.56984 13.7494 7.37967 13.7494H14.379C15.1499 13.7494 15.5353 13.7494 15.8503 13.6107C16.128 13.4884 16.3663 13.2912 16.5384 13.0413C16.7336 12.7578 16.8057 12.3791 16.95 11.6219L18.0325 5.93838C18.0833 5.67185 18.1087 5.53858 18.0719 5.43441C18.0396 5.34303 17.9759 5.26608 17.8922 5.21728C17.7968 5.16165 17.6611 5.16165 17.3898 5.16165H3.92604ZM8.36594 17.4298C8.36594 17.8815 7.99977 18.2477 7.54806 18.2477C7.09636 18.2477 6.73019 17.8815 6.73019 17.4298C6.73019 16.9781 7.09636 16.6119 7.54806 16.6119C7.99977 16.6119 8.36594 16.9781 8.36594 17.4298ZM14.909 17.4298C14.909 17.8815 14.5428 18.2477 14.0911 18.2477C13.6394 18.2477 13.2732 17.8815 13.2732 17.4298C13.2732 16.9781 13.6394 16.6119 14.0911 16.6119C14.5428 16.6119 14.909 16.9781 14.909 17.4298Z"
                                                stroke="black"
                                                strokeWidth="1.90204"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                            />
                                        </svg>
                                    </div>
                                    <div>
                                        <p className="fs-12 fw-bolder mb-0">Sales</p>
                                        <p className="fs-14 text-success mb-0 fw-bolder">
                                            {getCurrencyFormat(salePurchase?.sale)}
                                        </p>
                                    </div>
                                </a>
                            </div>
                        )}
                        {loading || salePurchaseLoading || cardLoading ? (
                            loadSkeltonLoader()
                        ) : (
                            <div className="details">
                                <a
                                    href={`${window.location.origin}/company/trading-profit-loss?start_date=${saleURL?.startDate}&end_date=${saleURL?.endDate}`}
                                    className="d-flex gap-3"
                                    style={{ cursor: "pointer" }}
                                >
                                    <div className="icon">
                                        <svg
                                            width="18"
                                            height="17"
                                            viewBox="0 0 18 17"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                d="M12.2729 5.07498C12.2729 5.94264 11.9282 6.77476 11.3147 7.38829C10.7011 8.00182 9.869 8.34649 9.00134 8.34649C8.13369 8.34649 7.30156 8.00182 6.68804 7.38829C6.07451 6.77476 5.72983 5.94264 5.72983 5.07498M2.15834 4.58539L1.58583 11.4556C1.46284 12.9314 1.40134 13.6694 1.65082 14.2386C1.87001 14.7388 2.24979 15.1515 2.73001 15.4115C3.27659 15.7074 4.01708 15.7074 5.49808 15.7074H12.5046C13.9856 15.7074 14.7261 15.7074 15.2727 15.4115C15.7529 15.1515 16.1327 14.7388 16.3519 14.2386C16.6013 13.6694 16.5399 12.9314 16.4169 11.4556L15.8443 4.58539C15.7385 3.31552 15.6856 2.68059 15.4044 2.20003C15.1568 1.77684 14.788 1.43756 14.3458 1.22593C13.8435 0.985597 13.2064 0.985597 11.9321 0.985596L6.07059 0.985596C4.79633 0.985596 4.1592 0.985595 3.65694 1.22592C3.21465 1.43756 2.84591 1.77684 2.59828 2.20003C2.31707 2.68059 2.26416 3.31552 2.15834 4.58539Z"
                                                stroke="black"
                                                strokeWidth="1.90204"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                            />
                                        </svg>
                                    </div>
                                    <div>
                                        <p className="fs-12 fw-bolder mb-0">Purchases</p>
                                        <p className="fs-14 text-danger mb-0 fw-bolder">
                                            {getCurrencyFormat(salePurchase?.purchase)}
                                        </p>
                                    </div>
                                </a>
                            </div>
                        )}
                    </div>
                </Col>
                <Col xl={4} md={6} className="mb-5 business-mb-0">
                    <div className="content-box p-5">
                        <div className="d-flex align-items-center h-34px gap-3 mb-3">
                            <h6 className="text-uppercase fs-12 fw-7 mb-0">
                                RECEIVABLES AND PAYABLES
                            </h6>
                        </div>
                        {loading || cardLoading ? (
                            loadSkeltonLoader()
                        ) : (
                            <div className="details mb-3">
                                <a
                                    href={receivableAndPayable?.receivableReportUrl}
                                    className="d-flex gap-3"
                                >
                                    <div className="icon">
                                        <svg
                                            width="19"
                                            height="19"
                                            viewBox="0 0 19 19"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                d="M11.2369 6.08567C10.7687 6.41943 10.1958 6.61577 9.577 6.61577C7.99604 6.61577 6.71442 5.33415 6.71442 3.7532C6.71442 2.17224 7.99604 0.890625 9.577 0.890625C10.6018 0.890625 11.5008 1.42913 12.0065 2.2386M5.07867 15.6837H7.21357C7.49192 15.6837 7.76857 15.7168 8.03841 15.7831L10.2942 16.3312C10.7836 16.4505 11.2935 16.4621 11.7881 16.366L14.2822 15.8808C14.9411 15.7524 15.5472 15.4369 16.0221 14.9749L17.7868 13.2583C18.2907 12.769 18.2907 11.9749 17.7868 11.4846C17.3331 11.0433 16.6146 10.9936 16.1013 11.3679L14.0447 12.8683C13.7502 13.0836 13.3918 13.1995 13.0232 13.1995H11.0373L12.3014 13.1995C13.0139 13.1995 13.591 12.6381 13.591 11.945V11.6941C13.591 11.1186 13.1884 10.6168 12.6147 10.4777L10.6636 10.0032C10.3461 9.9262 10.0209 9.88728 9.69407 9.88728C8.90497 9.88728 7.4766 10.5406 7.4766 10.5406L5.07867 11.5434M16.529 4.57107C16.529 6.15203 15.2473 7.43364 13.6664 7.43364C12.0854 7.43364 10.8038 6.15203 10.8038 4.57107C10.8038 2.99012 12.0854 1.7085 13.6664 1.7085C15.2473 1.7085 16.529 2.99012 16.529 4.57107ZM1.80716 11.1959L1.80716 15.9396C1.80716 16.3976 1.80716 16.6267 1.8963 16.8016C1.97472 16.9555 2.09983 17.0806 2.25373 17.159C2.42868 17.2482 2.65771 17.2482 3.11576 17.2482H3.77006C4.22812 17.2482 4.45715 17.2482 4.6321 17.159C4.78599 17.0806 4.91111 16.9555 4.98953 16.8016C5.07867 16.6267 5.07867 16.3976 5.07867 15.9396V11.1959C5.07867 10.7378 5.07867 10.5088 4.98953 10.3338C4.91111 10.18 4.78599 10.0548 4.6321 9.97642C4.45715 9.88728 4.22812 9.88728 3.77007 9.88728L3.11576 9.88728C2.65771 9.88728 2.42868 9.88728 2.25373 9.97642C2.09983 10.0548 1.97472 10.18 1.8963 10.3338C1.80716 10.5088 1.80716 10.7378 1.80716 11.1959Z"
                                                stroke="black"
                                                strokeWidth="1.63575"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                            />
                                        </svg>
                                    </div>
                                    <div>
                                        <p className="fs-12 fw-bolder mb-0">Receivables</p>
                                        <p className="fs-14 text-success mb-0 fw-bolder">
                                            {getCurrencyFormat(receivableAndPayable?.receivable)}
                                        </p>
                                    </div>
                                </a>
                            </div>
                        )}
                        {loading || cardLoading ? (
                            loadSkeltonLoader()
                        ) : (
                            <div className="details">
                                <a
                                    href={receivableAndPayable?.payableReportUrl}
                                    className="d-flex gap-3"
                                >
                                    <div className="icon">
                                        <svg
                                            width="20"
                                            height="21"
                                            viewBox="0 0 20 21"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <g clipPath="url(#clip0_2184_25266)">
                                                <path
                                                    d="M5.07867 9.5291V12.8006M14.8932 7.89334V11.1649M14.0753 3.80396C16.0781 3.80396 17.1613 4.11046 17.7003 4.34821C17.772 4.37987 17.8079 4.3957 17.9115 4.49455C17.9736 4.5538 18.0869 4.72766 18.1161 4.80838C18.1647 4.94304 18.1647 5.01665 18.1647 5.16387V13.9547C18.1647 14.698 18.1647 15.0696 18.0533 15.2607C17.9399 15.455 17.8305 15.5453 17.6183 15.62C17.4097 15.6934 16.9886 15.6125 16.1463 15.4507C15.5568 15.3374 14.8576 15.2542 14.0753 15.2542C11.6217 15.2542 9.16806 16.89 5.89655 16.89C3.8938 16.89 2.81059 16.5835 2.27162 16.3457C2.19984 16.3141 2.16395 16.2983 2.06037 16.1994C1.99829 16.1401 1.88495 15.9663 1.8558 15.8856C1.80716 15.7509 1.80716 15.6773 1.80716 15.5301L1.80716 6.7392C1.80716 5.99594 1.80716 5.62431 1.91862 5.43328C2.032 5.23897 2.14134 5.14864 2.35355 5.07396C2.56217 5.00055 2.98329 5.08147 3.82554 5.24329C4.41506 5.35656 5.11423 5.43971 5.89655 5.43971C8.35018 5.43971 10.8038 3.80396 14.0753 3.80396ZM12.0306 10.347C12.0306 11.4762 11.1152 12.3917 9.98593 12.3917C8.85668 12.3917 7.94124 11.4762 7.94124 10.347C7.94124 9.21772 8.85668 8.30228 9.98593 8.30228C11.1152 8.30228 12.0306 9.21772 12.0306 10.347Z"
                                                    stroke="black"
                                                    strokeWidth="1.63575"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                />
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_2184_25266">
                                                    <rect
                                                        width="19.6291"
                                                        height="19.6291"
                                                        fill="white"
                                                        transform="translate(0.171417 0.532471)"
                                                    />
                                                </clipPath>
                                            </defs>
                                        </svg>
                                    </div>
                                    <div>
                                        <p className="fs-12 fw-bolder mb-0">Payables</p>
                                        <p className="fs-14 text-danger mb-0 fw-bolder">
                                            {getCurrencyFormat(receivableAndPayable?.payable)}
                                        </p>
                                    </div>
                                </a>
                            </div>
                        )}
                    </div>
                </Col>
                <Col xl={4} md={6} className="mb-5 business-mb-0">
                    <div className="content-box p-5">
                        <div className="d-flex align-items-center gap-3 h-34px mb-3">
                            <h6 className="text-uppercase fs-12 fw-7 mb-0">cash and bank</h6>
                        </div>
                        {loading || cardLoading ? (
                            loadSkeltonLoader()
                        ) : (
                            <div className="details mb-3">
                                <div className="d-flex gap-3">
                                    <div className="icon">
                                        <svg
                                            width="20"
                                            height="20"
                                            viewBox="0 0 20 20"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <g clipPath="url(#clip0_2191_26039)">
                                                <path
                                                    d="M4.96401 8.43363V11.7051M14.7785 8.43363V11.7051M1.69251 6.96145L1.6925 13.1773C1.6925 14.0934 1.6925 14.5515 1.87079 14.9014C2.02762 15.2092 2.27786 15.4594 2.58564 15.6162C2.93555 15.7945 3.3936 15.7945 4.30971 15.7945L15.4328 15.7945C16.349 15.7945 16.807 15.7945 17.1569 15.6162C17.4647 15.4594 17.7149 15.2092 17.8718 14.9014C18.0501 14.5515 18.0501 14.0934 18.0501 13.1773V6.96145C18.0501 6.04534 18.0501 5.58728 17.8718 5.23738C17.7149 4.92959 17.4647 4.67935 17.1569 4.52253C16.807 4.34424 16.349 4.34424 15.4328 4.34424L4.30971 4.34424C3.3936 4.34424 2.93555 4.34424 2.58564 4.52252C2.27786 4.67935 2.02762 4.92959 1.87079 5.23738C1.69251 5.58728 1.69251 6.04534 1.69251 6.96145ZM11.916 10.0694C11.916 11.1986 11.0005 12.1141 9.87128 12.1141C8.74203 12.1141 7.82659 11.1986 7.82659 10.0694C7.82659 8.94013 8.74203 8.02469 9.87128 8.02469C11.0005 8.02469 11.916 8.94013 11.916 10.0694Z"
                                                    stroke="black"
                                                    strokeWidth="1.63575"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                />
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_2191_26039">
                                                    <rect
                                                        width="19.6291"
                                                        height="19.6291"
                                                        fill="white"
                                                        transform="translate(0.0567627 0.254883)"
                                                    />
                                                </clipPath>
                                            </defs>
                                        </svg>
                                    </div>
                                    <div>
                                        <p className="fs-12 fw-bolder mb-0">Cash</p>
                                        <p className="fs-14 text-primary mb-0 fw-bolder">
                                            {getCurrencyFormat(cashAndBank?.cash)}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        )}
                        {loading || cardLoading ? (
                            loadSkeltonLoader()
                        ) : (
                            <div className="details">
                                <div className="d-flex gap-3">
                                    <div className="icon">
                                        <svg
                                            width="20"
                                            height="21"
                                            viewBox="0 0 20 21"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                d="M4.14613 7.89342V14.4364M7.82658 7.89342V14.4364M11.916 7.89342V14.4364M15.5964 7.89342V14.4364M2.51038 15.745L2.51038 16.3993C2.51038 16.8574 2.51038 17.0864 2.59952 17.2614C2.67793 17.4153 2.80305 17.5404 2.95694 17.6188C3.1319 17.7079 3.36093 17.7079 3.81898 17.7079H15.9236C16.3816 17.7079 16.6106 17.7079 16.7856 17.6188C16.9395 17.5404 17.0646 17.4153 17.143 17.2614C17.2322 17.0864 17.2322 16.8574 17.2322 16.3993V15.745C17.2322 15.287 17.2322 15.058 17.143 14.883C17.0646 14.7291 16.9395 14.604 16.7856 14.5256C16.6106 14.4364 16.3816 14.4364 15.9236 14.4364H3.81898C3.36093 14.4364 3.1319 14.4364 2.95694 14.5256C2.80305 14.604 2.67793 14.7291 2.59952 14.883C2.51038 15.058 2.51038 15.287 2.51038 15.745ZM9.5874 3.04924L3.5351 4.39419C3.16946 4.47544 2.98664 4.51607 2.85018 4.61439C2.7298 4.7011 2.63529 4.81893 2.57675 4.95525C2.51038 5.10979 2.51038 5.29707 2.51038 5.67163L2.51038 6.58481C2.51038 7.04287 2.51038 7.27189 2.59952 7.44685C2.67793 7.60074 2.80305 7.72586 2.95694 7.80427C3.1319 7.89342 3.36093 7.89342 3.81898 7.89342H15.9236C16.3816 7.89342 16.6106 7.89342 16.7856 7.80427C16.9395 7.72586 17.0646 7.60074 17.143 7.44685C17.2322 7.27189 17.2322 7.04287 17.2322 6.58481V5.67163C17.2322 5.29707 17.2322 5.10979 17.1658 4.95525C17.1073 4.81893 17.0127 4.7011 16.8924 4.61439C16.7559 4.51607 16.5731 4.47544 16.2074 4.39419L10.1551 3.04924C10.0492 3.02569 9.99624 3.01392 9.94272 3.00923C9.89518 3.00506 9.84737 3.00506 9.79983 3.00923C9.7463 3.01392 9.69333 3.02569 9.5874 3.04924Z"
                                                stroke="black"
                                                strokeWidth="1.63575"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                            />
                                        </svg>
                                    </div>
                                    <div>
                                        <p className="fs-12 fw-bolder mb-0">Bank</p>
                                        <p className="fs-14 text-primary mb-0 fw-bolder">
                                            {getCurrencyFormat(cashAndBank?.bank)}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </Col>
            </Row>

            <BookDemo
                isBookDemo={isBookDemo}
                handleClose={() => setIsBookDemo(false)}
                handleConfirmDemo={handleConfirmDemo}
            />
            <BookDemoSuccess
                isBookSuccessfully={isBookSuccessfully}
                handleBookSuccessfullyClose={handleBookSuccessfullyClose}
            />
            {isShowYearBalance && <WarningModal
                        show={isShowYearBalance}
                        modalTitle="Transfer Previous Year’s Balance!"
                        message='Your 2024-2025 closing balance is not yet transferred to 2025-2026. Do you want to transfer it now?'
                        showCancelButton
                        showConfirmButton
                        confirmText="Confirm"
                        cancelText="Cancel"
                        confirmClass="success-btn"
                        handleClose={() => setIsShowYearBalance(false)}
                        handleSubmit={handleConfirmTransfer}
                    />}
        </>
    );
};

export default DashboardHeader;
