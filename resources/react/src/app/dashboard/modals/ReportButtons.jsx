import React, { useState } from 'react'
import { Col, Modal, Row } from 'react-bootstrap'
import ReactSelect from '../../../components/ui/ReactSelect';

const ReportButtons = ({ isReportButtonModal, handleclose, reportsButton, handleDeleteButton, reportsButtonList, selectedReport, handleSelectChange, isReportButtonList }) => {
    const [hoveredIndex, setHoveredIndex] = useState(null);
    const [hoveredSubButton, setHoveredSubButton] = useState();
    const [hoverTimeout, setHoverTimeout] = useState(null);
    const [isSelectReportButton, setIsSelectReportButton] = useState(false);

    const handleMouseEnter = (index, subButtons) => {

        setHoveredIndex(true);
        if (hoverTimeout) {

            clearTimeout(hoverTimeout);
            setHoverTimeout(null);
        }
        setHoveredIndex(index);
        setHoveredSubButton(subButtons);
    };

    const handleMouseLeave = () => {

        const timeout = setTimeout(() => {
            setHoveredIndex(null);
            setHoveredSubButton([]);
        }, 300);
        setHoverTimeout(timeout);
    };

    return (
        <Modal
            show={isReportButtonModal}
            onHide={handleclose}
            className={`sale-configuration-modal custom-offcanvas-modal fade p-0  report-buttons-modal`}
        >
            <div className="offcanvas-header bg-white">
                <h3 className="mb-0">Report Buttons</h3>
                <button
                    className="btn close-button p-0"
                    onClick={handleclose}
                    type="button"
                >
                    &times;
                </button>
            </div>
            <div className=" offcanvas-reportButton-body" >
                <Row>
                    <Col sm={12} className="d-xl-flex ">
                        <div className="content-box position-relative w-100 " onMouseLeave={handleMouseLeave}>
                            <>
                                {hoveredIndex && (
                                    <div className="subreport-dropdown"
                                        onMouseEnter={() => {
                                            if (hoverTimeout) {
                                                clearTimeout(hoverTimeout);
                                                setHoverTimeout(null);
                                            }
                                        }}
                                        onMouseLeave={handleMouseLeave}
                                    >
                                        {hoveredSubButton && hoveredSubButton.length > 0 && hoveredSubButton.map((sub, subIndex) => (
                                            <div key={`sub-${subIndex}`}>

                                                <a
                                                    key={`sub-${subIndex}`}
                                                    className="dropdown-item"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="bottom"
                                                    title={sub.tooltip || ""}
                                                    href={sub.url}
                                                    target="_blank"
                                                >
                                                    {sub.name}
                                                </a>

                                                {
                                                    sub.is_remove && (
                                                        <div
                                                            className="position-absolute remove_button bg-white zindex-1 rounded-circle"
                                                            style={{ top: "10px", right: "-10px", cursor: "pointer" }}
                                                            onClick={() => handleDeleteButton(sub.button_id)}
                                                        >
                                                            <svg width="20" height="20" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <circle cx="11.5" cy="11.5" r="11" fill="white" stroke="#F76600"></circle>
                                                                <rect x="6" y="10" width="11" height="3" rx="1" fill="#F76600"></rect>
                                                            </svg>
                                                        </div>
                                                    )
                                                }
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </>
                            <div className="overflow-y-auto p-4  " >
                                {reportsButton?.length > 0 &&
                                    reportsButton.map((report, index) => {
                                        const { button_id, is_remove, name, tooltip, url, sub_buttons } = report;
                                        const tooltipName = tooltip;
                                        return (
                                            <div
                                                key={index}
                                                className="position-relative delete_button "
                                                onMouseEnter={() => sub_buttons?.length > 0 ? handleMouseEnter(index, sub_buttons) : setHoveredIndex(false)}
                                            // onMouseLeave={handleMouseLeave}
                                            >
                                                <a
                                                    className="btn custom-dashboard-btn mt-3 me-3 px-1 py-2 "
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="bottom"
                                                    title=""
                                                    href={url?.length > 0 ? url : null}
                                                    data-bs-original-title={tooltipName}
                                                    target="_blank"
                                                >
                                                    {name}
                                                </a>
                                                {is_remove && <div class="position-absolute remove_button bg-white zindex-1 rounded-circle" style={{ top: "10px", right: "-10px", display: "none", cursor: "pointer", }} data-button-id="6"
                                                    onClick={() => handleDeleteButton(button_id)}
                                                >
                                                    <svg width="20" height="20" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <circle cx="11.5" cy="11.5" r="11" fill="white" stroke="#F76600"></circle>
                                                        <rect x="6" y="10" width="11" height="3" rx="1" fill="#F76600"></rect>
                                                    </svg>
                                                </div>}

                                            </div>

                                        );
                                    })
                                }

                                {isReportButtonList && (
                                    !isSelectReportButton ? (
                                        <div class="btn custom-add-report-btn me-3 mt-2 company-dashboard-add-btn position-relative"
                                            onClick={() => setIsSelectReportButton(true)}
                                            onMouseEnter={handleMouseLeave}
                                        >
                                            <i class="fas fa-plus text-gray"></i>Add Report
                                        </div>
                                    ) : (
                                        <div className="focus-shadow"  style={{ cursor: "pointer" }}
                                            onMouseEnter={handleMouseLeave}
                                        >
                                            <ReactSelect
                                                options={reportsButtonList}
                                                placeholder="Select Report"
                                                islabel="no"
                                                defaultLabel={"Select Report"}
                                                value={selectedReport}
                                                onChange={handleSelectChange}
                                                className="h-40px border-0 focus-shadow cursor-pointer"
                                            />
                                        </div>
                                    ))
                                }
                            </div>
                        </div>
                    </Col>
                </Row>
            </div>
        </Modal>
    )
}

export default ReportButtons
