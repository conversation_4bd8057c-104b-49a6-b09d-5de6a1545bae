import moment from "moment";
import { useContext, useEffect, useRef, useState } from "react";
import { Container } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import Toast from "../../components/ui/Toast";
import { apiBaseURL, CALCULATION_ON_TYPE, SHIPPING_ADDRESS_TYPE_LIST, TABLE_HEADER_TYPE, toastType, TRANSACTION_TYPE } from "../../constants";
import { StateContext } from "../../context/StateContext";
import {
    calculateAdditionalCharges,
    calculateAdditionalClassification,
    calculateAddLessCharges,
    calculateClassification,
    calculateTotals,
    customToFixed,
    gstCalculate,
    RoundOffMethod,
    RoundOffMethodForTds,
} from "../../shared/calculation";
import useDropdownOption from "../../shared/dropdownList";
import CustomHelmet from "../../shared/helmet";
import Loader from "../../shared/loader";
import {
    areAllProductsExemptOrNA,
    calculateGSTFlags,
    checkAllAdditionalChargesNaAndExempt,
    convertToFormData,
    fetchExpenseOriInvoiceData,
    findGstRate,
    prepareExpenseCreditDebitNote,
    prepareItemsData,
    prepareLedgerData,
    prepareSaveAndNewData,
} from "../../shared/prepareData";
import { correctClassificationNatureType, getEditLedgerFromList } from "../../shared/sharedFunction";
import { useTransactionShortcuts } from "../../shared/shortcut-keys";
import { errorToast } from "../../store/actions/toastAction";
import { getAdvancePayment } from "../../store/advance-payment/advancePaymentSlice";
import { fetchBrokerList } from "../../store/broker/brokerSlice";
import { fetchClassificationList } from "../../store/classification/classificationSlice";
import { fetchCompanyDetails, fetchUserPermission } from "../../store/company/companySlice";
import { fetchConfigurationList, rearrangeItemList, resetConfiguration } from "../../store/configuration/configurationSlice";
import { fetchDispatchAddressList } from "../../store/dispatchAddress/dispatchAddressSlice";
import { addExpense, deleteExpenseCnDn, updateExpense } from "../../store/expense-cn-dn/expenseSlice";
import { fetchExpenseInvoice } from "../../store/invoice/invoiceSlice";
import { fetchItemList } from "../../store/item/itemSlice";
import {
    fetchAdditionalLedgerList,
    fetchAddlessLedgerList,
    fetchItemLedgerDetail,
    fetchPartyDetail,
    fetchPartyList,
    fetchPaymentLedgerList,
    fetchPaymentModeList,
} from "../../store/ledger/ledgerSlice";
import { fetchPrevNextUrl } from "../../store/prev-next/prev-nextSlice";
import { fetchTcsList, fetchTdsList } from "../../store/rate/rateSlice";
import { fetchShippingAddressList } from "../../store/shippingAddress/shippingAddressSlice";
import { tableHeader } from "../../store/table/tableSlice";
import { fetchTransportList } from "../../store/transport/transportSlice";
import Error404Page from "../common/404";
import SaleInvoiceDetail from "../common/InvoiceDetail";
import SaleItems from "../common/Items";
import WarningModal from "../common/WarningModal";
import ConfigurationModal from "../modal/Configuration/ConfigurationModal";
import DeleteWarningModal from "../modal/DeleteWarningModal";

const AddExpenseCreditDebitTransaction = ({ id, singleSale, isCreditDuplicate }) => {
    const ExpenseCreateOrDebit = window.location.pathname.includes("credit");
    const isExpenseCredit = window.location.pathname.includes("expense-credit-notes");
    const expenseEdit = window.location.pathname.includes("/edit");
    const url = window.location.origin;
    const shippingAddressType = ExpenseCreateOrDebit ? SHIPPING_ADDRESS_TYPE_LIST.EXPENSE_CREDIT_NOTE : SHIPPING_ADDRESS_TYPE_LIST.EXPENSE_DEBIT_NOTE

    const { roundOffOption, gstOptions, classificationOptions, tableHeaderList, accountingTableHeader } = useDropdownOption();
    const {
        items,
        setItems,
        accountingItems,
        setAccountingItems,
        gstValue,
        setGstValue,
        invoiceDetail,
        setInvoiceDetail,
        partyAddress,
        setPartyAddress,
        ewayBillDetail,
        setEwayBillDetail,
        otherDetail,
        setOtherDetail,
        gstCalculation,
        setGstCalculation,
        paymentLedgerDetail,
        setPaymentLedgerDetail,
        classification,
        setClassification,
        additionalGst,
        setAdditionalGst,
        brokerDetail,
        setBrokerDetail,
        transporterDetail,
        setTransporterDetail,
        localDispatchAddress,
        setLocalDispatchAddress,
        gstQuote,
        setGstQuote,
        additionalCharges,
        setAdditionalCharges,
        tcsRate,
        setTcsRate,
        addLessChanges,
        setAddLessChanges,
        cessValue,
        selectedAddress,
        sameAsBill,
        setSameAsBill,
        itemType,
        setItemType,
        setChangeTax,
        grandTotal,
        setGrandTotal,
        mainGrandTotal,
        setMainGrandTotal,
        setCessValue,
        isEditCalculation,
        setIsEditCalculation,
        finalAmount,
        setFinalAmount,
        setConfigurationModalName,
        setConfigurationURL,
        setConfigurationTableList,
        setConfigurationFooterList,
        isIGSTCalculation,
        setIsIGSTCalculation,
        setIsSGSTCalculation,
        isSGSTCalculation,
        taxableValue,
        setTaxableValue,
        setLoader,
        loader,
        isTcsAmountChange,
        isChangedTcs,
        changeTax,
        setInvoiceValue,
        classificationType,
        deleteTransaction,
        openDeleteTransactionModel,
        closeDeleteTransactionModel,
        isCheckGstType,
        selectedAdvancePayment,
        setSelectedAdvancePayment,
        setShowPaymentTable,
        isDisable,
        setIsDisable,
        showDeleteWarningModel,
        setShowDeleteWarningModel,
        customFieldListTransaction,
        setCustomHeaderListTransaction,
        setHasUnsavedChanges,
        isFieldsChanges,
        setisFieldsChanges,
        isBackButtonClick,
        setIsBackButtonClick,
        setUnsavedBackUrl,
        setIsChangePartyId
    } = useContext(StateContext);
    const [isShowGstValue, setIsShowGstValue] = useState(false);
    const [tcsValue, setTCSValue] = useState(0);
    const [tcsRateValue, setTCSRateValue] = useState(0);
    const [shippingValue, setShippingValue] = useState(0);
    const [packingCharge, setPackingCharge] = useState(0);
    const [updatedTableHeader, setUpdatedTableHeader] = useState([]);
    const slides = [
        {
            image: "https://hisabkitab-staging.infyom.com/assets/images/GST-3.png",
            alt: "Slide 1",
        },
        {
            image: "https://hisabkitab-staging.infyom.com/assets/images/GST-4.png",
            alt: "Slide 2",
        },
    ];
    useEffect(() => {
        document.getElementById("showName").innerHTML = ExpenseCreateOrDebit
            ? expenseEdit
                ? "Edit Credit Notes"
                : "Add Credit Notes"
            : expenseEdit
            ? "Edit Debit Notes"
            : "Add Debit Notes";

        if (!expenseEdit && !isCreditDuplicate) {
            setIsEditCalculation(true);
        }
        setConfigurationModalName(
            ExpenseCreateOrDebit ? "Credit Note Configuration" : "Debit Note Configuration"
        );
        // setIsShowDocumentPrefixes(false);
    }, [ExpenseCreateOrDebit]);

    const dispatch = useDispatch();
    const expenseCnDnRef = useRef(null);
    const formRef = useRef(null);
    const {
        company,
        invoice,
        ledger,
        table,
        dispatchAddress,
        configuration,
        sale,
        prevNext,
        purchase,
        broker,
        expenseNote,
    } = useSelector(selector => selector);

    const fetchPurchaseCrDrDetail = purchase?.getPurchaseById;

    const configurationList = configuration?.configuration;

     useEffect(() => {
            const newCnDnData = JSON.parse(localStorage.getItem('saveAndNewData'));

            if (newCnDnData) {
                setIsChangePartyId(true);
                dispatch(fetchPartyDetail(newCnDnData?.supplier_id));
                dispatch(fetchShippingAddressList(parseFloat(newCnDnData?.supplier_id), shippingAddressType, id));
                if (newCnDnData?.supplier_id) {
                    dispatch(fetchPartyList({ ids: [newCnDnData?.supplier_id] }));
                } else {
                    dispatch(fetchPartyList());
                }

                const newData = newCnDnData;
                prepareSaveAndNewData(newData, setGstQuote, setPartyAddress, additionalCharges, setAdditionalCharges, setItemType);
            }

        }, []);

    useEffect(() => {
        if (singleSale) {
            const cn_dn = ExpenseCreateOrDebit ? "cn" : "dn";
            dispatch(fetchPartyDetail(singleSale?.supplier_id));
            if (singleSale?.supplier_id) {
                dispatch(fetchPartyList({ ids: [singleSale?.supplier_id] }));
            } else {
                dispatch(fetchPartyList());
            }
            const ids = [];
            const item_ledger_id = [];
            const tcs_type = 2;
            const tds_type = 1;
            const is_call_payment_ledger = true;
            if (singleSale?.expense_credit_note_items) {
                singleSale?.expense_credit_note_items?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            } else if (singleSale?.debit_note_items) {
                singleSale?.debit_note_items?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            }

            if (ids.length > 0) {
                dispatch(fetchItemList({ ids }));
            } else {
                dispatch(fetchItemList());
            }
            getEditLedgerFromList({dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction: singleSale});
            if (singleSale?.advance_payment?.length > 0) {
                setShowPaymentTable(true);
                dispatch(getAdvancePayment(singleSale?.supplier_id, ExpenseCreateOrDebit ? "expense-credit-note" : "expense-debit-note", id));
            }

            prepareExpenseCreditDebitNote({
                singleSale,
                id,
                cn_dn,
                setStateFunctions: {
                    invoiceDetail,
                    setInvoiceDetail,
                    setCessValue,
                    setGstQuote,
                    setItemType,
                    setClassification,
                    setItems,
                    setAccountingItems,
                    setAddLessChanges,
                    setAdditionalCharges,
                    setPartyAddress,
                    gstCalculation,
                    setGstCalculation,
                    setBrokerDetail,
                    setTransporterDetail,
                    setEwayBillDetail,
                    setOtherDetail,
                    setGstValue,
                    setTcsRate,
                    setPaymentLedgerDetail,
                    setGrandTotal,
                    setMainGrandTotal,
                    setChangeTax,
                    addLessChanges,
                    additionalCharges,
                    setFinalAmount,
                    setIsIGSTCalculation,
                    setIsSGSTCalculation,
                    taxableValue,
                    setTaxableValue,
                    localDispatchAddress,
                    setLocalDispatchAddress,
                    setSelectedAdvancePayment,
                    setCustomHeaderListTransaction,
                    setSameAsBill,
                    expenseEdit
                },
                dispatch,
                tableHeaderList,
                classificationOptions,
                accountingTableHeader,
                isCreditDuplicate
            });
        }
    }, [singleSale, classificationOptions]);

    useEffect(() => {
        if (fetchPurchaseCrDrDetail?.length !== 0) {
            const cn_dn = ExpenseCreateOrDebit ? "cn" : "dn";
            const ids = [];
            const item_ledger_id = [];
            const tcs_type = 2;
            const tds_type = 1;
            const is_call_payment_ledger = true;
            if (fetchPurchaseCrDrDetail?.purchase_transaction_items) {
                fetchPurchaseCrDrDetail?.purchase_transaction_items?.forEach(item => {
                    ids.push(item.item_id);
                    item_ledger_id.push(item.ledger_id);
                });
            }else if (fetchPurchaseCrDrDetail?.purchase_transaction_ledgers) {
                fetchPurchaseCrDrDetail?.purchase_transaction_ledgers?.forEach(item => {
                    item_ledger_id.push(item.ledger_id);
                });
            }
            if (ids.length > 0) {
                dispatch(fetchItemList({ ids }));
            } else {
                dispatch(fetchItemList());
            }
            getEditLedgerFromList({dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction: fetchPurchaseCrDrDetail});
            fetchExpenseOriInvoiceData(fetchPurchaseCrDrDetail, {
                ExpenseCreateOrDebit,
                setStateFunctions: {
                    invoiceDetail,
                    setInvoiceDetail,
                    setCessValue,
                    gstQuote,
                    setGstQuote,
                    setItemType,
                    setClassification,
                    setItems,
                    setAccountingItems,
                    setAddLessChanges,
                    setAdditionalCharges,
                    setPartyAddress,
                    setGstCalculation,
                    setBrokerDetail,
                    setTransporterDetail,
                    setEwayBillDetail,
                    setOtherDetail,
                    setGstValue,
                    setTcsRate,
                    setPaymentLedgerDetail,
                    setGrandTotal,
                    setMainGrandTotal,
                    setChangeTax,
                    setFinalAmount,
                    setIsIGSTCalculation,
                    setIsSGSTCalculation,
                    setTaxableValue,
                    setCustomHeaderListTransaction,
                    setSameAsBill,
                    expenseEdit
                },
                dispatch,
                tableHeaderList,
                accountingTableHeader,
                addLessChanges,
                additionalCharges,
                classificationOptions,
            });
        }
    }, [fetchPurchaseCrDrDetail, classificationOptions]);

    useEffect(() => {
        setLoader(true);
        dispatch(
            fetchConfigurationList(
                ExpenseCreateOrDebit
                ? apiBaseURL.EXPENSE_CREDIT_NOTE_CONFIGURATION
                : apiBaseURL.EXPENSE_DEBIT_NOTE_CONFIGURATION
            )
        );
        setConfigurationURL(
            ExpenseCreateOrDebit
            ? apiBaseURL.EXPENSE_CREDIT_NOTE_CONFIGURATION
            : apiBaseURL.EXPENSE_DEBIT_NOTE_CONFIGURATION
        );
        dispatch(fetchExpenseInvoice(ExpenseCreateOrDebit));
        dispatch(fetchPartyDetail(""));
        dispatch(fetchUserPermission());
        dispatch(fetchDispatchAddressList());
        dispatch(fetchCompanyDetails());
        dispatch(tableHeader(""));

        setTimeout(() => {
            dispatch(fetchClassificationList(2));
            if (!expenseEdit) {
                dispatch(fetchItemList());
                dispatch(fetchPartyList());
                dispatch(fetchAddlessLedgerList());
                dispatch(fetchPaymentLedgerList());
                dispatch(fetchTcsList({id:2}));
                dispatch(fetchTdsList({id:1}));
                dispatch(fetchItemLedgerDetail());
                dispatch(fetchAdditionalLedgerList());
            }
            dispatch(
                fetchPrevNextUrl(ExpenseCreateOrDebit ? { type: "11", id: id } : { type: "10", id: id })
            );
            dispatch(fetchBrokerList());
            dispatch(fetchTransportList());
            dispatch(fetchPaymentModeList(isExpenseCredit ? 2 : 1));
        }, 1500);
    }, [dispatch]);

    useEffect(() => {
        dispatch(resetConfiguration());
    }, []);

    useEffect(() => {
        setTimeout(() => {
            if (configurationList) {
                setLoader(false);
            }
        }, 300)
    }, [configurationList])

    useEffect(() => {
        if (
            !configurationList?.header?.is_change_gst_details &&
            // !classification?.classification_nature_name &&
            localDispatchAddress &&
            company?.company?.is_gst_applicable && ((!id && !isCreditDuplicate && !gstQuote?.original_inv_no) || isCheckGstType)
        ) {
            if (
                partyAddress?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id
            ) {
                setIsSGSTCalculation(true);
                setIsIGSTCalculation(false);
            } else {
                setIsSGSTCalculation(false);
                setIsIGSTCalculation(true);
            }
        }
    }, [
        ledger?.partyDetail?.billingAddress?.state_id,
        localDispatchAddress,
        gstQuote.original_inv_no,
        isCheckGstType,
        partyAddress?.billingAddress
    ]);

    useEffect(() => {
        if (
            !configurationList?.header?.is_change_gst_details &&
            company?.company?.is_gst_applicable && isCheckGstType
        ) {
            setClassification({
                rcm_applicable: false,
                classification_nature_name: "",
                classification_nature: 0,
            });
            if (
                partyAddress?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id
            ) {
                setIsSGSTCalculation(true);
                setIsIGSTCalculation(false);
            } else {
                setIsSGSTCalculation(false);
                setIsIGSTCalculation(true);
            }
        }
    }, [configurationList?.header?.is_change_gst_details]);

    useEffect(() => {
        setLocalDispatchAddress(dispatchAddress?.dispatchAddress);
    }, [dispatchAddress?.dispatchAddress]);

    useEffect(() => {
        setConfigurationTableList(
            [
                company?.company?.is_gst_applicable && {
                    name: "Change GST Details",
                    key: "is_change_gst_details",
                    value: configurationList?.header?.is_change_gst_details,
                    position: 1,
                },
                {
                    name: "Additional Ledger Description",
                    key: "is_additional_ledger_description",
                    value: configurationList?.item_table_configuration
                        ?.is_additional_ledger_description,
                    position: 3,
                },
                {
                    name: "Discount 2",
                    key: "is_enabled_discount_2",
                    value: configurationList?.item_table_configuration?.is_enabled_discount_2,
                    position: 7,
                },
                {
                    name: "MRP",
                    key: "is_enabled_mrp",
                    value: configurationList?.item_table_configuration?.is_enabled_mrp,
                    position: 6,
                },
            ].filter(Boolean)
        );

        setConfigurationFooterList([
            {
                name: "TCS",
                key: "is_enabled_tcs_details",
                value: configurationList?.footer?.is_enabled_tcs_details,
                position: 1,
            },
            {
                name: "TDS",
                key: "is_enabled_tds_details",
                value: configurationList?.footer?.is_enabled_tds_details,
                position: 2,
            },
            {
                name: "Payment Details",
                key: "is_enabled_payment_details",
                value: configurationList?.footer?.is_enabled_payment_details,
                position: 6,
            },
        ]);
    }, [configurationList, company]);

    useEffect(() => {
        if (isEditCalculation) {
            const { itemTotal, cessTotal } = calculateTotals(
                itemType === "accounting" ? accountingItems : items,
                classificationType
            );
            setGrandTotal(itemTotal);
            const addition_charges = calculateAdditionalCharges(
                additionalCharges,
                itemTotal,
                isIGSTCalculation
            );
            setTaxableValue(parseFloat(addition_charges?.addition_charges) + itemTotal);
            // setCessValue(cessTotal);
            const newInvoiceValue = isIGSTCalculation
                ? parseFloat(taxableValue || 0) +
                  parseFloat(cessValue || 0) +
                  parseFloat(gstValue?.igstValue || 0) +
                  parseFloat(additionalGst || 0)
                : parseFloat(taxableValue || 0) +
                  parseFloat(cessValue || 0) +
                  parseFloat(gstValue?.sgstValue || 0) +
                  parseFloat(additionalGst || 0) +
                  parseFloat(additionalGst || 0) +
                  parseFloat(gstValue?.cgstValue || 0);

            // Update the invoice value
            setInvoiceValue(newInvoiceValue);
            if (taxableValue > 0 && tcsRate?.tcs_rate && !isTcsAmountChange && isChangedTcs) {
                const tcsRateAmount =
                    tcsRate.tcs_calculated_on == CALCULATION_ON_TYPE.INVOICE_VALUE
                        ? parseFloat(newInvoiceValue * ((tcsRate?.tcs_rate || 0) / 100))
                        : parseFloat(taxableValue * ((tcsRate?.tcs_rate || 0) / 100));
                setTcsRate({
                    ...tcsRate,
                    tcs_amount: parseFloat(tcsRateAmount || 0).toFixed(2),
                });
            }
            const cgst =
                !isIGSTCalculation && isSGSTCalculation && !classification.rcm_applicable
                    ? customToFixed(parseFloat(gstValue?.cgstValue) + parseFloat(additionalGst || 0), 2)
                    : 0;
            const sgst =
                !isIGSTCalculation && isSGSTCalculation && !classification.rcm_applicable
                    ? customToFixed(parseFloat(gstValue?.sgstValue) + parseFloat(additionalGst || 0), 2)
                    : 0;
            const igst =
                isIGSTCalculation && !classification.rcm_applicable
                    ? customToFixed(parseFloat(gstValue?.igstValue) + parseFloat(additionalGst || 0), 2)
                    : 0;

            const cgstWithoutRCM =
                    !isIGSTCalculation && isSGSTCalculation
                        ? customToFixed(parseFloat(gstValue?.cgstValue) + parseFloat(additionalGst || 0), 2)
                        : 0;
            const sgstWithoutRCM =
                    !isIGSTCalculation && isSGSTCalculation
                        ? customToFixed(parseFloat(gstValue?.sgstValue) + parseFloat(additionalGst || 0), 2)
                        : 0;
            const igstWithoutRCM =
                    isIGSTCalculation
                        ? customToFixed(parseFloat(gstValue?.igstValue) + parseFloat(additionalGst || 0), 2)
                        : 0;
            const total =
                parseFloat(itemTotal) +
                parseFloat(addition_charges?.addition_charges || 0) +
                parseFloat(configurationList?.footer?.is_enabled_tcs_details ? tcsRate.tcs_tax_id ? isNaN(tcsRate.tcs_amount) ? 0 : tcsRate.tcs_amount : 0 || 0 : 0) +
                // (parseFloat(totalAddLessAmount) || 0) +
                parseFloat(company?.company?.is_gst_applicable ? cessValue || 0 : 0) +
                parseFloat(company?.company?.is_gst_applicable ? cgst : 0) +
                parseFloat(company?.company?.is_gst_applicable ? sgst : 0) +
                parseFloat(company?.company?.is_gst_applicable ? igst : 0);

            const totalWithoutAddLess =
                parseFloat(itemTotal) +
                parseFloat(addition_charges?.addition_charges || 0) +
                parseFloat(configurationList?.footer?.is_enabled_tcs_details ? tcsRate.tcs_tax_id ? isNaN(tcsRate.tcs_amount) ? 0 : tcsRate.tcs_amount : 0 || 0 : 0) +
                parseFloat(company?.company?.is_gst_applicable ? cessValue || 0 : 0) +
                parseFloat(company?.company?.is_gst_applicable ? cgstWithoutRCM : 0) +
                parseFloat(company?.company?.is_gst_applicable ? sgstWithoutRCM : 0) +
                parseFloat(company?.company?.is_gst_applicable ? igstWithoutRCM : 0);

            const updatedAddLessCharges = calculateAddLessCharges(
                addLessChanges,
                totalWithoutAddLess
            );
            const totalAddLessAmount = updatedAddLessCharges?.reduce((sum, change) => {
                if (change.al_type === 1) {
                    return sum + (parseFloat(change.al_value) || 0);
                } else if (change.al_type === 2) {
                    return sum + (parseFloat(change.al_total) || 0);
                }
                return sum;
            }, 0);
            setMainGrandTotal(parseFloat(total + (totalAddLessAmount || 0)));
            const gstData = calculateAdditionalClassification(classification, addition_charges?.acTotal);
            setAdditionalGst(gstData);
        }
    }, [
        taxableValue,
        tcsRate.tcs_amount,
        grandTotal,
        mainGrandTotal,
        cessValue,
        items,
        addLessChanges,
        gstValue,
        additionalGst,
        additionalCharges,
        configurationList?.footer?.round_off_method,
        configurationList?.footer?.is_enabled_tcs_details
    ]);

    const calculateTotal = () => {
        const { grandFinalAmount, roundOffAmount } = RoundOffMethod(
            mainGrandTotal,
            (id || gstQuote?.original_inv_no) && !isCheckGstType && gstCalculation?.round_off_method ? gstCalculation?.round_off_method : configurationList?.footer?.round_off_method
        );
        setGstCalculation({
            ...gstCalculation,
            is_gst_enabled: company?.company?.is_gst_applicable,
            round_of_amount: roundOffAmount,
        });
        setFinalAmount(grandFinalAmount);
    };

    useEffect(() => {
        if (isEditCalculation) {
            calculateTotal();
        }
    }, [mainGrandTotal, configurationList?.footer?.round_off_method, company, additionalCharges, isCheckGstType]);

    useEffect(() => {
        if (isEditCalculation) {
            const newInvoiceValue = isIGSTCalculation
                ? parseFloat(taxableValue || 0) +
                  parseFloat(cessValue || 0) +
                  parseFloat(gstValue?.igstValue || 0) +
                  parseFloat(additionalGst || 0)
                : parseFloat(taxableValue || 0) +
                  parseFloat(cessValue || 0) +
                  parseFloat(gstValue?.sgstValue || 0) +
                  parseFloat(additionalGst || 0) +
                  parseFloat(additionalGst || 0) +
                  parseFloat(gstValue?.cgstValue || 0);

            // Update the invoice value
            setInvoiceValue(newInvoiceValue);
            if (tcsRate?.tcs_rate) {
                setTcsRate(prevRate => ({
                    ...prevRate,
                    tcs_amount:
                        tcsRate.tcs_calculated_on == CALCULATION_ON_TYPE.INVOICE_VALUE
                            ? parseFloat(
                                  newInvoiceValue * ((tcsRate?.tcs_rate || 0) / 100)
                              ).toFixed(2)
                            : parseFloat(taxableValue * ((tcsRate?.tcs_rate || 0) / 100)).toFixed(
                                  2
                              ),
                }));
            }
            if (paymentLedgerDetail.tds_rate) {
                setPaymentLedgerDetail({
                    ...paymentLedgerDetail,
                    tds_amount: RoundOffMethodForTds(taxableValue * (paymentLedgerDetail?.tds_rate / 100), paymentLedgerDetail?.rounding_type),
                });
            }
        }
    }, [taxableValue]);

    useEffect(() => {
        let updatedHeaders = table?.tableHeader || [];
        if (
            configurationList?.item_table_configuration?.is_enabled_discount_2 === false ||
            configurationList?.item_table_configuration?.is_enabled_discount_2 == 0
        ) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.DISCOUNT_2);
        }
        if (configurationList?.item_table_configuration?.is_enabled_mrp === false) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.MRP);
        }
        if (!configurationList?.item_table_configuration?.is_enabled_hsn_code) {
            updatedHeaders = updatedHeaders?.filter(item => item.header !== TABLE_HEADER_TYPE.HSN_SAC);
        }
        const filteredColumns = updatedHeaders.filter(col => col?.is_enabled !== false);
        setUpdatedTableHeader(filteredColumns);
    }, [table?.tableHeader, configurationList]);

    useEffect(() => {
        if (!id) {
            const maxDate = moment(company?.company?.currentFinancialYear?.yearEndDate).format(
                "YYYY-MM-DD"
            );
            let newDate = "";
            const date1 = new Date();
            const date2 = new Date(maxDate);

            if (date1 < date2) {
                newDate = moment(date1).format("DD-MM-YYYY");
            } else {
                newDate = moment(date2).format("DD-MM-YYYY");
            }
            setInvoiceDetail({
                ...invoiceDetail,
                invoice_date: newDate,
                invoice_number: invoice?.expenseInvoice?.invoice_number,
            });
            setChangeTax(invoice?.expenseInvoice?.with_tax ? 1 : 0);
        }
    }, [invoice?.expenseInvoice]);

    useEffect(() => {
        if(invoice?.expenseInvoice?.invoice_type){
            dispatch(rearrangeItemList(ExpenseCreateOrDebit ? TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE : TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE, invoice?.expenseInvoice?.invoice_type == 2 ? 1 : 2));
            setItemType(invoice?.expenseInvoice?.invoice_type == 2 ? "item" : "accounting");
        }
    }, [invoice])

    useEffect(() => {
        if (configurationList?.header?.is_change_gst_details) {
            const gstData = calculateClassification(
                classification,
                itemType === "item"
                    ? findGstRate(items, gstOptions)
                    : findGstRate(accountingItems, gstOptions)
            );
            if (isEditCalculation) {
                setGstValue({
                    cgstValue: gstData.totalCgstTax !== NaN ? gstData?.totalCgstTax : 0.0,
                    sgstValue: gstData?.totalSgstTax || 0,
                    igstValue: gstData?.totalIgstTax || 0,
                });
            }
        } else {
            const matchState =
                ledger?.partyDetail?.billingAddress?.state_id ===
                localDispatchAddress[selectedAddress]?.state_id;
            const gstData = gstCalculate(
                itemType === "item"
                    ? findGstRate(items, gstOptions)
                    : findGstRate(accountingItems, gstOptions),
                grandTotal,
                matchState
            );
            if (isEditCalculation) {
                setGstValue({
                    cgstValue:
                        gstData.totalCgstTax !== NaN ? parseFloat(gstData.totalCgstTax) : 0.0,
                    sgstValue: parseFloat(gstData.totalSgstTax) || 0,
                    igstValue: parseFloat(gstData.totalIgstTax) || 0,
                });
            }
        }
    }, [
        classification,
        grandTotal,
        company,
        ledger?.partyDetail,
        accountingItems,
        items,
        additionalCharges,
        addLessChanges,
        tcsRate.tcs_amount,
        configurationList?.footer?.round_off_method,
    ]);
    useEffect(() => {
        if (ledger?.partyDetail?.billingAddress?.state_id) {
            const GstType =
                localDispatchAddress[selectedAddress]?.state_id ==
                ledger?.partyDetail?.billingAddress?.state_id
                    ? true
                    : false;
            setIsShowGstValue(GstType);
        }
    }, [company, ledger?.partyDetail]);

    useEffect(() => {
        const payment_detail = paymentLedgerDetail?.payment_detail?.map(payment => {
            const paymentDate = invoiceDetail.invoice_date;
            return {
                ...payment,
                pd_date: payment.is_show_invoice_date ? paymentDate : payment.pd_date,
            };
        });
        setPaymentLedgerDetail({
            ...paymentLedgerDetail,
            payment_detail: payment_detail,
        });
    }, [invoiceDetail]);

    useTransactionShortcuts(formRef);

    const handleSubmit = e => {
        const submitType = e.nativeEvent.submitter.value;
        const submit_button = submitType === "save" ? 1 : submitType == "saveAndNew" ? 2 : 3;
        const submit_button_type = submitType == "saveAndNew" ? "saveAndNew" : "";

        e.preventDefault();
        const dispatch_address = localDispatchAddress[selectedAddress] || company?.company?.billing_address;

        const isFirstDetailInvalid = detail => {
            return (
                (detail.ac_ledger_id === null && !detail.ac_value) ||
                detail.ac_value === "" ||
                (detail.ac_value == NaN && detail.ac_gst_rate_id === null) ||
                (detail.ac_gst_rate_id?.value === null && detail.ac_total === 0)
            );
        };
        const {
            itemList: item,
            is_na,
            hasNegativeTotal,
        } = prepareItemsData(
            items,
            gstCalculation,
            setGstCalculation,
            company,
            gstOptions,
            configurationList,
            changeTax
        );
        const {
            ledgerList: ledger,
            is_na: is_na_ledger,
            hasNegativeLedgerTotal,
        } = prepareLedgerData(
            accountingItems,
            gstCalculation,
            setGstCalculation,
            gstOptions,
            configurationList,
            changeTax
        );
        const {isExempt, isNa} = areAllProductsExemptOrNA(itemType === "accounting" ? accountingItems : items)
        const {isAdditionalChargesExempt,isAdditionalChargesNa } = checkAllAdditionalChargesNaAndExempt(additionalCharges)

        const { is_cgst_sgst_igst_calculated, is_gst_na } = calculateGSTFlags(
            company,
            isNa,
            isExempt,
            isAdditionalChargesNa,
            isAdditionalChargesExempt
        );
        if (submitType) {
            setIsDisable(true);
            if (
                company?.company?.is_gst_applicable &&
                configurationList?.header?.is_change_gst_details &&
                !classification.classification_nature_name
            ) {
                setIsDisable(false);
                return dispatch(
                    errorToast({
                        text: "Classification nature type is required.",
                        type: toastType.ERROR,
                    })
                );
            }

            if (hasNegativeTotal) {
                setIsDisable(false);
                return dispatch(
                    errorToast({
                        text: "Negative Item Amount Not Accepted.",
                        type: toastType.ERROR,
                    })
                );
            }

            if (hasNegativeLedgerTotal) {
                setIsDisable(false);
                return dispatch(
                    errorToast({
                        text: "Negative Item Amount Not Accepted.",
                        type: toastType.ERROR,
                    })
                );
            }
            const submitData = {
                ...(ExpenseCreateOrDebit
                    ? {
                          expense_cn_item_type: itemType === "item" ? 2 : 1,
                      }
                    : {
                          dn_item_type: itemType === "item" ? 2 : 1,
                      }),
                voucher_number: invoiceDetail.invoice_number,
                voucher_date: (invoiceDetail.invoice_date),
                supplier_purchase_return_number: invoiceDetail.debit_note_number,
                supplier_purchase_return_date: (invoiceDetail.debit_note_date),
                original_inv_no: gstQuote.original_inv_no,
                original_inv_date: (gstQuote.original_inv_date),
                supplier_id: gstQuote.party_ledger_id,
                ...(company?.company?.is_gst_applicable && {
                    gstin: gstQuote.gstin,
                }),
                billing_address: {
                    address_1: partyAddress.billingAddress.address_1,
                    address_2: partyAddress.billingAddress.address_2,
                    country_id: partyAddress.billingAddress.country_id,
                    state_id: partyAddress.billingAddress.state_id,
                    city_id: partyAddress.billingAddress.city_id,
                    pin_code: partyAddress.billingAddress.pin_code,
                },
                same_as_billing: sameAsBill ? 1 : 0,
                shipping_gstin: partyAddress?.shippingAddress?.shipping_gstin,
                shipping_name: partyAddress?.shippingAddress?.shipping_name,
                address_name: partyAddress?.shippingAddress?.address_name,
                party_name_same_as_address_name: partyAddress?.shippingAddress?.party_name_same_as_address_name ? 1 : 0,
                ...(partyAddress.shippingAddress?.shipping_address_id ?
                {shipping_address_id: partyAddress.shippingAddress?.shipping_address_id} :
                {shipping_address: configurationList?.header?.is_enabled_shipping_address
                    ? {
                          address_name: partyAddress?.shippingAddress?.address_name,
                          shipping_gstin: partyAddress?.shippingAddress?.shipping_gstin,
                          shipping_name: partyAddress?.shippingAddress?.shipping_name,
                          address_1: partyAddress?.shippingAddress?.address_1,
                          address_2: partyAddress?.shippingAddress?.address_2,
                          country_id: partyAddress?.shippingAddress?.country_id,
                          state_id: partyAddress?.shippingAddress?.state_id,
                          city_id: partyAddress?.shippingAddress?.city_id,
                          pin_code: partyAddress?.shippingAddress?.pin_code,
                      }
                    : null}),
                broker_details:
                    configurationList?.header?.is_enabled_broker_details && brokerDetail?.broker_id
                        ? {
                              broker_id: brokerDetail.broker_id,
                              brokerage_for_sale: brokerDetail.broker_percentage,
                              brokerage_on_value_type: brokerDetail.brokerage_on_value,
                          }
                        : null,
                transport_details: configurationList?.header?.is_enabled_transport_details
                    ? {
                          transport_id: transporterDetail.transport_id,
                          transporter_document_number:
                              transporterDetail.transporter_document_number,
                          transporter_document_date: transporterDetail.transporter_document_date,
                          transporter_vehicle_number: transporterDetail.transporter_vehicle_number,
                      }
                    : null,
                eway_bill_details: configurationList?.header?.is_enabled_eway_details
                    ? {
                          eway_bill_number: ewayBillDetail.eway_bill_number,
                          eway_bill_date: ewayBillDetail.eway_bill_date,
                      }
                    : null,
                other_details: {
                    po_no: otherDetail.po_number,
                    po_date: otherDetail.date,
                    credit_period: otherDetail.creditPeriod,
                    credit_period_type: otherDetail.creditPeriodType,
                    // shipping_name: null,
                    // shipping_gstin: null,
                },
                ...(itemType === "item" && { items: item }),
                ...(itemType === "accounting" && { ledgers: ledger }),
                main_classification_nature_type:
                    correctClassificationNatureType(isCheckGstType,
                        classification.classification_nature_name,
                        configurationList?.header?.is_change_gst_details,
                        partyAddress.billingAddress.state_id,
                        dispatch_address?.state_id,
                        isNa,
                        isExempt,
                        isAdditionalChargesNa,
                        isAdditionalChargesExempt,
                        true),
                is_rcm_applicable: classification.rcm_applicable ? 1 : 0,
                narration: additionalCharges?.note || null,
                term_and_condition: additionalCharges?.terms_and_conditions || null,
                ...(additionalCharges?.additional_detail?.length === 1 &&
                isFirstDetailInvalid(additionalCharges?.additional_detail[0])
                    ? { additional_charges: " " }
                    : {
                          additional_charges: additionalCharges?.additional_detail?.map(detail => ({
                              ...detail,
                              ...(company?.company?.is_gst_applicable && {
                                  ac_gst_rate_id: detail.ac_gst_rate_id?.value || null,
                              }),
                              //   add_less_total: detail?.ac_total,
                              ac_total_without_tax:
                                  detail?.ac_type == 2 ? detail?.ac_total : detail.ac_value,
                          })),
                      }),
                taxable_value: customToFixed(taxableValue, 2),
                total: grandTotal,
                gross_value: customToFixed(grandTotal, 2),
                ...(company?.company?.is_gst_applicable && {
                    cgst:
                        customToFixed(
                            !isIGSTCalculation && isSGSTCalculation
                                ? !isEditCalculation
                                    ? parseFloat(gstValue?.cgstValue)
                                    : parseFloat(gstValue?.cgstValue) + parseFloat(additionalGst || 0)
                                : 0,
                            2
                        ) || 0
                }),
                ...(company?.company?.is_gst_applicable && {
                    sgst:
                        customToFixed(
                            !isIGSTCalculation && isSGSTCalculation
                                ? !isEditCalculation
                                    ? parseFloat(gstValue?.sgstValue)
                                    : parseFloat(gstValue?.sgstValue) + parseFloat(additionalGst || 0)
                                : 0,
                            2
                        ) || 0
                }),
                ...(company?.company?.is_gst_applicable && {
                    igst: customToFixed(
                        isIGSTCalculation
                            ? !isEditCalculation
                                ? parseFloat(gstValue?.igstValue)
                                : parseFloat(gstValue?.igstValue) + parseFloat(additionalGst || 0)
                            : 0,
                        2
                    )
                }),
                ...(configurationList?.footer?.is_enabled_tcs_details ? {tcs_details: tcsRate.tcs_tax_id ? tcsRate : []} : {}),
                add_less: addLessChanges[0]?.al_ledger_id ? addLessChanges : [],
                grand_total: customToFixed(finalAmount, 2),
                ...(configurationList?.footer?.is_enabled_tds_details ? {tds_details: {
                    tds_tax_id: paymentLedgerDetail.tds_tax_id,
                    tds_rate: paymentLedgerDetail.tds_rate,
                    tds_amount: paymentLedgerDetail.tds_amount,
                }} : {}),
                ...(paymentLedgerDetail.payment_detail[0]?.pd_ledger_id
                    ? {
                          payment_details: paymentLedgerDetail.payment_detail,
                      }
                    : {
                          payment_details: " ",
                      }),
                cess: customToFixed(cessValue, 2),
                round_off_amount: customToFixed(gstCalculation.round_of_amount, 2),
                is_gst_enabled: !company?.company?.is_gst_applicable
                    ? 0
                    : gstCalculation.is_gst_enabled || 1,
                rounding_amount: customToFixed(gstCalculation.round_of_amount, 2),
                is_cgst_sgst_igst_calculated,
                is_gst_na,
                is_round_off_not_changed: gstCalculation.is_round_off_not_changed ?? 1,
                submit_button_value: submit_button,
                advance_payment: selectedAdvancePayment,
                // custom_fields: customFieldListTransaction?.flatMap((customField) =>
                //     customField.value ? [{ ...(customField?.id ? {id: customField?.id} : {}), custom_field_id: customField.custom_field_id, value: customField.value }] : []
                // ),
                custom_fields: customFieldListTransaction?.flatMap(customField =>
                    customField.value &&
                    (customField.is_enabled === undefined || customField.is_enabled)
                        ? [
                              {
                                  ...(customField?.id ? { id: customField.id } : {}),
                                  custom_field_id: customField.custom_field_id,
                                  value: customField.value,
                              },
                          ]
                        : []
                ),
                round_off_method: (id || isCreditDuplicate || gstQuote?.original_inv_no) && !isCheckGstType && gstCalculation?.round_off_method ? gstCalculation?.round_off_method : configurationList?.footer?.round_off_method
            };
            const formData = convertToFormData(submitData);
            if (additionalCharges?.upload_document) {
                additionalCharges.upload_document.forEach((doc, index) => {
                    if (doc.file) {
                        formData.append(
                            ExpenseCreateOrDebit
                                ? `expense_credit_note_document[${index}]`
                                : `expense_debit_note_document[${index}]`,
                            doc.file ? doc.file : " "
                        );
                    }
                });
            }
            if (id) {
                dispatch(
                    updateExpense(id, formData, submitType, ExpenseCreateOrDebit, submit_button, setIsDisable)
                );
            } else {
                dispatch(
                    addExpense(
                        formData,
                        submitType === "saveAndNew" ? "duplicate" : submitType,
                        ExpenseCreateOrDebit,
                        submit_button,
                        setIsDisable,
                        submitData,
                        submit_button_type
                    )
                );
            };
            setisFieldsChanges(false);
            setHasUnsavedChanges(false);
        }
    };

    useEffect(() => {
        setBrokerDetail({
            ...brokerDetail,
            broker_percentage: broker?.getBrokerDetailsById?.brokerage_for_purchase || "",
            brokerage_on_value: broker?.getBrokerDetailsById?.brokerage_for_purchase_type || "",
        });
    }, [broker?.getBrokerDetailsById]);

    const salesUrl = ExpenseCreateOrDebit
        ? "/company/expense-credit-notes"
        : "/company/expense-debit-notes";
    const ExpenceCrPrevUrl = `${url}${salesUrl}/${
        prevNext?.fetchPrevNextUrl?.previousBillId + "/edit"
    }`;
    const ExpenseCrNextUrl = `${url}${salesUrl}/${
        prevNext?.fetchPrevNextUrl?.nextBillId
            ? prevNext.fetchPrevNextUrl.nextBillId + "/edit"
            : "create"
    }`;
    const ExpenseDrPrevUrl = `${url}${salesUrl}/${
        prevNext?.fetchPrevNextUrl?.previousBillId + "/edit"
    }`;
    const ExpenseDrNextUrl = `${url}${salesUrl}/${
        prevNext?.fetchPrevNextUrl?.nextBillId
            ? prevNext.fetchPrevNextUrl.nextBillId + "/edit"
            : "create"
    }`;


    const handleDeleteTransaction = () => {
        if (id) {
            openDeleteTransactionModel()
        }
    };

    const handleConfirmDelete = () => {

        if (id) {
            dispatch(deleteExpenseCnDn(id, ExpenseCreateOrDebit, setShowDeleteWarningModel));
            closeDeleteTransactionModel();
        }
    }

    useEffect(() => {
        const isCreate = window.location.pathname.includes("expense-debit-notes/create") || window.location.pathname.includes("expense-credit-notes/create")

        if (isCreate) {
            setTimeout(() => {
                setLoader(false);
            }, 1000);
        }
    }, [])

    const handleInput = (e) => {
        setisFieldsChanges(true);
    };

    const handleBack = () => {
        if(isFieldsChanges) {
            setHasUnsavedChanges(true);
        };
    };

    const handleBeforeUnload = (e) => {
        if (isFieldsChanges && isBackButtonClick) {
            e.preventDefault();
            e.returnValue = '';
        }
    };

    useEffect(() => {
        if (isBackButtonClick) {
                window.addEventListener('beforeunload', handleBeforeUnload);
            return () => {
                window.removeEventListener('beforeunload', handleBeforeUnload);
            };
        };
    }, [isFieldsChanges, isBackButtonClick]);

    return (
        <>
            <CustomHelmet
                title={
                    id
                        ? ExpenseCreateOrDebit
                            ? "Edit Credit Notes "
                            : "Edit Debit Notes "
                        : ExpenseCreateOrDebit
                        ? "Add Credit Notes "
                        : "Add Debit Notes "
                }
            />
            {loader ? (
                <Loader />
            ) : loader && !configurationList?.document_prefix ? (
                <Loader />
            ) : expenseNote.status === 404 && !window.location.pathname.includes("/create") ? (
                <Error404Page />
            ) : (
                <div className="ms-3 mt-12" defaultActiveKey={"edit"} id="uncontrolled-tab-example">
                    {/* <Tab eventKey={"edit"} title="Edit"> */}
                    <form ref={formRef} onSubmit={handleSubmit} onInput={handleInput} >
                        <Container fluid className="p-0">
                            <div className="content-wrapper-invoice py-6 px-lg-10 px-sm-8 px-6">
                                <SaleInvoiceDetail
                                    ExpenseCreateOrDebit={ExpenseCreateOrDebit}
                                    isShowExpenseCreateOrDebit={true}
                                    isPurchase={true}
                                    id={id}
                                    expenseCnDnRef={expenseCnDnRef}
                                    ledgerModalName={"Add Supplier"}
                                    ledgerModalEditName={"Update Supplier"}
                                    shipping_address_type={shippingAddressType}
                                />
                            </div>
                            <div className="custom-nav-tabs nav-tabs d-flex">
                                <a
                                    href={
                                        !isFieldsChanges
                                            ? ExpenseCreateOrDebit
                                                ? ExpenseDrPrevUrl
                                                : ExpenceCrPrevUrl
                                            : undefined
                                    }
                                    onClick={e => {
                                        if (isFieldsChanges) {
                                            e.preventDefault();
                                            setUnsavedBackUrl(
                                                ExpenseCreateOrDebit
                                                    ? ExpenseDrPrevUrl
                                                    : ExpenceCrPrevUrl
                                            );
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        }
                                    }}
                                    className={`arrow-btn prev-btn d-flex justify-content-center ${
                                        !prevNext?.fetchPrevNextUrl?.previousBillId && "disabled"
                                    } align-items-center cursor_pointer`}
                                >
                                    <i className="fa-solid fa-angle-left"></i>
                                </a>
                                <a
                                    href={
                                        !isFieldsChanges
                                            ? ExpenseCreateOrDebit
                                                ? ExpenseDrNextUrl
                                                : ExpenseCrNextUrl
                                            : undefined
                                    }
                                    onClick={e => {
                                        if (isFieldsChanges) {
                                            e.preventDefault();
                                            setUnsavedBackUrl(
                                                ExpenseCreateOrDebit
                                                    ? ExpenseDrNextUrl
                                                    : ExpenseCrNextUrl
                                            );
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        }
                                    }}
                                    className={`arrow-btn next-btn d-flex justify-content-center align-items-center  ${
                                        !prevNext?.fetchPrevNextUrl?.nextBillId && !id && "disabled"
                                    } me-3 cursor_pointer`}
                                >
                                    <i className="fa-solid fa-angle-right"></i>
                                </a>
                                <ConfigurationModal roundOffOption={roundOffOption} />
                            </div>
                        </Container>
                        <SaleItems
                            items={itemType === "item" ? items : accountingItems}
                            setItems={itemType === "item" ? setItems : setAccountingItems}
                            grandTotal={grandTotal}
                            setGrandTotal={setGrandTotal}
                            mainGrandTotal={mainGrandTotal}
                            shippingValue={shippingValue}
                            setShippingValue={setShippingValue}
                            tcsValue={tcsValue}
                            setTCSValue={setTCSValue}
                            tcsRateValue={tcsRateValue}
                            setTCSRateValue={setTCSRateValue}
                            packingCharge={packingCharge}
                            setPackingCharge={setPackingCharge}
                            tableHeader={updatedTableHeader}
                            tableHeaderList={tableHeaderList}
                            accountingTableHeader={accountingTableHeader}
                            isShowGstValue={isShowGstValue}
                            taxableValue={taxableValue}
                            setFinalAmount={setFinalAmount}
                            finalAmount={finalAmount}
                            isPurchase={true}
                            settlePaymentType={
                                ExpenseCreateOrDebit ? "expense-credit-note" : "expense-debit-note"
                            }
                            shipping_address_type={shippingAddressType}
                        />
                        <Container fluid className="p-0 mt-10 fixed-bottom-section">
                            <div className="d-flex flex-wrap gap-sm-4 gap-3 fixed-buttons px-lg-10 px-sm-8 px-6">
                                <button
                                    type="submit"
                                    name="submitType"
                                    value="save"
                                    className="btn btn-primary"
                                    disabled={isDisable}
                                >
                                    Save
                                </button>
                                {!id && (
                                    <button
                                        type="submit"
                                        name="submitType"
                                        value="saveAndNew"
                                        className="btn btn-primary"
                                        disabled={isDisable}
                                    >
                                        Save & New
                                    </button>
                                )}
                                <button
                                    type="submit"
                                    name="submitType"
                                    value="saveAndPrint"
                                    className="btn btn-primary"
                                    disabled={isDisable}
                                >
                                    Save & Print
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        if (isFieldsChanges) {
                                            setUnsavedBackUrl(
                                                `${window.location.origin}${salesUrl}`
                                            );
                                            setIsBackButtonClick(false);
                                            handleBack();
                                        } else {
                                            window.location.href = `${window.location.origin}${salesUrl}`;
                                        }
                                    }}
                                >
                                    Back
                                </button>
                                {id && (
                                    <button
                                        onClick={handleDeleteTransaction}
                                        className="btn btn-danger"
                                    >
                                        Delete
                                    </button>
                                )}
                            </div>
                        </Container>
                    </form>
                </div>
            )}
            <Toast />
            {deleteTransaction && (
                <WarningModal
                    show={deleteTransaction}
                    title="Delete!"
                    message="Are you sure want to delete this transaction?"
                    showCancelButton
                    showConfirmButton
                    confirmText="Yes, Delete"
                    cancelText="No, Cancel"
                    handleClose={closeDeleteTransactionModel}
                    handleSubmit={() => handleConfirmDelete()}
                />
            )}

            {showDeleteWarningModel && (
                <DeleteWarningModal
                    show={showDeleteWarningModel?.show}
                    handleClose={() => setShowDeleteWarningModel({ show: false })}
                    transactions={showDeleteWarningModel?.transactions}
                />
            )}
        </>
    );
};
export default AddExpenseCreditDebitTransaction;
