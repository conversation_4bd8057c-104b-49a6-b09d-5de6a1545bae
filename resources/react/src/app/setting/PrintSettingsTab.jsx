import { Row, Col, Container } from "react-bootstrap";
import React, { useContext, useEffect, useState } from "react";
import ReactSelect from "../../components/ui/ReactSelect";
import InvoiceSettingModal from "../modal/Setting/InvoiceSettingModal";
import HeaderSettingModal from "../modal/Setting/HeaderSettingModal";
import TableSettingModal from "../modal/Setting/TableSettingModal";
import FooterSettingModal from "../modal/Setting/FooterSettingModal";
import CustomizeFormatModal from "../modal/Setting/CustomizeFormatModal";
import {
    fetchPdfPreview,
    fetchPrintHeaderSetting,
    fetchPrintSetting,
    getPdfTemplete,
    SavePdfTemplete,
    UpdatePdfTemplete,
} from "../../store/setting/settingSlice";
import { useDispatch, useSelector } from "react-redux";
import useDropdownOption from "../../shared/dropdownList";
import { StateContext } from "../../context/StateContext";
const PrintSettingsTab = () => {
    const dispatch = useDispatch();
    const { setting } = useSelector(selector => selector);
    const { transactionTypeOptions } = useDropdownOption();
    const {
        selectTransactionType,
        setSelectTransactionType,
        selectPdfFormat,
        setSelectPdfFormat,
        selectedIndex,
        setSelectedIndex,
        thermalPageSize,
        setThermalPageSize,
        userPermission
    } = useContext(StateContext);
    const { printSetting, pdfPreview } = setting;

    const [pdfFormatList, setPdfFormatList] = useState([]);
    const [isSetDefault, setIsSetDefault] = useState(false);

    useEffect(() => {
        dispatch(fetchPrintSetting());
    }, [dispatch]);

    useEffect(() => {
        const select_pdf_format = setting?.printSetting?.select_pdf_format;
        const select_pdf_template = setting?.printSetting?.select_pdf_template;
        const pdf_template = setting?.printSetting?.template;
        if (select_pdf_format) {
            const selected_pdf_transaction_type =
                selectTransactionType == 1
                    ? select_pdf_format?.income_transaction
                    : selectTransactionType == 2
                    ? select_pdf_format?.estimate_transaction
                    : selectTransactionType == 3
                    ? select_pdf_format?.delivery_transaction
                    : select_pdf_format?.expense_transaction;
            dispatch(getPdfTemplete(selectTransactionType, selected_pdf_transaction_type));
            dispatch(fetchPdfPreview(selectTransactionType, selected_pdf_transaction_type));
            setSelectPdfFormat(selected_pdf_transaction_type);
        }
        if (select_pdf_format) {
            const selected_pdf_templete =
                selectTransactionType == 1
                    ? select_pdf_format?.income_transaction == 4
                        ? select_pdf_template?.income_landscape_transaction
                        : select_pdf_template?.income_transaction
                    : selectTransactionType == 2
                    ? select_pdf_format?.estimate_transaction == 4
                        ? select_pdf_template?.estimate_landscape_transaction
                        : select_pdf_template?.estimate_transaction
                    : selectTransactionType == 3
                    ? select_pdf_format?.delivery_transaction == 4 || select_pdf_format?.delivery_transaction == 6
                        ? select_pdf_template?.delivery_challan_landscape_transaction
                        : select_pdf_template?.delivery_transaction
                    : select_pdf_format?.expense_transaction;
            setSelectedIndex(selected_pdf_templete);
        }
    }, [setting?.printSetting]);

    useEffect(() => {
        const thermal_print_size = setting?.pdfTemplete?.select_pdf_template?.thermal_transaction
        if(thermal_print_size){
            setThermalPageSize(thermal_print_size)
        }
    }, [setting?.pdfTemplete])

    useEffect(() => {
        const pdfFormat =
            selectTransactionType == 4
                ? "expense_transaction"
                : selectTransactionType == 2
                ? "estimate_transaction"
                : selectTransactionType == 3
                ? "delivery_transaction"
                : "income_transaction";
        const pdfFormatList =
            printSetting?.pdf_format?.[pdfFormat] &&
            Object.entries(printSetting?.pdf_format?.[pdfFormat] || {}).map(([key, value]) => {
                return {
                    label: value,
                    value: key,
                };
            });
        setPdfFormatList(pdfFormatList);
    }, [printSetting, transactionTypeOptions, selectTransactionType]);

    const handleTransactionType = selectedOption => {
        setSelectTransactionType(selectedOption.value);
        setIsSetDefault(true);
        const select_pdf_format = setting?.printSetting?.select_pdf_format;
        const selected_pdf_transaction_type =
            selectedOption.value == 1
                ? select_pdf_format?.income_transaction
                : selectedOption.value == 2
                ? select_pdf_format?.estimate_transaction
                : selectedOption.value == 3
                ? select_pdf_format?.delivery_transaction
                : select_pdf_format?.expense_transaction;
        const transaction_type =
            selectedOption.value == 1
                ? "pdf_format"
                : selectedOption.value == 2
                ? "estimate_pdf_format"
                : selectedOption.value == 3
                ? "delivery_challan_pdf_format"
                : selectedOption.value == 4
                ? "expense_pdf_format"
                : "";
        const response = {
            key: transaction_type,
            format : selected_pdf_transaction_type,
        };
        dispatch(UpdatePdfTemplete(response));
    };

    const handlePdfFormatType = selectedOption => {
        setSelectPdfFormat(selectedOption.value);
        setIsSetDefault(true);
        const transaction_type =
            selectTransactionType == 1
                ? "pdf_format"
                : selectTransactionType == 2
                ? "estimate_pdf_format"
                : selectTransactionType == 3
                ? "delivery_challan_pdf_format"
                : selectTransactionType == 4
                ? "expense_pdf_format"
                : "";
        const response = {
            key: transaction_type,
            format : selectedOption.value,
        };
        dispatch(UpdatePdfTemplete(response, selectTransactionType));
        dispatch(fetchPdfPreview(selectTransactionType, selectedOption.value));
    };

    const onClickPageSize = (e) =>{
        const {value} = e.target;
        setThermalPageSize(value)
        const response = {
            transaction_type: selectTransactionType,
            pdf_format: selectPdfFormat,
            pdf_template: selectedIndex,
            thermal_print_size: value
        };
        dispatch(SavePdfTemplete(response, "", selectTransactionType));
    }

    return (
        <>
            <Container fluid className="print-settings-container mt-2 bg-white scroll-color p-0 h-100">
                <div className="py-6 px-lg-10 px-sm-8 px-6">
                    <Row>
                        <Col xxl={3} xl={3} className="py-8 mb-xl-0 mb-5 border-lg-right">
                            <div className="focus-shadow" style={{ maxWidth: "250px" }}>
                                <ReactSelect
                                    defaultValue={selectTransactionType}
                                    value={selectTransactionType}
                                    onChange={handleTransactionType}
                                    options={transactionTypeOptions}
                                    placeholder="Transaction Type"
                                    isDisabled={!userPermission?.edit_print_setting}
                                />
                            </div>
                            <div className="focus-shadow mt-5" style={{ maxWidth: "250px" }}>
                                <ReactSelect
                                    value={selectPdfFormat}
                                    onChange={handlePdfFormatType}
                                    defaultValue={selectPdfFormat}
                                    options={pdfFormatList}
                                    placeholder="PDF Format"
                                    isDisabled={!userPermission?.edit_print_setting}
                                />
                            </div>
                            {selectPdfFormat == 3 ? (
                                <div className="thermal-page-size-button">
                                    <div className="text-start">
                                        <label class="fw-bold my-3">Page Size :</label>
                                    </div>
                                    <div class="d-flex">
                                        <input
                                            type="radio"
                                            className="btn-check"
                                            id="thermalPrint2"
                                            value="2"
                                            checked={thermalPageSize == 2}
                                            onChange={onClickPageSize}
                                            disabled={!userPermission?.edit_print_setting}
                                        />
                                        <label
                                            id="checkLabel"
                                            class="fs-12 btn fw-bold text-primary border border-3 me-3 "
                                            for="thermalPrint2"
                                        >
                                            2 inch
                                        </label>
                                        <input
                                            type="radio"
                                            className="btn-check"
                                            id="thermalPrint3"
                                            value="3"
                                            onChange={onClickPageSize}
                                            checked={thermalPageSize == 3}
                                            disabled={!userPermission?.edit_print_setting}
                                        />
                                        <label
                                            id="checkLabel"
                                            className="fs-12 btn fw-bold text-primary border border-3 me-3 "
                                            for="thermalPrint3"
                                        >
                                            3 inch
                                        </label>
                                    </div>
                                </div>
                            ) : (
                                ""
                            )}
                            <div className="mt-5">
                                <div style={{ maxWidth: "320px " }}>
                                    <div>
                                        <InvoiceSettingModal pdfType={selectPdfFormat} />
                                        {selectPdfFormat != 3 ? <CustomizeFormatModal /> : ""}
                                        <HeaderSettingModal
                                            type={selectTransactionType}
                                            pdfType={selectPdfFormat}
                                        />
                                        <TableSettingModal
                                            type={selectTransactionType}
                                            pdfType={selectPdfFormat}
                                        />
                                        <FooterSettingModal
                                            type={selectTransactionType}
                                            pdfType={selectPdfFormat}
                                        />
                                    </div>
                                </div>
                            </div>
                        </Col>
                        <Col xxl={9} xl={9} className="pb-2">
                            <div className="pdf-auto">
                                <div className="w-990px overflow-hidden">
                                    <div dangerouslySetInnerHTML={{ __html: pdfPreview }}></div>
                                </div>
                            </div>
                        </Col>
                    </Row>
                </div>
            </Container>
        </>
    );
};

export default PrintSettingsTab;
