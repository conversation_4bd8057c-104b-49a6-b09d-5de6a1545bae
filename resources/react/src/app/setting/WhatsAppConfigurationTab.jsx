import { Row, Col, Form } from "react-bootstrap";
import React, { useContext, useEffect, useState } from "react";
import {
    channgeWhatsappAutoPaymentReminderStatus,
    deleteWhatsappDevice,
    getWhatsappConfiguration,
    storeAndUpdateWhatsappConfiguration,
    whatsappConnectedDevices,
} from "../../store/setting/settingSlice";
import { useDispatch, useSelector } from "react-redux";
import { useFormik } from "formik";
import DeviceModal from "../modal/Setting/DeviceModal";
import WarningModal from "../common/WarningModal";
import QRCodeModal from "../modal/Setting/QRCodeModal";
import { StateContext } from "../../context/StateContext";

const WhatsAppConfigurationTab = () => {
    const { setting } = useSelector(state => state);
    const whatsappConfiguration = setting?.whatsappConfiguration;
    const TempleteList = whatsappConfiguration?.whatsapp_template_settings;
    const DeliveList = whatsappConfiguration?.devices;
    const dispatch = useDispatch();
    const { userPermission } = useContext(StateContext);
    const [addNewDevice, setAddNewDevice] = useState(false);
    const [editDeviceName, setEditDeviceName] = useState("");
    const [editDeviceId, setEditDeviceId] = useState("");
    const [isDeviceDelete, setIsDeviceDelete] = useState(null);
    const [isConnectDevice, setIsConnectDevice] = useState(null);


    const formik = useFormik({
        enableReinitialize: true,
        initialValues: {
            from_name: whatsappConfiguration?.settings?.from_name || "",
            reply_to_email: whatsappConfiguration?.settings?.replay_to_email || "",
            auto_payment_reminder_for_whatsapp:
                whatsappConfiguration?.auto_payment_reminder_for_whatsapp == 1 ? true : false,
            useForEmail: whatsappConfiguration?.useForEmail || [],
            auto_payment: whatsappConfiguration?.auto_payment_reminder_for_whatsapp,
        },
        onSubmit: values => {
            handleSubmit(values);
        },
    });

    const handleAddNewDevice = (data) => {
        if(whatsappConfiguration?.one_za_integration_enabled || !userPermission?.edit_email_whatsapp_setting)return
        setAddNewDevice(data);
    };

    const handleSubmit = async (data) => {
        try {
            const params = {
                from_name: data?.from_name,
                replay_to_email: data?.reply_to_email,
                auto_payment_reminder_for_whatsapp: data?.auto_payment_reminder_for_whatsapp ? 1 : 0,
            };
            await dispatch(storeAndUpdateWhatsappConfiguration(params));
        } catch (error) {
            console.log(error);
        }
    };

    const handleChangeAutoReminder = async (e) => {
        const { value, checked } = e.target;

        const params = {
            type: "auto_payment_reminder_for_whatsapp",
        }
        const response = await dispatch(channgeWhatsappAutoPaymentReminderStatus(params));
        if (response?.success) {
          formik.setFieldValue("auto_payment_reminder_for_whatsapp", checked ? 1 : 0);
        };
    };

    const handleChangeToggle = (e, templete) => {
        const { checked } = e.target;
        dispatch(storeAndUpdateWhatsappConfiguration(templete?.id, checked ? 1 : 0));
    };

    const handleEditDevice = (type, device) => {
        if(!userPermission?.edit_email_whatsapp_setting)return
        setAddNewDevice(type);
        setEditDeviceName(device?.device_name);
        setEditDeviceId(device?.instance_id);
    }

    const hanldeDeleteDevice = (id) => {
        if(!userPermission?.edit_email_whatsapp_setting)return
        setIsDeviceDelete(id)
    }

    const handleConfirmDelete = async () => {
        try {
            const response = await dispatch(deleteWhatsappDevice(isDeviceDelete));

            if (response?.success) {
                setIsDeviceDelete(null);
            }
        } catch (error) {
            console.log(error);
        }
    }

    const handleDeviceSelection = async (deviceId) => {
        const params = { device_id: deviceId };
        await dispatch(whatsappConnectedDevices(params));
    };

    const  handleConnectDevice = (id) => {
        if(!userPermission?.edit_email_whatsapp_setting)return
        setIsConnectDevice(id)
    }

    const handleCloseQrCodeModel = () => {
        setIsConnectDevice(null);
        dispatch(getWhatsappConfiguration());
    }

    return (
        <>
            <Row>
                <Col sm={12} className="pb-5 pt-0 mt-5">
                    <div className="d-flex align-items-center rounded py-3 px-3 bg-warning">
                        <p className="fs-14 fw-bold mb-0">
                            Note :- Whatsapp messages will be sent from the selected device. Please
                            select one device.
                        </p>
                    </div>
                </Col>
            </Row>
            <Row className="row mt-5 mb-3">
                <Col sm={12}>
                    <div className="d-flex flex-wrap justify-content-between align-items-center">
                        <div className="mt-4 d-flex flex-wrap align-items-center col-md-8 col-sm-12 mb-sm-0 mb-3">
                            <label className="fs-6 fw-semibold mb-1">
                                Auto Payment Reminder Configuration
                            </label>
                            <div className="form-check form-switch ms-sm-5 ms-2 mb-0 ps-0">
                                <input
                                    className="form-check-input m-0"
                                    type="checkbox"
                                    role="switch"
                                    id="flexSwitchCheckChecked1"
                                    checked={formik.values.auto_payment_reminder_for_whatsapp == 1}
                                    value={formik.values.auto_payment_reminder_for_whatsapp}
                                    onChange={handleChangeAutoReminder}
                                    disabled={!userPermission?.edit_email_whatsapp_setting}
                                />
                            </div>
                            <a className={!userPermission?.edit_email_whatsapp_setting && "disabled"} href={`${window.location.origin}/company/auto-payment-reminder?type=whatsapp`}>
                                <i className="fas fa-edit text-primary fs-2 ms-sm-5 ms-3"></i>
                            </a>
                        </div>
                        <div className="d-flex flex-wrap col-md-4 col-sm-12 justify-content-md-end justify-content-sm-start">
                            <a
                                className={`${(whatsappConfiguration?.one_za_integration_enabled || !userPermission?.edit_email_whatsapp_setting) && "disabled"} btn btn-primary text-white p-2 ms-3 mb-2 rounded-1 fa-14 align-items-center`}
                                href={`${window.location.origin}/company/setting-purchase-whatsapp-credit`}
                            >
                                <i className="fa-solid fa-coins"></i>
                                Purchase Credits
                            </a>
                            <button
                                type="button"
                                disabled={whatsappConfiguration?.one_za_integration_enabled}
                                onClick={() => handleAddNewDevice(1)}
                                className={`${whatsappConfiguration?.one_za_integration_enabled && "button-disabled "}  btn btn-primary text-white p-2 ms-3 mb-2 rounded-1 fa-14 align-items-center `}
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    shape-rendering="geometricPrecision"
                                    text-rendering="geometricPrecision"
                                    image-rendering="optimizeQuality"
                                    fillRule="evenodd"
                                    clipRule="evenodd"
                                    viewBox="0 0 512 511.999"
                                    height="15px"
                                    width="15px"
                                >
                                    <path
                                        d="M476.335 35.664v.001c47.554 47.552 47.552 125.365.002 172.918l-101.729 101.73c-60.027 60.025-162.073 42.413-194.762-32.45 35.888-31.191 53.387-21.102 87.58-6.638 20.128 8.512 43.74 3.955 60.08-12.387l99.375-99.371c21.49-21.493 21.492-56.662 0-78.155-21.489-21.488-56.677-21.472-78.151 0l-71.278 71.28c-23.583-11.337-50.118-14.697-75.453-10.07a121.476 121.476 0 0118.767-24.207l82.651-82.65c47.554-47.551 125.365-47.555 172.918-.001zM35.664 476.334l.001.001c47.554 47.552 125.365 47.552 172.917 0l85.682-85.682a121.496 121.496 0 0019.325-25.157c-27.876 6.951-57.764 4.015-83.932-8.805l-70.192 70.19c-21.472 21.471-56.658 21.492-78.149 0-21.492-21.491-21.493-56.658 0-78.149l99.375-99.376c20.363-20.363 61.002-26.435 91.717 1.688 29.729-3.133 41.275-8.812 59.742-26.493-39.398-69.476-137.607-80.013-194.757-22.863L35.664 303.417c-47.552 47.553-47.552 125.364 0 172.917z"
                                        fill="#ffffff"
                                    ></path>
                                </svg>
                                Add New Device
                            </button>
                        </div>
                    </div>
                </Col>
            </Row>
            <Form>
                <Row>
                    {DeliveList?.map(device => (
                        <Col md={6}>
                            <div className="d-flex flex-xxl-row flex-column gap-3 my-2 p-4 radio-box">
                                <div className="d-flex flex-xl-row flex-column gap-2 w-100 align-items-xl-center justify-content-xxl-start justify-content-xl-between">
                                    <div className="d-flex  align-items-center">
                                        <input
                                            class="form-check-input wp-radio mb-0 cursor-pointer"
                                            type="radio"
                                            name="flexRadioDefault"
                                            id={`device_${device?.id}`}
                                            checked={device?.id === whatsappConfiguration?.connected_device}
                                            value={device?.id}
                                            onChange={() => handleDeviceSelection(device?.id)}
                                            disabled={!userPermission?.edit_email_whatsapp_setting}
                                        />
                                        <label
                                            htmlFor={`device_${device?.id}`}
                                            className="form-label m-0 text-gray-900 fs-4 ms-3"
                                        >
                                            {device?.device_name}
                                        </label>
                                        <span
                                            onClick={() => handleEditDevice(2, device)}
                                        >
                                            <i className="bi bi-pencil-fill fs-7 align-self-center mt-3 mt-md-0 mx-md-0 mx-md-2 ps-2 cursor-pointer"></i>
                                        </span>
                                    </div>
                                    <div className="text-end">
                                        <span className="ms-3 available-quota text-nowrap">
                                            {device?.credits} credits remaining
                                        </span>
                                    </div>
                                </div>
                                <div className="d-flex justify-content-end align-items-center">
                                    <div
                                        className={`${device?.is_connected ? "bg-success" : "bg-warning"
                                            } text-white p-1 mx-2 rounded-1 cursor-pointer fw-semibold`}
                                        onClick={() => handleConnectDevice(device?.instance_id)}
                                    >
                                        {device?.is_connected ? "Connected" : "Connect"}
                                    </div>
                                    <div onClick={() => hanldeDeleteDevice(device?.id)}
                                        className="btn p-0 d-flex justify-content-center align-items-center btn-active-color-danger text-hover-danger"
                                    >
                                        <span className="svg-icon svg-icon-3 me-0">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24px"
                                                height="24px"
                                                viewBox="0 0 24 24"
                                                version="1.1"
                                            >
                                                <g
                                                    stroke="none"
                                                    stroke-width="1"
                                                    fill="none"
                                                    fillRule="evenodd"
                                                >
                                                    <rect x="0" y="0" width="24" height="24"></rect>
                                                    <path
                                                        d="M6,8 L6,20.5 C6,21.3284271 6.67157288,22 7.5,22 L16.5,22 C17.3284271,22 18,21.3284271 18,20.5 L18,8 L6,8 Z"
                                                        fill="#000000"
                                                        fillRule="nonzero"
                                                    ></path>
                                                    <path
                                                        d="M14,4.5 L14,4 C14,3.44771525 13.5522847,3 13,3 L11,3 C10.4477153,3 10,3.44771525 10,4 L10,4.5 L5.5,4.5 C5.22385763,4.5 5,4.72385763 5,5 L5,5.5 C5,5.77614237 5.22385763,6 5.5,6 L18.5,6 C18.7761424,6 19,5.77614237 19,5.5 L19,5 C19,4.72385763 18.7761424,4.5 18.5,4.5 L14,4.5 Z"
                                                        fill="#000000"
                                                        opacity="0.3"
                                                    ></path>
                                                </g>
                                            </svg>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </Col>
                    ))}
                </Row>
            </Form>
            <Row>
                <Col className="pb-4 pt-8 px-5" sm={12}>
                    <div className="flipped ">
                        <div className="scroll-color overflow-auto">
                            <table className="table table-responsive-sm text-nowrap email-table w-100 flipped-table">
                                <thead>
                                    <tr>
                                        <th className="ps-0">Template Name</th>
                                        <th className="text-center">Auto Send WhatsApp</th>
                                        <th className="text-center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {TempleteList?.map((templete, index) => (
                                        <tr>
                                            <td className="w-50">{templete?.template_name}</td>
                                            <td>
                                                <div className="form-check form-switch mb-0 d-flex justify-content-center ps-0">
                                                    <input
                                                        className="form-check-input w-35px cursor-pointer m-0"
                                                        type="checkbox"
                                                        role="switch"
                                                        id="flexSwitchChecked1"
                                                        checked={templete?.use_for_whatsapp == 1}
                                                        onChange={e =>
                                                            handleChangeToggle(e, templete)
                                                        }
                                                        disabled={whatsappConfiguration?.one_za_integration_enabled || !userPermission?.edit_email_whatsapp_setting}
                                                    />
                                                </div>
                                            </td>
                                            <td>
                                                <a className={`${ (whatsappConfiguration?.one_za_integration_enabled || !userPermission?.edit_email_whatsapp_setting) && "disabled"} d-block text-center`} href={`${window.location.origin}/company/company-email-configuration/${templete?.id}/edit?type=whatsapp`}>
                                                    <i className="fas fa-edit text-primary"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </Col>
            </Row>

            <DeviceModal addNewDevice={addNewDevice} handleClose={() => { setAddNewDevice(0); setEditDeviceName("") }} editDeviceName={editDeviceName} editDeviceId={editDeviceId} />
           {isConnectDevice && <QRCodeModal isConnectDevice={isConnectDevice} handleClose={handleCloseQrCodeModel} />}
            <WarningModal
                show={isDeviceDelete}
                title="Delete!"
                message="Are you sure want to delete this Whatsapp Device ?"
                showCancelButton
                showConfirmButton
                confirmText="Yes, Delete"
                cancelText="No, Cancel"
                handleClose={() => setIsDeviceDelete(false)}
                handleSubmit={handleConfirmDelete}
            />
        </>
    );
};
export default WhatsAppConfigurationTab;
