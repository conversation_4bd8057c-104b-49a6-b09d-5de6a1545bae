import moment from "moment";
import { ADVANCE_PAYMENT_TRANSACTION_TYPE } from "../constants";
import { addItem, getItemModelDetail } from "../store/item/itemSlice";
import { formattedDate } from "./calculation";
import { fetchAdditionalLedgerList, fetchAddlessLedgerList, fetchItemLedgerDetail, fetchPartyList, fetchPaymentLedgerList } from "../store/ledger/ledgerSlice";
import { fetchTcsList, fetchTdsList } from "../store/rate/rateSlice";
import { fetchBankList } from "../store/configuration/configurationSlice";

export const validateAndFormatDate = dateString => {
    // Check if the date is valid in the format "DD-MM-YYYY"
    const isValid = moment(dateString, "DD-MM-YYYY", true).isValid();

    // If valid, return the formatted date
    if (isValid) {
        return moment(dateString, "DD-MM-YYYY").format("DD-MM-YYYY");
    }

    // Otherwise, return today's date in "DD-MM-YYYY" format
    return moment().format("DD-MM-YYYY");
};

export const getCurrencyFormat = number => {
    const settingDecimal = 2;
    let isNegativeAmount = false;

    if (number < 0) {
        number = Math.abs(number);
        isNegativeAmount = true;
    }

    if (number == null) {
        number = Math.abs(number);
        isNegativeAmount = false;
    }

    number = number.toString();

    let decimal = "." + (number.split(".")[1] || "0000");

    let money = parseInt(number.split(".")[0]) || number;
    const length = money.toString().length;
    let delimiter = "";
    money = money.toString().split("").reverse().join("");

    for (let i = 0; i < length; i++) {
        if (i === 3 || (i > 3 && (i - 1) % 2 === 0)) {
            delimiter += ",";
        }
        delimiter += money[i];
    }

    let result = delimiter.split("").reverse().join("");
    decimal = decimal.replace(/0\./, ".");
    decimal = decimal.substring(0, settingDecimal + 1);

    if (decimal !== "0") {
        if (decimal.length !== settingDecimal + 1) {
            decimal += "0".repeat(settingDecimal + 1 - decimal.length);
        }
        result += decimal;
    }

    // Ensure there is a decimal point if not present
    if (!result.includes(".")) {
        result += "." + "0".repeat(settingDecimal);
    }

    if (isNegativeAmount) {
        result = "-" + result;
    }

    return result;
};

export const renderURL = (type, voucher_number, id) => {
    if (type === ADVANCE_PAYMENT_TRANSACTION_TYPE.DELIVERY_CHALLAN) {
        return <a target="_blank" href={`/company/delivery-challan/${id}/edit`}>{voucher_number}</a>
    } else if (type === ADVANCE_PAYMENT_TRANSACTION_TYPE.SALE_RETURN) {
        return <a target="_blank" href={`/company/sale-returns/${id}/edit`}>{voucher_number}</a>
    } else if (type === ADVANCE_PAYMENT_TRANSACTION_TYPE.INCOME_DEBIT_NOTE) {
        return <a target="_blank" href={`/company/income-debit-notes/${id}/edit`}>{voucher_number}</a>
    } else if (type === ADVANCE_PAYMENT_TRANSACTION_TYPE.SALE) {
        return <a target="_blank" href={`/company/sales/${id}/edit`}>{voucher_number}</a>
    } else if (type === ADVANCE_PAYMENT_TRANSACTION_TYPE.INCOME_CREDIT_NOTE) {
        return <a target="_blank" href={`/company/income-credit-notes/${id}/edit`}>{voucher_number}</a>
    } else if (type === ADVANCE_PAYMENT_TRANSACTION_TYPE.PURCHASE) {
        return <a target="_blank" href={`/company/purchases/${id}/edit`}>{voucher_number}</a>
    } else if (type === ADVANCE_PAYMENT_TRANSACTION_TYPE.EXPENSE_CREDIT_NOTE) {
        return <a target="_blank" href={`/company/expense-credit-notes/${id}/edit`}>{voucher_number}</a>
    } else if (type === ADVANCE_PAYMENT_TRANSACTION_TYPE.EXPENSE_DEBIT_NOTE) {
        return <a target="_blank" href={`/company/expense-debit-notes/${id}/edit`}>{voucher_number}</a>
    } else if (type === ADVANCE_PAYMENT_TRANSACTION_TYPE.RECEIPT) {
        return <a target="_blank" href={`/company/transaction-receipt/${id}/edit`}>{voucher_number}</a>
    } else if (type === ADVANCE_PAYMENT_TRANSACTION_TYPE.PURCHASE_RETURN) {
        return <a target="_blank" href={`/company/purchase-returns/${id}/edit`}>{voucher_number}</a>
    } else if (type === ADVANCE_PAYMENT_TRANSACTION_TYPE.PAYMENT) {
        return <a target="_blank" href={`/company/transaction-payment/${id}/edit`}>{voucher_number}</a>
    }
}
export const correctClassificationNatureType = (
    isCheckGstType,
    classificationNature,
    isChangeGSTDetails,
    partyState,
    companyState,
    isNa,
    isExempt,
    isAdditionalChargesNa,
    isAdditionalChargesExempt,
    isPurchase = false
) => {
    if (!isCheckGstType && classificationNature) {
        return classificationNature;
    }
    if (!isChangeGSTDetails) {
        if (isNa && isAdditionalChargesNa) {
            if (partyState == companyState) {
                return isPurchase ? "Intrastate Purchase Nil Rated" : "Intrastate Sales Nil Rated";
            } else {
                return isPurchase ? "Interstate Purchase Nil Rated" : "Interstate Sales Nil Rated";
            }
        } else if (isExempt && isAdditionalChargesExempt) {
            if (partyState == companyState) {
                return isPurchase ? "Intrastate Purchase Exempt" : "Intrastate Sales Exempt";
            } else {
                return isPurchase ? "Interstate Purchase Exempt" : "Interstate Sales Exempt";
            }
        } else {
            if (partyState == companyState) {
                return isPurchase ? "Intrastate Purchase Taxable" : "Intrastate Sales Taxable";
            } else {
                return isPurchase ? "Interstate Purchase Taxable" : "Interstate Sales Taxable";
            }
        }
    } else {
        return classificationNature;
    }
};

export const addItemOnTheFly = async (dispatch, itemOnFly, name, index, items, setItems, setItemIndex) => {
    const defaultGroup = await dispatch(getItemModelDetail());
    const unitOfMeasurement =
        defaultGroup?.unitOfMeasurement &&
        Object.entries(defaultGroup?.unitOfMeasurement || {}).map(([key, value]) => {
            return {
                label: value,
                value: key,
            };
        });
    const defaultPrimaryUnit = unitOfMeasurement?.find(unit => unit.label === "PCS-PIECES");
    const incomeLedgers =
        defaultGroup?.incomeLedgers &&
        Object.entries(defaultGroup?.incomeLedgers || {}).map(([key, value]) => {
            return {
                label: value,
                value: key,
            };
        });
    const expenseLedgers =
        defaultGroup?.expenseLedgers &&
        Object.entries(defaultGroup?.expenseLedgers || {}).map(([key, value]) => {
            return {
                label: value,
                value: key,
            };
        });
    const defaultIncomeOption = incomeLedgers?.find(option => option?.label == "Sale");
    const defaultExpenseOption = expenseLedgers?.find(option => option?.label == "Purchase");
    const data = {
        ...itemOnFly,
        item_name: name,
        group_id: defaultGroup?.defaultSelectedGroup,
        primary_unit_of_measurement: defaultPrimaryUnit?.value,
        income_ledger_id: defaultIncomeOption?.value,
        expense_ledger_id: defaultExpenseOption?.value,
    };
    dispatch(addItem(data, "", items, setItems, index, setItemIndex));
};

export const checkPathName = (route) => {
    return window.location.pathname.includes(route)
}

export const isCurrentFinancialYear = (company) => {
  if (!company?.current_financial_year?.year_start_date || !company?.current_financial_year?.year_end_date) return false;

  const currentDateParts = formattedDate().split("-");
  const currentDate = new Date(
    parseInt(currentDateParts[2]), // Year
    parseInt(currentDateParts[1]) - 1, // Month (0-indexed)
    parseInt(currentDateParts[0]) // Day
  );

  // Use the correct path from the company object to get dates
  const startDate = new Date(company.current_financial_year.year_start_date);
  const endDate = new Date(company.current_financial_year.year_end_date);

  return currentDate >= startDate && currentDate <= endDate;
};

export const getEditLedgerFromList = props => {
    const { dispatch, item_ledger_id, tcs_type, tds_type, is_call_payment_ledger, singleTransaction} = props;
    const payment_ledger_id = [];
    const tcs_ledger_id = [];
    const tds_ledger_id = [];
    const additional_ledger_id = [];
    const add_less_ledger_id = [];
    const bank_id = [];
    const is_exist_bank_id = [];

    if (singleTransaction?.payment_details?.length > 0) {
        singleTransaction?.payment_details.forEach(payment => {
            payment_ledger_id.push(payment.ledger_id);
        });
    }

    if (singleTransaction?.additional_charges?.length > 0) {
        singleTransaction?.additional_charges.forEach(additional => {
            additional_ledger_id.push(additional.ledger_id);
        });
    }

    if (singleTransaction?.add_less?.length > 0) {
        singleTransaction?.add_less.forEach(addless => {
            add_less_ledger_id.push(addless.ledger_id);
        });
    }

    if (singleTransaction?.tcs_tax_id) {
        tcs_ledger_id.push(singleTransaction?.tcs_tax_id);
    }

    if (singleTransaction?.bank_id) {
        bank_id.push(singleTransaction?.bank_id);
        if("bank_id" in singleTransaction) {
            is_exist_bank_id.push(singleTransaction?.bank_id);
        }
    }

    if (singleTransaction?.tds_tax_id) {
        tds_ledger_id.push(singleTransaction?.tds_tax_id);
    }
    //party-ledger
    if (singleTransaction?.party_ledger_id) {
        dispatch(fetchPartyList({ids: [singleTransaction?.party_ledger_id]}));
    } else {
        dispatch(fetchPartyList());
    }

    //item-ledger
    if (item_ledger_id.length > 0) {
        dispatch(fetchItemLedgerDetail({ ids: item_ledger_id }));
    } else {
        dispatch(fetchItemLedgerDetail());
    }
    // payment-ledger
    if (is_call_payment_ledger) {
        if (payment_ledger_id.length > 0) {
            dispatch(fetchPaymentLedgerList({ ids: payment_ledger_id }));
        } else {
            dispatch(fetchPaymentLedgerList());
        }
    }
    // bank id
    if (bank_id) {
        dispatch(fetchBankList({ ids: bank_id }));
    } else if(is_exist_bank_id) {
        dispatch(fetchBankList());
    }
    // tcs-ledger
    if (tcs_ledger_id && tcs_type) {
        dispatch(fetchTcsList({ id: tcs_type, ids: tcs_ledger_id }));
    } else if(tcs_type) {
        dispatch(fetchTcsList({ id: tcs_type }));
    }
    // tds-ledger
    if (tds_ledger_id && tds_type) {
        dispatch(fetchTdsList({ id: tds_type, ids: tds_ledger_id }));
    } else if(tds_type) {
        dispatch(fetchTdsList({ id: tds_type }));
    }
    // additional
    if (additional_ledger_id) {
        dispatch(fetchAdditionalLedgerList({ ids: additional_ledger_id }));
    } else {
        dispatch(fetchAdditionalLedgerList());
    }
    // add-less
    if (add_less_ledger_id) {
        dispatch(fetchAddlessLedgerList({ ids: add_less_ledger_id }));
    } else {
        dispatch(fetchAddlessLedgerList());
    }
};

const paymentPaths = [
    "sale-returns",
    "sale-returns-create",
    "create-sale-return",
    "income-credit-notes",
    "income-credit-notes-create",
    "purchases",
    "purchases-create",
    "expense-credit-notes",
    "expense-credit-notes-create",
  ];

export const isPaymentModeList = paymentPaths.some(path => window.location.pathname.includes(path));
