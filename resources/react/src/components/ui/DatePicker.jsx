import moment from "moment";
import React, { useState, useEffect, useRef, useContext } from "react";
import Flatpickr from "react-flatpickr";
import { useSelector } from "react-redux";
import { StateContext } from "../../context/StateContext";
import { useParams, useLocation } from "react-router-dom";

const Datepicker = ({
    value,
    defaultValue,
    placeholder,
    options = false,
    onChange,
    required,
    isPurchase = false,
    isPaymentDetails = false,
    isTransporter = false,
    domId,
    disabled = false,
}) => {
    const { id } = useParams();
    const location = useLocation();
    const { invoiceDetail, setInvoiceDetail } = useContext(StateContext);
    const [isChangeDate, setIsChangeDate] = useState(false);
    const [isFocused, setIsFocused] = useState(false);
    const [inputValue, setInputValue] = useState("");
    const [minMaxDate, setMinMaxDate] = useState({ minDate: "", maxDate: "" });
    const inputRef = useRef(null);

    const hasValue = inputValue?.length;

    const { company } = useSelector(state => state);

    const defaultOptions = {
        allowInput: true,
        altFormat: "d-m-Y",
        ariaDateFormat: "d-m-Y",
        dateFormat: "d-m-Y",
    };

    const dateOption = {
        ...defaultOptions,
        ...(options ? { ...minMaxDate } : null),
    };
    const handleFocus = () => setIsFocused(true);
    const handleBlur = () => setIsFocused(false);

    const handleDateChange = (selectedDates, dateStr) => {
        setIsChangeDate(true);
        setInputValue(dateStr || "");
        onChange(selectedDates, dateStr);
    };

    function formatDate(dateString) {
        if (dateString) {
            const [year, month, day] = dateString?.split("-");
            return `${day}-${month}-${year}`;
        }
    }

    useEffect(() => {
        const minDate = company?.company?.currentFinancialYear?.yearStartDate;
        const minLockIncomeDate = company?.company?.lock_transaction?.income;
        const minLockExpenseDate = company?.company?.lock_transaction?.expense;
        const maxDate = company?.company?.currentFinancialYear?.yearEndDate;
        const formattedMinDate = formatDate(
            isPurchase ? (minLockExpenseDate || minDate) : minLockIncomeDate ? minLockIncomeDate : minDate
        );
        const formattedMaxDate = formatDate(maxDate);
        const isDuplicate = location.pathname.includes("/duplicate");
        const minDateFormat = new Date(minDate);
        const maxDateFormat = new Date(maxDate);
        const cDate = moment(value).format("YYYY-MM-DD");
        const currentDate = new Date(cDate);
        if (currentDate >= minDateFormat && currentDate <= maxDateFormat) {
            setInputValue(value);
        } else {
            if (isPaymentDetails || isTransporter) {
                setInputValue(value);
            }
            const parseDate = (dateStr) => {
                if (!dateStr || typeof dateStr !== "string" || !dateStr.includes("-")) {
                    return null; // Return null for invalid inputs
                }
                const [day, month, year] = dateStr?.split("-");
                return new Date(`${year}-${month}-${day}`);
            };
            const parsedValue = parseDate(value);
            const parsedMaxDate = new Date(maxDate);
            if(parsedValue > parsedMaxDate && options) {
                setInputValue(maxDate);
                setInvoiceDetail({
                    ...invoiceDetail,
                    invoice_date: value
                });
                return;
            }
            if (value && !isChangeDate) {
                if (options && !id && !isDuplicate) {

                    setInputValue(value);
                    setInvoiceDetail({
                        ...invoiceDetail,
                        ...(isPurchase ? {} : {invoice_date: value}),
                        challan_date: value,
                    });
                }
                else {
                    setInputValue(value);
                }
            }
        }
        setMinMaxDate({ minDate: formattedMinDate, maxDate: formattedMaxDate });
    }, [value, company?.company?.currentFinancialYear]);

    const handleBlurChanges = (e) => {
        handleBlur();
        let typedValue = e.target.value.trim();
        if (typedValue === inputValue) return;
    
        // Support dot (.) and dash (-) separated values
        if (/^\d{1,2}[.\-]\d{1,2}[.\-]\d{4}$/.test(typedValue)) {
            // Normalize to dd-mm-yyyy
            let parts = typedValue.replace(/\./g, "-").split("-");
            let [dd, mm, yyyy] = parts.map(p => p.padStart(2, "0"));

            const formatted = `${dd}-${mm}-${yyyy}`;
            setInputValue(formatted);

            const dateObj = new Date(
                parseInt(yyyy, 10),
                parseInt(mm, 10) - 1,
                parseInt(dd, 10)
            );

            if (!isNaN(dateObj)) {
                onChange([dateObj], formatted);
            } else {
                onChange([], "");
            }
        } else {
            // fallback
            setInputValue(typedValue);
            onChange([], typedValue);
        }
    };

    return (
        <div className="datepicker-container h-40px" style={{ position: "relative" }}>
            <label
                className={`floating-label ${hasValue || isFocused ? "label-up" : ""}`}
                style={{
                    position: "absolute",
                    top: hasValue || isFocused ? "0px" : "16px",
                    left: "12px",
                    fontSize: "14px",
                    color: hasValue || isFocused ? "#4F158C" : "#5E6278",
                    backgroundColor: hasValue || isFocused ? "white" : "transparent",
                    padding: hasValue || isFocused ? "0 4px" : "0",
                    transition: "top 0.2s, font-size 0.2s",
                    zIndex: 1,
                }}
                onClick={() => inputRef.current.focus()}
            >
                {placeholder}
                {required && <span style={{ color: "red", paddingLeft: "4px" }}>*</span>}
            </label>

            <Flatpickr
                key={`${minMaxDate.minDate}-${minMaxDate.maxDate}`}
                id={domId}
                value={inputValue}
                options={dateOption}
                autoComplete="off"
                onChange={handleDateChange}
                className="custom-date-input w-100 h-40px"
                onFocus={handleFocus}
                onBlur={(e) => {
                    handleBlurChanges(e);
                }}
                disabled={disabled}
                render={({ defaultValue, ...props }, ref) => (
                    <input
                        {...props}
                        ref={ref || inputRef}
                        value={inputValue}
                        required={required}
                        disabled={disabled}
                        onChange={e => {
                            setInputValue(e.target.value);
                            if (e.target.value === "") {
                                onChange([], "");
                            }
                        }}
                        style={{
                            paddingLeft: "12px",
                            borderColor: isFocused ? "rgba(79, 21, 140, 0.1)" : "#6f6f6f",
                            fontWeight: hasValue || isFocused ? 500 : 400,
                            boxShadow: isFocused
                                ? `inset 0 1px 1px rgba(0, 0, 0, 0.08), 0 0 0 0.2rem rgba(79, 21, 140, 0.3)`
                                : "none",
                            transition: "border-color 0.2s ease, box-shadow 0.2s ease",
                            outline: "none",
                        }}
                        onFocus={() => {
                            ref.current?._flatpickr.open();
                        }}
                        // onBlur={e => {
                        // handleBlur();
                        // const typedValue = e.target.value;
                        // setInputValue(typedValue);
                        // onChange(e.target.value, "");
                        // }}
                    />
                )}
            />
        </div>
    );
};

export default Datepicker;
