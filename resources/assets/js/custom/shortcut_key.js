hotkeys('alt+i', function () {
    location.href = route('company.sales.create');
})

hotkeys('alt+u', function () {
    location.href = route('company.purchases.create');
})

hotkeys('alt+r', function () {
    location.href = route('company.transaction-receipt.create');
})

hotkeys('alt+o', function () {
    location.href = route('company.transaction-payment.create');
})

hotkeys('alt+j', function () {
    location.href = route('company.transaction-journal.create');
})

hotkeys('alt+g', function () {
    location.href = route('company.income-estimate-quote.create');
})

hotkeys('alt+h', function () {
    location.href = route('company.delivery-challan.create');
})

hotkeys('alt+t', function () {
    location.href = route('company.purchase-orders.create');
})

hotkeys('alt+l', function () {
    if ($('.transaction-form:visible').length) return;
    location.href = route('company.ledgers.create');
})
//     let isModelOpen = false;

//     if ($('#saleTransactionForm').length ||
//         $('#updateSaleTransactionForm').length) {
//         if ($('[name=sales_item_type]:checked').val() == 2 ||
//             $('[name=sales_item_type]:checked').val() == 1) {
//             isModelOpen = true;
//             $('.add-ledger-transaction-model').trigger('click')
//         }
//     }

//     if ($('#saleReturnForm').length || $('#updateSaleReturnForm').length) {
//         if ($('[name=sale_return_item_type]:checked').val() == 2 ||
//             $('[name=sale_return_item_type]:checked').val() == 1) {
//             isModelOpen = true;
//             $('.add-ledger-transaction-model').trigger('click')
//         }
//     }

//     if ($('#incomeDebitNoteTransactionForm').length ||
//         $('#updateIncomeDebitNoteTransactionForm').length) {
//         if ($('[name=dn_item_type]:checked').val() == 2 ||
//             $('[name=dn_item_type]:checked').val() == 1) {
//             isModelOpen = true;
//             $('.add-ledger-transaction-model').trigger('click')
//         }
//     }

//     if ($('#incomeCreditNoteTransactionForm').length ||
//         $('#updateIncomeCreditNoteTransactionForm').length) {
//         if ($('[name=cn_item_type]:checked').val() == 2 ||
//             $('[name=cn_item_type]:checked').val() == 1) {
//             isModelOpen = true;
//             $('.add-ledger-transaction-model').trigger('click')
//         }
//     }

//     if ($('#purchaseTransactionForm').length ||
//         $('#updatePurchaseTransactionForm').length) {
//         if ($('[name=purchase_item_type]:checked').val() == 2 ||
//             $('[name=purchase_item_type]:checked').val() == 1) {
//             isModelOpen = true;
//             $('.add-ledger-transaction-model').trigger('click')
//         }
//     }

//     if ($('#purchaseReturnTransactionForm').length ||
//         $('#updatePurchaseReturnForm').length) {
//         if ($('[name=pr_item_type]:checked').val() == 2 ||
//             $('[name=pr_item_type]:checked').val() == 1) {
//             isModelOpen = true;
//             $('.add-ledger-transaction-model').trigger('click')
//         }
//     }

//     if ($('#expenseDebitNoteTransactionForm').length ||
//         $('#updateExpenseDebitNoteForm').length) {
//         if ($('[name=dn_item_type]:checked').val() == 2 ||
//             $('[name=dn_item_type]:checked').val() == 1) {
//             isModelOpen = true;
//             $('.add-ledger-transaction-model').trigger('click')
//         }
//     }

//     if ($('#expenseCreditNoteTransactionForm').length ||
//         $('#updateExpenseCreditNoteTransactionForm').length) {
//         if ($('[name=expense_cn_item_type]:checked').val() == 2 ||
//             $('[name=expense_cn_item_type]:checked').val() == 1) {
//             isModelOpen = true;
//             $('.add-ledger-transaction-model').trigger('click')
//         }
//     }

//     if ($('#receiptTransactionForm, #paymentTransactionForm').length) {
//         isModelOpen = true;
//         $('.add-ledger-transaction-model').trigger('click')
//     }

//     if ($('#createJournalTransactionForm').length || $('#updateJournalTransactionForm').length) {
//         isModelOpen = true;
//         $('.add-ledger-transaction-model').trigger('click')
//     }

//     if(!isModelOpen) {
//         location.href = route('company.ledgers.create');
//     }
// })

hotkeys('alt+c', function () {
    if ($('.transaction-form:visible').length) return;
    location.href = route('company.item-masters.create');
})
//     let isModelOpen = false;
//     loadInputMaxLength();
//     if ($('#saleTransactionForm').length ||
//         $('#updateSaleTransactionForm').length) {
//         if ($('[name=sales_item_type]:checked').val() == 2) {
//             isModelOpen = true;
//             $('.add-item-transaction-model').trigger('click')
//         }
//         // if ($('[name=sales_item_type]:checked').val() == 1) {
//         //     location.href = route('company.item-masters.create');
//         // }
//     }

//     if ($('#saleReturnForm').length || $('#updateSaleReturnForm').length) {
//         if ($('[name=sale_return_item_type]:checked').val() == 2) {
//             isModelOpen = true;
//             $('.add-item-transaction-model').trigger('click')
//         }
//         // if ($('[name=sale_return_item_type]:checked').val() == 1){
//         //     location.href = route('company.item-masters.create');
//         // }
//     }

//     if ($('#incomeDebitNoteTransactionForm').length ||
//         $('#updateIncomeDebitNoteTransactionForm').length) {
//         if ($('[name=dn_item_type]:checked').val() == 2) {
//             isModelOpen = true;
//             $('.add-item-transaction-model').trigger('click')
//         }
//         // if ($('[name=dn_item_type]:checked').val() == 1){
//         //     location.href = route('company.item-masters.create');
//         // }
//     }

//     if ($('#incomeCreditNoteTransactionForm').length ||
//         $('#updateIncomeCreditNoteTransactionForm').length) {
//         if ($('[name=cn_item_type]:checked').val() == 2) {
//             isModelOpen = true;
//             $('.add-item-transaction-model').trigger('click')
//         }
//         // if ($('[name=cn_item_type]:checked').val() == 1){
//         //     location.href = route('company.item-masters.create');
//         // }
//     }

//     if ($('#purchaseTransactionForm').length ||
//         $('#updatePurchaseTransactionForm').length) {
//         if ($('[name=purchase_item_type]:checked').val() == 2) {
//             isModelOpen = true;
//             $('.add-item-transaction-model').trigger('click')
//         }
//         // if ($('[name=purchase_item_type]:checked').val() == 1){
//         //     location.href = route('company.item-masters.create');
//         // }
//     }

//     if ($('#purchaseReturnTransactionForm').length ||
//         $('#updatePurchaseReturnForm').length) {
//         if ($('[name=pr_item_type]:checked').val() == 2) {
//             isModelOpen = true;
//             $('.add-item-transaction-model').trigger('click')
//         }
//         // if ($('[name=pr_item_type]:checked').val() == 1){
//         //     location.href = route('company.item-masters.create');
//         // }
//     }

//     if ($('#expenseDebitNoteTransactionForm').length ||
//         $('#updateExpenseDebitNoteForm').length) {
//         if ($('[name=dn_item_type]:checked').val() == 2) {
//             isModelOpen = true;
//             $('.add-item-transaction-model').trigger('click')
//         }
//         // if ($('[name=dn_item_type]:checked').val() == 1){
//         //     location.href = route('company.item-masters.create');
//         // }
//     }

//     if ($('#expenseCreditNoteTransactionForm').length ||
//         $('#updateExpenseCreditNoteTransactionForm').length) {
//         if ($('[name=expense_cn_item_type]:checked').val() == 2) {
//             isModelOpen = true;
//             $('.add-item-transaction-model').trigger('click')
//         }
//         // if ($('[name=expense_cn_item_type]:checked').val() == 1){
//         //     location.href = route('company.item-masters.create');
//         // }
//     }

//     if(!isModelOpen) {
//         location.href = route('company.item-masters.create');
//     }
// })

hotkeys('alt+F2', function () {

    if ($('#saleTransactionForm').length ||
        $('#updateSaleTransactionForm').length) {
        $('.date-of-income-gst').focus().val('')
    }

    if ($('#saleReturnForm').length || $('#updateSaleReturnForm').length) {
        $('.date-of-sales-return').focus().val('')
    }

    if ($('#incomeDebitNoteTransactionForm').length ||
        $('#updateIncomeDebitNoteTransactionForm').length) {
        $('.date-of-income-debit-note').focus().val('')
    }

    if ($('#incomeCreditNoteTransactionForm').length ||
        $('#updateIncomeCreditNoteTransactionForm').length) {
        $('.date-of-income-credit-note').focus().val('')
    }

    if ($('#purchaseTransactionForm').length ||
        $('#updatePurchaseTransactionForm').length) {
        $('.date-of-purchase').focus().val('')
    }

    if ($('#purchaseReturnTransactionForm').length ||
        $('#updatePurchaseReturnForm').length) {
        $('.date-of-purchase-return').focus().val('')
    }

    if ($('#expenseDebitNoteTransactionForm').length ||
        $('#updateExpenseDebitNoteForm').length) {
        $('.date-of-debit-note').focus().val('')
    }

    if ($('#expenseCreditNoteTransactionForm').length ||
        $('#updateExpenseCreditNoteTransactionForm').length) {
        $('.date-of-expense-credit-note').focus().val('')
    }

    if ($('#receiptTransactionForm').length) {
        $('.date-of-receipt-transaction').focus().val('')
    }

    if ($('#paymentTransactionForm').length) {
        $('.date-of-receipt-transaction').focus().val('')
    }

    if ($('#createJournalTransactionForm').length ||
        $('#updateJournalTransactionForm').length) {
        $('.date-of-journal-transaction').focus().val('')
    }
})

hotkeys('alt+f12', function () {
    if ($('#saleTransactionForm').length ||
        $('#updateSaleTransactionForm').length) {
        $('#incomeConfiguration').trigger('click')
    }

    if ($('#saleReturnForm').length || $('#updateSaleReturnForm').length) {
        $('.sale-return-configuration-modal').trigger('click')
    }

    if ($('#incomeDebitNoteTransactionForm').length ||
        $('#updateIncomeDebitNoteTransactionForm').length) {
        $('.income-debit-note-configuration-modal').trigger('click')
    }

    if ($('#incomeCreditNoteTransactionForm').length ||
        $('#updateIncomeCreditNoteTransactionForm').length) {
        $('.income-credit-note-configuration-modal').trigger('click')
    }

    if ($('#purchaseTransactionForm').length ||
        $('#updatePurchaseTransactionForm').length) {
        $('.purchase-configuration-modal').trigger('click')
    }

    if ($('#purchaseReturnTransactionForm').length ||
        $('#updatePurchaseReturnForm').length) {
        $('.purchase-return-configuration-modal').trigger('click')
    }

    if ($('#expenseDebitNoteTransactionForm').length ||
        $('#updateExpenseDebitNoteForm').length) {
        $('.expense-debit-note-configuration-modal').trigger('click')
    }

    if ($('#expenseCreditNoteTransactionForm').length ||
        $('#updateExpenseCreditNoteTransactionForm').length) {
        $('.expense-credit-note-configuration-modal').trigger('click')
    }

    if ($('#receiptTransactionForm').length ||
        $('#updateReceiptTransactionForm').length) {
        $('.receipt-transaction-configuration-modal').trigger('click')
    }

    if ($('#paymentTransactionForm').length ||
        $('#updatePaymentTransactionForm').length) {
        $('.payment-transaction-configuration-modal').trigger('click')
    }

    if ($('#createJournalTransactionForm').length ||
        $('#updateJournalTransactionForm').length) {
        $('.journal-transaction-configuration-modal').trigger('click')
    }

    if ($('#outstandingPayableReportDataTable').length ||
        $('#outstandingReceivableReportDataTable').length) {
        $('.show-narration-outstanding-report').trigger('click')
    }

})

hotkeys('alt+p', function () {

    if ($('#saleTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#saleCustomerName').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#saleTransactionForm').trigger('submit')
        setTimeout(function () {
            let downloadPdfId = $('.view-sale-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href', route('company.sales-pdf-download',
                { salePdfDownloadId: downloadPdfId, isView: 1 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }
    if ($('#updateSaleTransactionForm').length) {
        let url = $('#updateSaleTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateSaleTransactionForm').trigger('submit')
        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href', route('company.sales-pdf-download',
                { salePdfDownloadId: updateId, isView: 1 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }

    if ($('#saleReturnForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#salesReturnCustomerName').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#saleReturnForm').trigger('submit')
        setTimeout(function () {
            let downloadPdfId = $('.sale-return-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.sale-return-pdf-download',
                    { saleReturnPdfDownloadId: downloadPdfId, isView: 1 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }
    if ($('#updateSaleReturnForm').length) {
        let url = $('#updateSaleReturnForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateSaleReturnForm').trigger('submit')

        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.sale-return-pdf-download',
                    { saleReturnPdfDownloadId: updateId, isView: 1 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }

    if ($('#incomeDebitNoteTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#incomeDebitNotesCustomerName').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#incomeDebitNoteTransactionForm').trigger('submit')
        setTimeout(function () {
            let downloadPdfId = $('.income-dn-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href', route('company.income-dn-pdf-download',
                { incomeDnDownloadPdfId: downloadPdfId, isView: 1 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }
    if ($('#updateIncomeDebitNoteTransactionForm').length) {
        let url = $('#updateIncomeDebitNoteTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateIncomeDebitNoteTransactionForm').trigger('submit')

        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href', route('company.income-dn-pdf-download',
                { incomeDnDownloadPdfId: updateId, isView: 1 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }

    if ($('#incomeCreditNoteTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#incomeCreditNotesCustomerName').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#incomeCreditNoteTransactionForm').trigger('submit')
        setTimeout(function () {
            let downloadPdfId = $('.income-cn-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href', route('company.income-cn-pdf-download',
                { incomeCnDownloadPdfId: downloadPdfId, isView: 1 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }
    if ($('#updateIncomeCreditNoteTransactionForm').length) {
        let url = $('#updateIncomeCreditNoteTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateIncomeCreditNoteTransactionForm').trigger('submit')

        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href', route('company.income-cn-pdf-download',
                { incomeCnDownloadPdfId: updateId, isView: 1 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }

    if ($('#receiptTransactionForm').length) {
        if ($('.received-amount-total').val() == '') {
            displayErrorMessage('Amount field required')
        }
        $('#receiptTransactionForm').trigger('submit')
        setTimeout(function () {
            let downloadPdfId = $('.view-receipt-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.transaction-receipt-pdf-download',
                    { receiptPdfDownloadId: downloadPdfId, isView: 1 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }
    if ($('#updateReceiptTransactionForm').length) {
        let url = $('#updateReceiptTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateReceiptTransactionForm').trigger('submit')
        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.transaction-receipt-pdf-download',
                    { receiptPdfDownloadId: updateId, isView: 1 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }

    if ($('#paymentTransactionForm').length) {
        $('#paymentTransactionForm').trigger('submit')
        setTimeout(function () {
            let pdfId = $('.view-payment-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.transaction-payment-pdf-download',
                    { paymentPdfDownloadId: pdfId, isView: 1 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }
    if ($('#updatePaymentTransactionForm').length) {
        let url = $('#updatePaymentTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updatePaymentTransactionForm').trigger('submit')
        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.transaction-payment-pdf-download',
                    { paymentPdfDownloadId: updateId, isView: 1 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }

    if ($('#createJournalTransactionForm').length) {
        $('#createJournalTransactionForm').trigger('submit')
        setTimeout(function () {
            let pdfId = $('.view-journal-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.transaction-journal-pdf-download',
                    { journalPdfDownloadId: pdfId, isView: 1 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }
    if ($('#updateJournalTransactionForm').length) {
        let url = $('#updateJournalTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateJournalTransactionForm').trigger('submit')
        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.transaction-journal-pdf-download',
                    { journalPdfDownloadId: updateId, isView: 1 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }

    if ($("#ledgerReportIndex").length) {
        let ledgerReportForm = $("#downloadLedgerReportPDFForm");

        if (ledgerReportForm.length) {
            ledgerReportForm.append('<input type="hidden" name="is_view" value="true">');
            ledgerReportForm.submit();
        }
    }
    if ($("#stockReportIndex").length) {
        let stockReportForm = $("#downloadStockReportPDFForm");

        if (stockReportForm.length) {
            stockReportForm.append('<input type="hidden" name="is_view" value="true">');
            stockReportForm.submit();
        }
    }

    if ($("#outstandingReportIndex").length) {
        openReportWindow(".shortcut-outstanding-pdf-export", "&is_view=true");
    }
    if ($("#ageingReportIndex").length) {
        openReportWindow(".shortcut-ageing-pdf-export", "&is_view=true");
    }
    if ($("#saleReportIndex").length) {
        openReportWindow(".shortcut-sale-report-pdf-export", "&is_view=true");
    }
    if ($("#purchaseReportIndex").length) {
        openReportWindow(".shortcut-purchase-report-pdf-export", "&is_view=true");
    }
    if ($("#dayBookReportIndex").length) {
        openReportWindow(".shortcut-daybook-pdf-export", "&is_view=true");
    }
    if ($("#brokerReportIndex").length) {
        openReportWindow(".shortcut-broker-pdf-export", "&is_view=true");
    }
    if ($("#trialBalanceReportIndex").length) {
        openReportWindow(".shortcut-trail-balance-pdf-export", "&is_view=true");
    }
    if ($("#tradingProfitLossReportIndex").length) {
        openReportWindow(".shortcut-profit-loss-pdf-export", "&is_view=true");
    }
    if ($("#balanceSheetReportIndex").length) {
        openReportWindow(".shortcut-balance-sheet-pdf-export", "&is_view=true");
    }
    if ($("#cashFlowReportIndex").length) {
        openReportWindow(".shortcut-cash-flow-pdf-export", "&is_view=true");
    }
    if ($("#gstr1ReportIndex").length) {
        openReportWindow(".gstr-1-summary-pdf", "&is_view=true");
    }
    if ($("#gstr3bReportIndex").length) {
        openReportWindow(".gstr-3b-summary-pdf", "&is_view=true");
    }
    if ($("#inputTaxReportIndex").length) {
        openReportWindow(".shortcut-input-tax-pdf-export", "&is_view=true");
    }
    if ($("#outputTaxReportIndex").length) {
        openReportWindow(".shortcut-output-tax-pdf-export", "&is_view=true");
    }
    if ($("#hsnOutwardTaxReportIndex").length) {
        openReportWindow(".shortcut-hsn-outward-pdf-export", "&is_view=true");
    }
    if ($("#hsnInwardTaxReportIndex").length) {
        openReportWindow(".shortcut-hsn-inward-pdf-export", "&is_view=true");
    }
    if ($("#tdsLiabilityReportIndex").length) {
        openReportWindow(".shortcut-tds-liability-pdf-export", "&is_view=true");
    }
    if ($("#tdsReturnReportIndex").length) {
        openReportWindow(".shortcut-tds-return-pdf-export", "&is_view=true");
    }
    if ($("#tcsLiabilityReportIndex").length) {
        openReportWindow(".shortcut-tcs-liability-pdf-export", "&is_view=true");
    }
    if ($("#tcsReturnReportIndex").length) {
        openReportWindow(".shortcut-tcs-return-pdf-export", "&is_view=true");
    }
})

function openReportWindow(selector, parameter) {
    if (parameter != null) {
        if ($(selector).length) {
            let url = $(selector).attr("href");
            window.open(url + parameter, "_blank");
        }
    } else {
        if ($(selector).length) {
            let url = $(selector).attr("href");
            window.open(url, "_blank");
        }
    }
}

hotkeys('alt+s', function () {
    if ($('#saleTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#saleCustomerName').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#saleTransactionForm').trigger('submit')
    }
    if ($('#updateSaleTransactionForm').length) {
        let url = $('#updateSaleTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateSaleTransactionForm').trigger('submit')
    }

    if ($('#saleReturnForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#salesReturnCustomerName').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#saleReturnForm').trigger('submit')
    }
    if ($('#updateSaleReturnForm').length) {
        let url = $('#updateSaleReturnForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateSaleReturnForm').trigger('submit')
    }

    if ($('#incomeDebitNoteTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#incomeDebitNotesCustomerName').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#incomeDebitNoteTransactionForm').trigger('submit')
    }
    if ($('#updateIncomeDebitNoteTransactionForm').length) {
        let url = $('#updateIncomeDebitNoteTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateIncomeDebitNoteTransactionForm').trigger('submit')
    }

    if ($('#incomeCreditNoteTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#incomeCreditNotesCustomerName').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#incomeCreditNoteTransactionForm').trigger('submit')
    }
    if ($('#updateIncomeCreditNoteTransactionForm').length) {
        let url = $('#updateIncomeCreditNoteTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateIncomeCreditNoteTransactionForm').trigger('submit')
    }

    if ($('#purchaseTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#purchaseSupplierName').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#purchaseTransactionForm').trigger('submit')
    }
    if ($('#updatePurchaseTransactionForm').length) {
        let url = $('#updatePurchaseTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updatePurchaseTransactionForm').trigger('submit')
    }

    if ($('#purchaseReturnTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#purchaseReturnSupplier').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#purchaseReturnTransactionForm').trigger('submit')
    }
    if ($('#updatePurchaseReturnForm').length) {
        let url = $('#updatePurchaseReturnForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updatePurchaseReturnForm').trigger('submit')
    }

    if ($('#expenseDebitNoteTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#purchaseReturnSupplier').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#expenseDebitNoteTransactionForm').trigger('submit')
    }
    if ($('#updateExpenseDebitNoteForm').length) {
        let url = $('#updateExpenseDebitNoteForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateExpenseDebitNoteForm').trigger('submit')
    }

    if ($('#expenseCreditNoteTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#expenseReturnSupplier').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#expenseCreditNoteTransactionForm').trigger('submit')
    }
    if ($('#updateExpenseCreditNoteTransactionForm').length) {
        let url = $('#updateExpenseCreditNoteTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateExpenseCreditNoteTransactionForm').trigger('submit')
    }

    if ($('#receiptTransactionForm').length) {
        $('#receiptTransactionForm').trigger('submit')
    }
    if ($('#updateReceiptTransactionForm').length) {
        $('#updateReceiptTransactionForm').trigger('submit')
    }

    if ($('#paymentTransactionForm').length) {
        $('#paymentTransactionForm').trigger('submit')
    }
    if ($('#updatePaymentTransactionForm').length) {
        $('#updatePaymentTransactionForm').trigger('submit')
    }

    if ($('#createJournalTransactionForm').length) {
        $('#createJournalTransactionForm').trigger('submit')
    }
    if ($('#updateJournalTransactionForm').length) {
        $('#updateJournalTransactionForm').trigger('submit')
    }

})

hotkeys("alt+n", function () {
    if ($("#saleTransactionForm").length) {
        if (
            $("#paymentMethod").val() == "" &&
            $("#paymentModCash").is(":checked")
        ) {
            displayErrorMessage("Payment ledger field required");
        }
        if ($("#saleCustomerName").val() == "") {
            displayErrorMessage("Party Name field required");
        }
        $(".sale-btn-value").val(2);
        $("#saleTransactionForm").trigger("submit");
    }

    if ($("#saleReturnForm").length) {
        if (
            $("#paymentMethod").val() == "" &&
            $("#paymentModCash").is(":checked")
        ) {
            displayErrorMessage("Payment ledger field required");
        }
        if ($("#salesReturnCustomerName").val() == "") {
            displayErrorMessage("Party Name field required");
        }
        $(".sale-return-btn-value").val(2);
        $("#saleReturnForm").trigger("submit");
    }

    if ($("#incomeDebitNoteTransactionForm").length) {
        if (
            $("#paymentMethod").val() == "" &&
            $("#paymentModCash").is(":checked")
        ) {
            displayErrorMessage("Payment ledger field required");
        }
        if ($("#incomeDebitNotesCustomerName").val() == "") {
            displayErrorMessage("Party Name field required");
        }
        $(".income-debit-note-btn-value").val(2);
        $("#incomeDebitNoteTransactionForm").trigger("submit");
    }

    if ($("#incomeCreditNoteTransactionForm").length) {
        if (
            $("#paymentMethod").val() == "" &&
            $("#paymentModCash").is(":checked")
        ) {
            displayErrorMessage("Payment ledger field required");
        }
        if ($("#incomeCreditNotesCustomerName").val() == "") {
            displayErrorMessage("Party Name field required");
        }
        $(".income-credit-note-btn-value").val(2);
        $("#incomeCreditNoteTransactionForm").trigger("submit");
    }

    if ($("#purchaseTransactionForm").length) {
        if (
            $("#paymentMethod").val() == "" &&
            $("#paymentModCash").is(":checked")
        ) {
            displayErrorMessage("Payment ledger field required");
        }
        if ($("#purchaseSupplierName").val() == "") {
            displayErrorMessage("Party Name field required");
        }
        $(".purchase-btn-value").val(2);
        $("#purchaseTransactionForm").trigger("submit");
    }

    if ($("#purchaseReturnTransactionForm").length) {
        if (
            $("#paymentMethod").val() == "" &&
            $("#paymentModCash").is(":checked")
        ) {
            displayErrorMessage("Payment ledger field required");
        }
        if ($("#purchaseReturnSupplier").val() == "") {
            displayErrorMessage("Party Name field required");
        }
        $(".purchase-return-btn-value").val(2);
        $("#purchaseReturnTransactionForm").trigger("submit");
    }

    if ($("#expenseDebitNoteTransactionForm").length) {
        if (
            $("#paymentMethod").val() == "" &&
            $("#paymentModCash").is(":checked")
        ) {
            displayErrorMessage("Payment ledger field required");
        }
        if ($("#purchaseReturnSupplier").val() == "") {
            displayErrorMessage("Party Name field required");
        }
        $(".expense-debit-note-btn-value").val(2);
        $("#expenseDebitNoteTransactionForm").trigger("submit");
    }

    if ($("#expenseCreditNoteTransactionForm").length) {
        if (
            $("#paymentMethod").val() == "" &&
            $("#paymentModCash").is(":checked")
        ) {
            displayErrorMessage("Payment ledger field required");
        }
        if ($("#expenseReturnSupplier").val() == "") {
            displayErrorMessage("Party Name field required");
        }
        $(".expense-credit-note-btn-value").val(2);
        $("#expenseCreditNoteTransactionForm").trigger("submit");
    }

    if ($("#receiptTransactionForm").length) {
        $(".receipt-transaction-btn-value").val(2);
        $("#receiptTransactionForm").trigger("submit");
    }

    if ($("#paymentTransactionForm").length) {
        $(".transaction-payment-btn-value").val(2);
        $("#paymentTransactionForm").trigger("submit");
    }

    if ($("#createJournalTransactionForm").length) {
        $(".journal-btn-value").val(2);
        $("#createJournalTransactionForm").trigger("submit");
    }
});

hotkeys('alt+d', function () {
    if ($('#saleTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#saleCustomerName').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#saleTransactionForm').trigger('submit')
        setTimeout(function () {
            let downloadPdfId = $('.view-sale-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href', route('company.sales-pdf-download',
                { salePdfDownloadId: downloadPdfId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }
    if ($('#updateSaleTransactionForm').length) {
        let url = $('#updateSaleTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateSaleTransactionForm').trigger('submit')

        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href', route('company.sales-pdf-download',
                { salePdfDownloadId: updateId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }

    if ($('#saleReturnForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#salesReturnCustomerName').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#saleReturnForm').trigger('submit')
        setTimeout(function () {
            let downloadPdfId = $('.sale-return-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.sale-return-pdf-download',
                    { saleReturnPdfDownloadId: downloadPdfId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }
    if ($('#updateSaleReturnForm').length) {
        let url = $('#updateSaleReturnForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateSaleReturnForm').trigger('submit')

        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.sale-return-pdf-download',
                    { saleReturnPdfDownloadId: updateId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }

    if ($('#incomeDebitNoteTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#incomeDebitNotesCustomerName').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#incomeDebitNoteTransactionForm').trigger('submit')
        setTimeout(function () {
            let downloadPdfId = $('.income-dn-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href', route('company.income-dn-pdf-download',
                { incomeDnDownloadPdfId: downloadPdfId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }
    if ($('#updateIncomeDebitNoteTransactionForm').length) {
        let url = $('#updateIncomeDebitNoteTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateIncomeDebitNoteTransactionForm').trigger('submit')

        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href', route('company.income-dn-pdf-download',
                { incomeDnDownloadPdfId: updateId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }

    if ($('#incomeCreditNoteTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#incomeCreditNotesCustomerName').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#incomeCreditNoteTransactionForm').trigger('submit')

        setTimeout(function () {
            let downloadPdfId = $('.income-cn-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href', route('company.income-cn-pdf-download',
                { incomeCnDownloadPdfId: downloadPdfId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }
    if ($('#updateIncomeCreditNoteTransactionForm').length) {
        let url = $('#updateIncomeCreditNoteTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateIncomeCreditNoteTransactionForm').trigger('submit')

        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href', route('company.income-cn-pdf-download',
                { incomeCnDownloadPdfId: updateId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }

    if ($('#purchaseTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#purchaseSupplierName').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#purchaseTransactionForm').trigger('submit')
        setTimeout(function () {
            let downloadPdfId = $('.view-purchase-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href', route('company.purchase-pdf-download',
                { purchasePdfDownloadId: downloadPdfId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 11000)
    }
    if ($('#updatePurchaseTransactionForm').length) {
        let url = $('#updatePurchaseTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updatePurchaseTransactionForm').trigger('submit')

        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href', route('company.purchase-pdf-download',
                { purchasePdfDownloadId: updateId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 11000)
    }

    if ($('#purchaseReturnTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#purchaseReturnSupplier').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#purchaseReturnTransactionForm').trigger('submit')
        setTimeout(function () {
            let downloadPdfId = $('.view-purchase-return-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.purchase-return-pdf-download',
                    { purchaseReturnPdfDownloadId: downloadPdfId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 11000)
    }
    if ($('#updatePurchaseReturnForm').length) {
        let url = $('#updatePurchaseReturnForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updatePurchaseReturnForm').trigger('submit')

        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.purchase-return-pdf-download',
                    { purchaseReturnPdfDownloadId: updateId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 11000)
    }

    if ($('#expenseDebitNoteTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#purchaseReturnSupplier').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#expenseDebitNoteTransactionForm').trigger('submit')
        setTimeout(function () {
            let downloadPdfId = $('.expense-dn-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.expense-dn-pdf-download',
                    { expenseDnDownloadPdfId: downloadPdfId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 11000)
    }
    if ($('#updateExpenseDebitNoteForm').length) {
        let url = $('#updateExpenseDebitNoteForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateExpenseDebitNoteForm').trigger('submit')

        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.expense-dn-pdf-download',
                    { expenseDnDownloadPdfId: updateId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 11000)
    }

    if ($('#expenseCreditNoteTransactionForm').length) {
        if ($('#paymentMethod').val() == '' &&
            $('#paymentModCash').is(':checked')) {
            displayErrorMessage('Payment ledger field required')
        }
        if ($('#expenseReturnSupplier').val() == '') {
            displayErrorMessage('Party Name field required')
        }
        $('#expenseCreditNoteTransactionForm').trigger('submit')

        setTimeout(function () {
            let downloadPdfId = $('.expense-cn-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.expense-cn-pdf-download',
                    { expenseCnDownloadPdfId: downloadPdfId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 11000)
    }
    if ($('#updateExpenseCreditNoteTransactionForm').length) {
        let url = $('#updateExpenseCreditNoteTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateExpenseCreditNoteTransactionForm').trigger('submit')

        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.expense-cn-pdf-download',
                    { expenseCnDownloadPdfId: updateId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 11000)
    }

    if ($('#receiptTransactionForm').length) {
        $('#receiptTransactionForm').trigger('submit')
        setTimeout(function () {
            let downloadPdfId = $('.view-receipt-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.transaction-receipt-pdf-download',
                    { receiptPdfDownloadId: downloadPdfId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }
    if ($('#updateReceiptTransactionForm').length) {
        let url = $('#updateReceiptTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateReceiptTransactionForm').trigger('submit')
        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.transaction-receipt-pdf-download',
                    { receiptPdfDownloadId: updateId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }

    if ($('#paymentTransactionForm').length) {
        $('#paymentTransactionForm').trigger('submit')
        setTimeout(function () {
            let pdfId = $('.view-payment-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.transaction-payment-pdf-download',
                    { paymentPdfDownloadId: pdfId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }
    if ($('#updatePaymentTransactionForm').length) {
        let url = $('#updatePaymentTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updatePaymentTransactionForm').trigger('submit')
        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.transaction-payment-pdf-download',
                    { paymentPdfDownloadId: updateId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }

    if ($('#createJournalTransactionForm').length) {
        $('#createJournalTransactionForm').trigger('submit')
        setTimeout(function () {
            let pdfId = $('.view-journal-pdf-preview-modal').
                eq(0).
                attr('data-id')
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.transaction-journal-pdf-download',
                    { journalPdfDownloadId: pdfId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }
    if ($('#updateJournalTransactionForm').length) {
        let url = $('#updateJournalTransactionForm').attr('action')
        let updateId = url.substring(url.lastIndexOf('/') + 1)
        $('#updateJournalTransactionForm').trigger('submit')
        setTimeout(function () {
            let element = document.createElement('a')
            element.setAttribute('href',
                route('company.transaction-journal-pdf-download',
                    { journalPdfDownloadId: updateId, isView: 0 }))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
        }, 3000)
    }

    if ($('#ledgerReportIndex').length) {
        $('#downloadLedgerReportPDFForm').trigger('submit')
    }
    if ($("#outstandingReportIndex").length) {
        openReportWindow(".shortcut-outstanding-pdf-export");
    }
    if ($('#ageingReportIndex').length) {
        openReportWindow(".shortcut-ageing-pdf-export");
    }
    if ($("#saleReportIndex").length) {
        openReportWindow(".shortcut-sale-report-pdf-export");
    }
    if ($("#purchaseReportIndex").length) {
        openReportWindow(".shortcut-purchase-report-pdf-export");
    }
    if ($("#dayBookReportIndex").length) {
        openReportWindow(".shortcut-daybook-pdf-export");
    }
    if ($('#stockReportIndex').length) {
        $('#downloadStockReportPDFForm').trigger('submit')
    }
    if ($('#itemStockReportIndex').length) {
        $('#downloadItemStockReportPDFForm').trigger('submit')
    }
    if ($("#brokerReportIndex").length) {
        openReportWindow(".shortcut-broker-pdf-export");
    }
    if ($("#trialBalanceReportIndex").length) {
        openReportWindow(".shortcut-trail-balance-pdf-export");
    }
    if ($("#tradingProfitLossReportIndex").length) {
        openReportWindow(".shortcut-profit-loss-pdf-export");
    }
    if ($('#balanceSheetReportIndex').length) {
        openReportWindow(".shortcut-balance-sheet-pdf-export");
    }
    if ($("#cashFlowReportIndex").length) {
        openReportWindow(".shortcut-cash-flow-pdf-export");
    }
    if ($("#gstr1ReportIndex").length) {
        openReportWindow(".gstr-1-summary-pdf");
    }
    if ($("#gstr3bReportIndex").length) {
        openReportWindow(".gstr-3b-summary-pdf");
    }
    if ($("#inputTaxReportIndex").length) {
        openReportWindow(".shortcut-input-tax-pdf-export");
    }
    if ($("#outputTaxReportIndex").length) {
        openReportWindow(".shortcut-output-tax-pdf-export");
    }
    if ($("#hsnOutwardTaxReportIndex").length) {
        openReportWindow(".shortcut-hsn-outward-pdf-export");
    }
    if ($("#hsnInwardTaxReportIndex").length) {
        openReportWindow(".shortcut-hsn-inward-pdf-export");
    }
    if ($("#tdsLiabilityReportIndex").length) {
        openReportWindow(".shortcut-tds-liability-pdf-export");
    }
    if ($("#tdsReturnReportIndex").length) {
        openReportWindow(".shortcut-tds-return-pdf-export");
    }
    if ($("#tcsLiabilityReportIndex").length) {
        openReportWindow(".shortcut-tcs-liability-pdf-export");
    }
    if ($("#tcsReturnReportIndex").length) {
        openReportWindow(".shortcut-tcs-return-pdf-export");
    }
    if ($("#billWiseProfitReportIndex").length) {
        openReportWindow(".shortcut-broker-pdf-export");
    }
    if ($("#itemWiseProfitReportIndex").length) {
        openReportWindow(".shortcut-item-wise-profit-pdf-export");
    }
    if ($("#PartyWiseSalesPurchaseReportIndex").length) {
        openReportWindow(".shortcut-party-wise-sales-purchase-pdf-export");
    }
    if ($("#supplierSummaryReportIndex").length){
        openReportWindow(".shortcut-supplier-summary-pdf-export")
    }
})

hotkeys('shift+1', function () {
    location.href = route('company.ledger-report');
})

hotkeys('shift+2', function () {
    let date = companyFilter.trading_profit_loss_report_date ? companyFilter.trading_profit_loss_report_date.split(' - ') : '';
    let hideZeroBalance = $("#hideZeroBalanceTradingProfitLoss").is(":checked");
    let startDate = date[0] ?? moment().startOf('month').format('YYYY-MM-DD')
    let endDate = date[1] ?? moment().endOf('month').format('YYYY-MM-DD')
    location.href = route('company.trading-profit-loss', {
        'start_date': startDate,
        'end_date': endDate,
        // 'stock_method': '',
        'details': false,
        'hideZeroBalance': hideZeroBalance,
    });
})

hotkeys('shift+3', function () {
    let date = companyFilter.balance_sheet_date ?? moment().format('DD-MM-YYYY');
    location.href = route('company.balance-sheet-report', {
        'date': moment(reformatDateString(date)).format('YYYY-MM-DD'),
        'details': false,
        'hideZeroBalance': 0,
    });
})

hotkeys('shift+4', function () {
    location.href = route('company.trial-balance-report');
})

hotkeys('shift+5', function () {
    location.href = route('company.cash-flow-report');
})

hotkeys('shift+6', function () {
    location.href = route('company.report-stock', { 'item_type': 1 });
})

hotkeys('shift+7', function () {
    let date = companyFilter.gstr_3b_report_date ? companyFilter.gstr_3b_report_date.split(' - ') : '';
    let startDate = date[0] ?? moment().startOf('month').format('YYYY-MM-DD')
    let endDate = date[1] ?? moment().endOf('month').format('YYYY-MM-DD')
    location.href = route('company.reports.gstr-3b-summary', {
        'start_date': startDate,
        'end_date': endDate,
    });
})

hotkeys('shift+8', function () {
    let date = companyFilter.gstr_1_report_date ? companyFilter.gstr_1_report_date.split(' - ') : '';
    let startDate = date[0] ?? moment().startOf('month').format('YYYY-MM-DD')
    let endDate = date[1] ?? moment().endOf('month').format('YYYY-MM-DD')
            var currentDate = moment();
    var financialYear = companyFilter.current_financial_year ? companyFilter.current_financial_year.split(' - ') : [];

        var fyStartYear = parseInt(financialYear[0].trim());
        var fyEndYear = parseInt(financialYear[1].trim());

        // Determine current financial year (April to March)
        var currentFyStart = moment(currentDate.year() + "-04-01");
        var currentFyEnd = moment((currentDate.year() + 1) + "-03-31");

        if (currentDate.month() < 3) { // Jan=0, Feb=1, Mar=2
            currentFyStart = moment((currentDate.year() - 1) + "-04-01");
            currentFyEnd = moment(currentDate.year() + "-03-31");
        }

    var sDate, eDate;

    if (fyStartYear === currentFyStart.year() && fyEndYear === currentFyEnd.year()) {
        // Current FY selected → Show last month
        var lastMonth = currentDate.clone().subtract(1, 'month');
        sDate = lastMonth.clone().startOf('month').format('YYYY-MM-DD');
        eDate = lastMonth.clone().endOf('month').format('YYYY-MM-DD');
    } else {
        // Other FY selected → Show March of selected FY
        sDate = moment(fyEndYear + "-03-01").startOf('month').format('YYYY-MM-DD');
        eDate = moment(fyEndYear + "-03-31").format('YYYY-MM-DD');
    }
    location.href = route('company.reports.gstr-1', {
        'start_date': sDate,
        'end_date': eDate,
        'filter_type': 1
    });
})

hotkeys('shift+9', function () {
    location.href = route('company.tds-liability-report');
})

hotkeys('shift+F2', function () {

    if ($('#saleTransactionIndex').length) {
        $('#saleDateRangeFilter').focus().val('')
    }

    if ($('#saleReturnTransactionIndex').length) {
        $('#saleReturnDateRangeFilter').focus().val('')
    }

    if ($('#incomeDebitNoteIndex').length) {
        $('#debitNoteDateRangeFilter').focus().val('')
    }

    if ($('#incomeCreditNoteIndex').length) {
        $('#creditNoteDateRangeFilter').focus().val('')
    }

    if ($('#purchaseTransactionIndex').length) {
        $('#purchaseDateRangeFilter').focus().val('')
    }

    if ($('#purchaseReturnTransactionIndex').length) {
        $('#purchaseReturnDateRangeFilter').focus().val('')
    }

    if ($('#expenseDebitNoteIndex').length) {
        $('#expenseDebitNoteDateRangeFilter').focus().val('')
    }

    if ($('#expenseCreditNoteIndex').length) {
        $('#expenseCreditNoteDateRangeFilter').focus().val('')
    }

    if ($('#receiptTransactionIndex').length) {
        $('#receiptDateRangeFilter').focus().val('')
    }

    if ($('#paymentTransactionIndex').length) {
        $('#paymentDateRangeFilter').focus().val('')
    }

    if ($('#companyWithoutFranchiseList').length) {
      $('#companyDateRangeFilter').focus().val('')
    }

    if ($('#journalTransactionIndex').length) {
        $('#journalDateRangeFilter').focus().val('')
    }

    if ($('#ledgerReportIndex').length) {
        $('#ledgerReportDateRangeFilter').focus().val('')
    }

    if ($('#outstandingReportIndex').length) {
        $('#outstandingReportDatepicker').focus().val('')
    }

    if ($('#ageingReportIndex').length) {
        $('#ageingReportDatepicker').focus().val('')
    }

    if ($('#saleReportIndex').length) {
        $('#saleReportDateRangeFilter').focus().val('')
    }

    if ($('#purchaseReportIndex').length) {
        $('#purchaseReportDateRangeFilter').focus().val('')
    }

    if ($('#dayBookReportIndex').length) {
        $('#dayBookReportDateRangeFilter').focus().val('')
    }

    if ($('#stockReportIndex').length) {
        $('#stockReportDateRangeFilter').focus().val('')
    }

    if ($('#brokerReportIndex').length) {
        $('#brokerReportDateRangeFilter').focus().val('')
    }

    if ($('#trialBalanceReportIndex').length) {
        $('#trailBalanceDateFilter').focus().val('')
    }

    if ($('#tradingProfitLossReportIndex').length) {
        $('#tradingProfitLossReportDatepicker').focus().val('')
    }

    if ($('#balanceSheetReportIndex').length) {
        $('#balanceSheetDate').focus().val('')
    }

    if ($('#cashFlowReportIndex').length) {
        $('#cashFlowSheetDate').focus().val('')
    }

    if ($('#gstr1ReportIndex').length) {
        $('#gstr1ReportDateRange').focus().val('')
    }

    if ($('#gstr3bReportIndex').length) {
        $('#gstr3bSummaryReportDateRange').focus().val('')
    }

    if ($('#inputTaxReportIndex').length) {
        $('#inputTaxRegisterReportDateRangeFilter').focus().val('')
    }

    if ($('#outputTaxReportIndex').length) {
        $('#outputTaxRegisterReportDateRangeFilter').focus().val('')
    }

    if ($('#hsnOutwardTaxReportIndex').length) {
        $('#hsnOutwardTaxReportDateRangeFilter').focus().val('')
    }

    if ($('#hsnInwardTaxReportIndex').length) {
        $('#hsnInwardTaxReportDateRangeFilter').focus().val('')
    }

    if ($('#tdsLiabilityReportIndex').length) {
        $('#tdsLiabilityReportDateRangeFilter').focus().val('')
    }

    if ($('#tdsReturnReportIndex').length) {
        $('#tdsReturnReportDateRangeFilter').focus().val('')
    }

    if ($('#tcsLiabilityReportIndex').length) {
        $('#tcsLiabilityReportDateRangeFilter').focus().val('')
    }

    if ($('#tcsReturnReportIndex').length) {
        $('#tcsReturnReportDateRangeFilter').focus().val('')
    }
})

hotkeys('shift+n', function () {
    if ($('#ledgerReportDatatable').length) {
        $('.show-narration-ledger-report').trigger('click')
    }

    if ($('#trialBalanceReportDatatable').length) {
        $('#showFullDetailsTrialBalance').trigger('click')
    }

    if ($('#tradingPLAccountReportDatatable').length) {
        $('#showFullDetailsTradingPLAccount').trigger('click')
    }

    if ($('#balanceSheetReportDatatable').length) {
        $('#showFullDetailsBalanceSheet').trigger('click')
    }

})

hotkeys('alt+e', function () {
    if ($('#ledgerReportIndex').length) {
        $('#downloadLedgerReportPDFButton').trigger('submit')
    }

    if ($('#outstandingReportIndex').length) {
        let reportType = $('#outstandingReportType').val()
        let configType = $('#outstandingConfigType').val()
        let element = document.createElement('a')
        element.setAttribute('href', route('company.outstanding-report-export',
            {
                type: 'excel',
                report_type: reportType,
                config_type: configType,
            }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#ageingReportIndex').length) {
        let configType = $('#ageingConfigType').val()
        let element = document.createElement('a')
        element.setAttribute('href', route('company.ageing-report-export',
            { type: 'excel', config_type: configType }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#saleReportIndex').length) {
        let reportType = $('#saleReportType').val()
        let element = document.createElement('a')
        element.setAttribute('href', route('company.export-sales-report',
            { type: 'excel', report_type: reportType }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#purchaseReportIndex').length) {
        let reportType = $('#purchaseReportType').val()
        let element = document.createElement('a')
        element.setAttribute('href', route('company.export-purchase-report',
            { type: 'excel', report_type: reportType }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#dayBookReportIndex').length) {
        let element = document.createElement('a')
        element.setAttribute('href', route('company.export-day-book-report',
            { type: 'excel' }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#stockReportIndex').length) {
        let element = document.createElement('a')
        element.setAttribute('href', route('company.report-stock-excel'))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#itemStockReportIndex').length) {
        let element = document.createElement('a')
        element.setAttribute('href', route('company.item-wise-details-excel'))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#brokerReportIndex').length) {
        let element = document.createElement('a')
        element.setAttribute('href', route('company.export-broker-report',
            { type: 'excel' }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#trialBalanceReportIndex').length) {
        let startDate = $('#trialBalanceReportStartDate').val()
        let endDate = $('#trialBalanceReportEndDate').val()
        let element = document.createElement('a')
        let showTransactions = $('#showFullDetailsTrialBalance').is(":checked")
        let showDetails = $('#showAllDetailsTrialBalance').is(":checked")
        element.setAttribute('href', route('company.export-trial-balance',
            { type: 'excel', start_date: startDate, end_date: endDate ,showTransactions:showTransactions,showDetails:showDetails }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#tradingProfitLossReportIndex').length) {
        let startDate = $('#tradingPLReportStartDate').val()
        let endDate = $('#tradingPLReportEndDate').val()
        let hideZeroBalance = $("#hideZeroBalanceTradingProfitLoss").is(":checked");
        let element = document.createElement('a')
        element.setAttribute('href', route('company.export-trading-profit-loss',
            { type: 'excel', start_date: startDate, end_date: endDate ,hideZeroBalance : hideZeroBalance}))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#balanceSheetReportIndex').length) {
        let showFullDetails = $('#balanceSheetFullDetails').val()
        let date = $('#balanceSheetDate').val()
        let element = document.createElement('a')
        element.setAttribute('href', route('company.balance-sheet-report-pdf',
            { type: 'excel', showFullDetails: showFullDetails, date: date ,hideZeroBalance: $("#hideZeroBalanceBalanceSheet").is(":checked"),}))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#cashFlowReportIndex').length) {
        let element = document.createElement('a')
        element.setAttribute('href', route('company.export-cash-flow-report',
            { type: 'excel' }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#gstr1ReportIndex').length) {
        let startDate = $('#gstr1StartDate').val()
        let endDate = $('#gstr1EndDate').val()
        let element = document.createElement('a')
        element.setAttribute('href', route('company.reports.gstr-1-excel',
            { start_date: startDate, end_date: endDate,'filter_type': 1 }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#gstr3bReportIndex').length) {
        let startDate = $('#gstr3BStartDate').val()
        let endDate = $('#gstr3BEndDate').val()
        let element = document.createElement('a')
        element.setAttribute('href',
            route('company.reports.gstr-3b-detailed.excel',
                { start_date: startDate, end_date: endDate }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#inputTaxReportIndex').length) {
        let startDate = $('#inputTaxReportStartDate').val()
        let endDate = $('#inputTaxReportEndDate').val()
        let reportType = $('#inputTaxReportType').val()
        let element = document.createElement('a')
        element.setAttribute('href',
            route('company.input-tax-register-report-export',
                {
                    start_date: startDate,
                    end_date: endDate,
                    report_type: reportType,
                    type: 'excel',
                }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#outputTaxReportIndex').length) {
        let startDate = $('#outputTaxReportStartDate').val()
        let endDate = $('#outputTaxReportEndDate').val()
        let reportType = $('#outputTaxReportType').val()
        let element = document.createElement('a')
        element.setAttribute('href',
            route('company.output-tax-register-report-export',
                {
                    start_date: startDate,
                    end_date: endDate,
                    report_type: reportType,
                    type: 'excel',
                }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#hsnOutwardTaxReportIndex').length) {
        let startDate = $('#hsnOutwardReportStartDate').val()
        let endDate = $('#hsnOutwardReportEndDate').val()
        let reportType = $('#hsnOutwardReportType').val()
        let element = document.createElement('a')
        element.setAttribute('href', route('company.hsn-out-ward-report',
            {
                start_date: startDate,
                end_date: endDate,
                report_type: reportType,
                type: 'excel',
            }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#hsnInwardTaxReportIndex').length) {
        let startDate = $('#hsnInwardReportStartDate').val()
        let endDate = $('#hsnInwardReportEndDate').val()
        let reportType = $('#hsnInwardReportType').val()
        let element = document.createElement('a')
        element.setAttribute('href', route('company.hsn-in-ward-report',
            {
                start_date: startDate,
                end_date: endDate,
                report_type: reportType,
                type: 'excel',
            }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#tdsLiabilityReportIndex').length) {
        let reportType = $('#tdsLiabilityReportType').val()
        let element = document.createElement('a')
        element.setAttribute('href',
            route('company.export-tds-liability-report',
                { report_type: reportType, type: 'excel' }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#tdsReturnReportIndex').length) {
        let element = document.createElement('a')
        element.setAttribute('href', route('company.export-tds-return-report',
            { type: 'excel' }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#tcsLiabilityReportIndex').length) {
        let reportType = $('#tcsLiabilityReportType').val()
        let element = document.createElement('a')
        element.setAttribute('href',
            route('company.export-tcs-liability-report',
                { report_type: reportType, type: 'excel' }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if ($('#tcsReturnReportIndex').length) {
        let element = document.createElement('a')
        element.setAttribute('href', route('company.export-tcs-return-report',
            { type: 'excel' }))
        element.setAttribute('target', '_blank')
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)
    }

    if($('#companyWithoutFranchiseList').length) {
        let element = document.createElement('a')
        element.setAttribute('href', route('admin.company-list.export'))
            element.setAttribute('target', '_blank')
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
    }

    if($('#subscriptionList').length) {
      let element = document.createElement('a')
      element.setAttribute('href', route('admin.subscription-list-export',
          { type: 'excel' }))
          element.setAttribute('target', '_blank')
          document.body.appendChild(element)
          element.click()
          document.body.removeChild(element)
    }

    if ($("#billWiseProfitReportIndex").length) {
        openReportWindow(".shortcut-broker-excel-export");
    }
    if ($("#itemWiseProfitReportIndex").length) {
        openReportWindow(".shortcut-item-wise-profit-excel-export");
    }
    if ($("#PartyWiseSalesPurchaseReportIndex").length) {
        openReportWindow(".shortcut-party-wise-sales-purchase-excel-export");
    }
    if ($("#supplierSummaryReportIndex").length){
        openReportWindow(".shortcut-supplier-summary-excel-export")
    }
})

document.addEventListener('keydown', function(e) {
    if (e.key === 'F11') {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        try {
            location.href = route('company.setting.index');
        } catch (error) {
            try {
                location.href = route('company.general-setting.index');
            } catch (fallbackError) {
                console.warn('Features page route not available');
            }
        }
        return false;
    }
}, true); // Use capture mode

// Function key handlers using document event listener
document.addEventListener('keydown', function(e) {
    // F3 - Change/Load Company
    if (e.key === 'F3') {
        e.preventDefault();
        try {
            location.href = route('company.companies.index');
        } catch (error) {
            console.warn('Company selection route not available');
        }
        return false;
    }

    // F5 - Payment Voucher
    if (e.key === 'F5') {
        e.preventDefault();
        try {
            location.href = route('company.transaction-payment.create');
        } catch (error) {
            console.warn('Payment voucher route not available');
        }
        return false;
    }

    // F6 - Receipt Voucher
    if (e.key === 'F6') {
        e.preventDefault();
        try {
            location.href = route('company.transaction-receipt.create');
        } catch (error) {
            console.warn('Receipt voucher route not available');
        }
        return false;
    }

    // F7 - Journal Voucher
    if (e.key === 'F7') {
        e.preventDefault();
        try {
            location.href = route('company.transaction-journal.create');
        } catch (error) {
            console.warn('Journal voucher route not available');
        }
        return false;
    }
    // F8 - Sales Voucher
    if (e.key === 'F8') {
        e.preventDefault();
        try {
            if (typeof window.ReactNavigate === "function") {
                window.ReactNavigate('/company/sales/create');
            } else {
                location.href = route('company.sales.create');
            }
        } catch (error) {
            console.warn('Sales voucher route not available');
        }
        return false;
    }

    // F9 - Purchase Voucher
    if (e.key === 'F9') {
        e.preventDefault();
        try {
            if (typeof window.ReactNavigate === "function") {
                window.ReactNavigate('/company/purchases/create');
            } else {
                location.href = route('company.purchases.create');
            }
        } catch (error) {
            console.warn('Sales voucher route not available');
        }
        return false;
    }

    if (e.key === 'Escape') {
        // Check if there's an open modal first
        const openModal = document.querySelector('.modal.show, .modal.fade.show, [role="dialog"][aria-hidden="false"]');
        const openBootstrapModal = document.querySelector('.modal-backdrop');
        const openSweetAlert = document.querySelector('.swal2-container');
        const openDropdown = document.querySelector('.dropdown-menu.show');

        // If there's an open modal, tooltip, dropdown, or SweetAlert, let the default behavior handle it
        if (openModal || openBootstrapModal || openSweetAlert || openDropdown) {
          // Don't prevent default - let the modal/component handle the escape key
          return;
        }

        e.preventDefault();

        // Check if an input field is currently focused
        const activeElement = document.activeElement;

        const isInputFocused = activeElement && (
          activeElement.tagName === 'INPUT' ||
          activeElement.tagName === 'TEXTAREA' ||
          activeElement.tagName === 'SELECT' ||
          activeElement.isContentEditable ||
          activeElement.getAttribute('contenteditable') === 'true'
        );
console.log(isInputFocused,"isInputFocused");
        if (isInputFocused) {
          // Store the currently focused element and remove focus
          lastFocusedElementRef.current = activeElement;
          activeElement.blur();
        } else {
          // No input is focused - check if we have a previously focused element to refocus
          const storedElement = lastFocusedElementRef.current;
          const isStoredElementValid = storedElement &&
            document.contains(storedElement) &&
            storedElement.offsetParent !== null && // Element is visible
            !storedElement.disabled; // Element is not disabled

          if (isStoredElementValid) {
            // Check if the stored element is on the current page by checking if it's in the current viewport context
            const currentPageInputs = document.querySelectorAll('input, textarea, select, [contenteditable="true"]');
            const isElementOnCurrentPage = Array.from(currentPageInputs).includes(storedElement);

            if (isElementOnCurrentPage) {
              // Refocus the last focused element
              try {
                storedElement.focus();
                // Clear the reference after refocusing
                lastFocusedElementRef.current = null;
              } catch (error) {
                console.warn('Failed to refocus element:', error);
                lastFocusedElementRef.current = null;
              }
            } else {
              // Element is not on current page, clear reference and navigate back
              lastFocusedElementRef.current = null;
              try {
                window.history.back();
              } catch (error) {
                console.warn('Browser back navigation failed:', error);
              }
            }
          } else {
            console.log('No valid element to refocus');
            // No valid element to refocus, clear reference and navigate back
            lastFocusedElementRef.current = null;
            try {
              window.history.back();
            } catch (error) {
              console.warn('Browser back navigation failed:', error);
            }
          }
        }
        return;
      }

    if (e.key === 'F12') {
        e.preventDefault();

        // Check if any modal is currently visible and close it
        let modalVisible = false;

        // Check for visible modals and close them
        if ($('.modal.show').length > 0) {
            $('.modal.show').modal('hide');
            modalVisible = true;
        }

        // If no modal was visible, open the appropriate one
        if (!modalVisible) {
            if ($("#saleTransactionForm").length || $("#updateSaleTransactionForm").length) {
                $("#incomeConfiguration").trigger("click");
            }
            else if ($("#saleReturnForm").length || $("#updateSaleReturnForm").length) {
                $(".sale-return-configuration-modal").trigger("click");
            }
            else if (
                $("#incomeDebitNoteTransactionForm").length ||
                $("#updateIncomeDebitNoteTransactionForm").length
            ) {
                $(".income-debit-note-configuration-modal").trigger("click");
            }
            else if (
                $("#incomeCreditNoteTransactionForm").length ||
                $("#updateIncomeCreditNoteTransactionForm").length
            ) {
                $(".income-credit-note-configuration-modal").trigger("click");
            }
            else if ($("#purchaseTransactionForm").length || $("#updatePurchaseTransactionForm").length) {
                $(".purchase-configuration-modal").trigger("click");
            }
            else if ($("#purchaseReturnTransactionForm").length || $("#updatePurchaseReturnForm").length) {
                $(".purchase-return-configuration-modal").trigger("click");
            }
            else if (
                $("#expenseDebitNoteTransactionForm").length ||
                $("#updateExpenseDebitNoteForm").length
            ) {
                $(".expense-debit-note-configuration-modal").trigger("click");
            }
            else if (
                $("#expenseCreditNoteTransactionForm").length ||
                $("#updateExpenseCreditNoteTransactionForm").length
            ) {
                $(".expense-credit-note-configuration-modal").trigger("click");
            }
            else if ($("#receiptTransactionForm").length || $("#updateReceiptTransactionForm").length) {
                $(".receipt-transaction-configuration-modal").trigger("click");
            }
            else if ($("#paymentTransactionForm").length || $("#updatePaymentTransactionForm").length) {
                $(".payment-transaction-configuration-modal").trigger("click");
            }
            else if (
                $("#createJournalTransactionForm").length ||
                $("#updateJournalTransactionForm").length
            ) {
                $(".journal-transaction-configuration-modal").trigger("click");
            }
            else if (
                $("#outstandingPayableReportDataTable").length ||
                $("#outstandingReceivableReportDataTable").length
            ) {
                $(".show-narration-outstanding-report").trigger("click");
            }
        }
        return false;
    }
});