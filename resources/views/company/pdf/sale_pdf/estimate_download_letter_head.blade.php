<!DOCTYPE html>
<html lang="en">
@php
    $pdfSymbol = getPrintPdfCurrencySymbol($currentCompany->id);
    $dockereEnabled = dockerEnabled() && !isset($preview_enabled);
    $fontStyleName = $dockereEnabled ? 'Arial Unicode MS' : 'Arial-unicode-ms';
    $fontCssName = $dockereEnabled ? 'font-docker.css' : 'font.css';
@endphp

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/' . $fontCssName) }}">
    <title>{{ isset($fileName) ? $fileName : $transaction->document_number }}</title>
    @include('company.pdf.sale_pdf.print_custom_font_css_variables')
    <style>
        h1 {
            font-size: 27px;
        }

        * {
            margin: 0;
            padding: 0;
            text-indent: 0;
            font-family: "{{ $fontStyleName }}";
            font-size: 13px;
            font-weight: 400;
            box-sizing: border-box;
        }

        @page {
            margin-left: 20px !important;
            margin-right: 20px !important;
            margin-top: var(--header-space) !important;
            margin-bottom: var(--footer-space) !important;
        }

        .letter-head-table {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            width: 100%;
            box-sizing: border-box;
        }

        .main-content-table {
            border: 2px solid black;
            display: flex;
            flex-direction: column;
        }

        table {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }

        .custom-table-heading {
            padding: 6px 8px;
            font-weight: bold;
            width: 100px;
            font-size: 12px;
            line-height: 12px;
        }

        .text-primary {
            color: #4f158c;
        }

        .address {
            font-size: 11px;
            /* line-height: 14px; */
            padding-left: 8px;
            padding-right: 8px;
        }

        .phone {
            font-size: 11px;
            /* line-height: 14px; */
            padding-left: 8px;
            padding-right: 8px;
        }

        td {
            vertical-align: top;
        }

        .fs-13 {
            font-size: 13px;
        }

        .fs-12 {
            font-size: 12px;
        }

        .fw-6 {
            font-weight: 600;
        }

        .whitespace-nowrap {
            white-space: nowrap;
        }

        .border-bottom {
            border-bottom: 1px solid black;
        }

        .border-right {
            border-right: 1px solid black;
        }

        .border-top {
            border-top: 1px solid black;
        }

        .border-left {
            border-left: 1px solid black;
        }

        .vertical-top {
            vertical-align: top;
        }

        .vertical-middle {
            vertical-align: middle;
        }

        .vertical-bottom {
            vertical-align: bottom;
        }

        .text-center {
            text-align: center;
        }

        .text-start {
            text-align: left;
        }

        .text-end {
            text-align: right;
        }

        .table-heading {
            padding: 3px 8px;
            text-align: left;
            position: relative;
            /* background-color: #eeeeee !important; */
        }

        .signature {
            max-width: 210px;
            height: 100px;
            margin-left: auto;
        }

        .desc {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-size: 12px;
            position: relative;
            padding-left: 5px;
            padding-right: 5px;
        }

        .desc::before {
            position: absolute;
            content: "(";
            top: 0;
            left: 0;
            font-size: 12px;
        }

        .desc::after {
            position: absolute;
            content: ")";
            bottom: 2px;
            right: 0;
            font-size: 12px;
        }

        .item-table tr:nth-last-child(-n+2):not(:last-child) td {
            font-size: 12px;
            border-bottom: 1px solid black;
            padding: 4px 8px 0 4px;
            height: 100% !important;
            vertical-align: top;
        }

        .d-none {
            display: none;
        }

        /*.for-preview {
                padding:20px;
                box-sizing:border-box
            }*/

        .min-width-250 {
            min-width: 250px !important;
        }

        .min-width-150 {
            min-width: 150px !important;
        }

        .pe-0 {
            padding-right: 0;
        }

        .ps-0 {
            padding-left: 0;
        }

        .col {
            flex: 1 0 0%;
        }

        .row {
            display: flex;
            flex-wrap: wrap;
        }

        .qr-code {
            max-width: 75px;
            min-width: 75px;
            height: 75px;
        }

        .mb-0 {
            margin-bottom: 0 !important;
        }

        .fw-7 {
            font-weight: 700;
        }

        .fw-5 {
            font-weight: 500;
        }

        .bg-light {
            background-color: #F5F8FA !important;
        }

        .ps-3 {
            padding-left: 0.75rem !important; /* 12px padding on the left */
        }

        .w-100 {
            width: 100%
        }

    </style>
    @include('company.pdf.sale_pdf.print_custom_font_css')
</head>

<body>
    <div class="letter-head-table main-content-table">
            <table class="border-bottom">
                <tbody>
                    <tr>
                        @foreach (printCustomPDFLabelsForEstimate() as $key => $customLabel)
                            <td class="company-address-font-size fw-7 {{ $loop->iteration == 2 ? 'text-end' : '' }}" style="padding:4px 8px;">
                                {{ $key ?? null }}: <span class="company-address-font-size">{{ $customLabel ?? null }}</span>
                            </td>
                        @endforeach
                    </tr>
                </tbody>
            </table>
            <div class="text-center border-bottom" style="padding: 4px 2px 2px 2px">
                <h6 class="mb-0 fw-7 text-primary" style="font-size: 14px;">
                    {{ $transaction->transactionTitle->name }} ({#INVOICE_TYPE#})
                </h6>
            </div>
            <div class="row">
                <div class="col">
                    <table>
                        <tbody>
                            <tr>
                                <td>
                                    <div style="display: flex; padding: 4px 5px 4px 5px; height: 100%;">
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                {{-- Estimate No --}}
                <div class="border-left" style="width:33.34%;">
                    <div>
                        <table>
                            <tbody>
                                <tr>
                                    <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 2px 8px;">
                                        {{ $changeLabel['estimate_invoice_number'] ?? 'Estimate No' }}:
                                    </td>
                                    <td class="text-end header-contents-font-size" style="padding: 2px 8px 2px 0px;  white-space: normal">
                                        {{ $transaction->document_number }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 2px 8px;">
                                        {{ $changeLabel['estimate_invoice_date'] ?? 'Estimate Date' }}:
                                    </td>
                                    <td class="text-end header-contents-font-size" style="padding: 2px 8px 2px 0px;  white-space: normal">
                                        {{ Carbon\Carbon::parse($transaction->document_date)->format('d-m-Y') }}
                                    </td>
                                </tr>
                                @if($invoiceSetting['show_estimate_credit_period'] ?? true)
                                    <tr>
                                        <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 2px 8px;">
                                            {{ $changeLabel['estimate_credit_period'] ?? 'Credit Period' }}:
                                        </td>
                                        <td class="text-end header-contents-font-size" style="padding: 2px 8px 2px 0px;  white-space: normal">
                                            {{ $creditPeriod }}
                                        </td>
                                    </tr>
                                @endif
                                @if($invoiceSetting['show_estimate_due_date'] ?? true)
                                    <tr>
                                        <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 2px 8px;">
                                            {{ $changeLabel['estimate_due_date'] ?? 'Due Date' }}:
                                        </td>
                                        <td class="text-end header-contents-font-size" style="padding: 2px 8px 2px 0px;  white-space: normal">
                                            {{ $dueDate }}
                                        </td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
                {{-- End Estimate No --}}
            </div>

            <div class="row">
                {{-- Estimate To --}}
                <div class="col vertical-top border-bottom pe-0 ps-0 {{ (($invoiceSetting['estimate_ship_to_details'] ?? true) || isset($transaction['dispatch_address_id']) && isset($dispatchAddress) && ($invoiceSetting['estimate_dispatch_from_details'] ?? false)) ? 'border-right' : '' }}">
                    <p class="mb-0 text-primary border-bottom border-top fw-7 table-heading header-labels-font-size">
                        {{ $changeLabel['estimate_bill_to'] ?? 'Estimate To' }} :
                    </p>
                    <h4 class="mb-0 fw-7 header-contents-font-size" style="padding: 4px 0 1px 8px; font-size: 15px">
                        {{ strtoupper($customerDetail->name) }}
                    </h4>
                    @if (isset($billingAddress))
                        <p class="m-0 address ps-3 fs-12 fw-5 header-contents-font-size">
                            @if ($billingAddress->address_1 != null)
                                {{ strtoupper($billingAddress->address_1) }}
                            @endif
                            @if ($billingAddress->address_2 != null)
                                {{ strtoupper($billingAddress->address_2) }},
                            @endif
                            {{ $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                            {{ $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                            {{ strtoupper(getCountryName($billingAddress->country_id ?? null)) }},
                            {{ $billingAddress->pin_code ?? null }}
                        </p>
                    @endif
                    <p class="mb-0 fs-12 fw-5 ps-3 header-contents-font-size" style="padding: 2px 0px 0px 0px">
                        @if (!empty($transaction->party_phone_number))
                            Contact No:
                                +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                        @elseif (!empty($customerDetail->model->phone_1))
                            Contact No:
                                +{{ $customerDetail->model->region_code_1 ?? '' }} {{ $customerDetail->model->phone_1 ?? null }}
                        @endif
                        @if (!empty($customerDetail->model->phone_2) && ($customerDetail->model->phone_1 !== $customerDetail->model->phone_2))
                            {{ empty($customerDetail->model->phone_1) && empty($transaction->party_phone_number) ? 'Contact No:' : "|" }}
                            <span class="whitespace-nowrap header-contents-font-size">
                                +{{ $customerDetail->model->region_code_2 ?? '' }} {{ $customerDetail->model->phone_2 ?? null }}
                            </span>
                        @endif
                    </p>
                    @if (!empty($customerDetail->model->person_email))
                        <p class="mb-0 fs-12 fw-5 ps-3 header-contents-font-size" style="padding: 2px 0px 0px 0px">
                            Email: {{ $customerDetail->model->person_email ?? null }}
                        </p>
                    @endif
                    @if ($showGst)
                        <p class="mb-0 fs-12 fw-5 ps-3 header-contents-font-size" style="padding: 2px 0px 0px 0px">
                            GSTIN: {{ $transaction->gstin ?? $customerDetail->model->gstin ?? null }}
                        </p>
                    @endif
                    @if (!empty($panNumber) && $showPanNumber)
                        <p class="mb-0 fs-12 fw-5 ps-3 header-contents-font-size" style="padding: 2px 0px 0px 0px">
                            PAN: {{ $panNumber ?? null }}
                        </p>
                    @endif
                </div>
                {{-- End Estimate To --}}
                {{-- Ship to --}}
                @if ($invoiceSetting['estimate_ship_to_details'] ?? true)
                    <div class="col vertical-top border-bottom {{ (isset($transaction['dispatch_address_id']) && isset($dispatchAddress) && ($invoiceSetting['estimate_dispatch_from_details'] ?? false)) ? 'border-right' : '' }}">
                        <p
                            class="mb-0 text-primary border-bottom border-top fw-7 table-heading header-labels-font-size">
                            {{ $changeLabel['estimate_ship_to'] ?? 'Ship to' }}:
                        </p>
                        <h4 class="mb-0 fw-7 header-contents-font-size" style="padding: 4px 0 1px 8px; font-size: 15px">
                            {{ strtoupper($transaction->shipping_name ?? $customerDetail->name) }}
                        </h4>
                        <p class="mb-0 address fs-12 fw-5 header-contents-font-size">
                            @if (isset($shippingAddress->address_1))
                                {{ strtoupper($shippingAddress->address_1) }},
                            @endif
                            @if (isset($shippingAddress->address_2))
                                {{ strtoupper($shippingAddress->address_2) }},
                            @endif
                            {{ isset($shippingAddress->city_id) ? strtoupper(getCityName($shippingAddress->city_id)) . ',' : ''  }}
                            {{ isset($shippingAddress->state_id) ? strtoupper(getStateName($shippingAddress->state_id)) . ',' : '' }}
                            {{ isset($shippingAddress->country_id) ? strtoupper(getCountryName($shippingAddress->country_id)) . ',' : '' }},
                            {{ $shippingAddress->pin_code ?? null }}
                        </p>
                        @if (!empty($transaction->party_phone_number))
                            <p class="mb-0 fs-12 fw-5 ps-3 header-contents-font-size" style="padding: 2px 0px 0px 0px">
                                Contact No:
                                    +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                            </p>
                        @endif
                        @if ($isCompanyGstApplicable && !empty($transaction->shipping_gstin))
                            <p class="mb-0 fs-12 fw-5 ps-3 header-contents-font-size" style="padding: 2px 0px 0px 0px">
                                GSTIN: {{ $transaction->shipping_gstin ?? null }}
                            </p>
                        @endif
                        @if (!empty($panNumber) && $showPanNumber)
                            <p class="mb-0 fs-12 fw-5 ps-3 header-contents-font-size" style="padding: 2px 0px 0px 0px">
                                PAN: {{ $panNumber ?? null }}
                            </p>
                        @endif
                    </div>
                @endif
                {{-- End Ship to --}}
                {{-- Dispatch From --}}
                @if (isset($transaction['dispatch_address_id']) && isset($dispatchAddress) && ($invoiceSetting['estimate_dispatch_from_details'] ?? false))
                    <div class="col vertical-top border-bottom pe-0 ps-0">
                        <p
                            class="mb-0 text-primary border-bottom border-top fw-7 table-heading header-labels-font-size">
                            {{ $changeLabel['estimate_dispatch_from'] ?? 'Dispatch from' }}:
                        </p>
                        <p class="mb-0 address fs-12 fw-5 header-contents-font-size">
                            {{ isset($dispatchAddress->address_1) ? strtoupper($dispatchAddress->address_1 .',') : null }}
                            {{ isset($dispatchAddress->address_2) ? strtoupper($dispatchAddress->address_2 .',') : null }}
                            {{ isset($dispatchAddress->city_id) ?  strtoupper(getCityName($dispatchAddress->city_id).',') : null }}
                            {{ isset($dispatchAddress->state_id) ? strtoupper(getStateName($dispatchAddress->state_id).',') : null }}
                            {{ isset($dispatchAddress->country_id) ? strtoupper(getCountryName($dispatchAddress->country_id).',') : null }}
                            {{ $dispatchAddress->pin_code ?? null }}
                        </p>
                    </div>
                @endif
                {{-- End Dispatch From --}}
            </div>

            <div class="border-bottom" style="display: flex;">
                {{-- Transport Details --}}
                @if (($invoiceSetting['estimate_transport_details'] ?? true) && isset($transaction->transportDetails))
                    <div class="vertical-top pe-0 ps-0 border-right" style="width: 50%;">
                        <table>
                            <tbody>
                                <tr>
                                    <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:42%">
                                        {{ $changeLabel['estimate_transport_name'] ?? 'Transport Name' }}:
                                    </td>
                                    <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                        {{ $transaction->transportDetails->transporter_name ?? '' }}
                                    </td>
                                </tr>
                                @if($isCompanyGstApplicable)
                                    <tr>
                                        <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:42%">
                                            {{ $changeLabel['gstin'] ?? 'GSTIN' }}:
                                        </td>
                                        <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                            {{ $transaction->transportDetails->gstin ?? '' }}
                                        </td>
                                    </tr>
                                @endif
                                <tr>
                                    <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:42%">
                                        {{ $changeLabel['estimate_document_no'] ?? 'Document No' }}:
                                    </td>
                                    <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                        {{ $transaction->transporter_document_number ?? '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:42%">
                                        {{ $changeLabel['estimate_document_date'] ?? 'Document Date' }}:
                                    </td>
                                    <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                        {{ !empty($transaction->transporter_document_date) ? \Carbon\Carbon::parse($transaction->transporter_document_date)->format('d-m-Y') : '' }}
                                    </td>
                                </tr>
                                @if (!empty($transaction->transporter_vehicle_number))
                                    <tr>
                                        <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:42%">
                                            {{ $changeLabel['estimate_transport_vehicle_number'] ?? 'Vehicle Number' }}:
                                        </td>
                                        <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                            {{ $transaction->transporter_vehicle_number ?? '' }}
                                        </td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                @endif
                {{-- End Transport Details --}}
                {{-- Broker Details, PO Number --}}
                <div class="vertical-top pe-0 ps-0" style="width: 50%;">
                    <table>
                        <tbody>
                             @if (($invoiceSetting['estimate_broker_details'] ?? true) && !empty($transaction->brokerDetails))
                                    <tr>
                                        <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:42%">
                                            {{ $changeLabel['broker_name'] ?? 'Broker Name' }}:
                                        </td>
                                        <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                            {{ $transaction->brokerDetails->broker_name ?? '' }}
                                        </td>
                                    </tr>
                                    @if($isCompanyGstApplicable)
                                        <tr>
                                            <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:42%">
                                                {{ $changeLabel['gstin'] ?? 'GSTIN' }}:
                                            </td>
                                            <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                                {{ $transaction->brokerDetails->gstin ?? '' }}
                                            </td>
                                        </tr>
                                    @endif
                            @endif
                            @if ($invoiceSetting['estimate_po_number'] ?? true)
                                    <tr>
                                        <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:42%">
                                            {{ $changeLabel['estimate_po_number_label'] ?? 'PO No' }}:
                                        </td>
                                        <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                            {{ $transaction->po_no }}
                                        </td>
                                    </tr>
                            @endif
                            @if ($invoiceSetting['show_estimate_po_date'] ?? true)
                                    <tr>
                                        <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:42%">
                                            {{ $changeLabel['estimate_po_date'] ?? 'PO Date' }}:
                                        </td>
                                        <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                            {{ isset($transaction->po_date) ? \Carbon\Carbon::parse($transaction->po_date)->format('d-m-Y') : null }}
                                        </td>
                                    </tr>
                            @endif
                            <tr>
                                <td class="fs-12 fw-6 header-contents-font-size" style="padding: 2px 8px 0px 8px; width:42%">
                                    Valid For:
                                </td>
                                <td class="fs-12 header-contents-font-size" style="padding: 2px 8px 0px 0px">
                                    {{ $validFor }} {{ !empty($transaction->valid_till_date) ? ' ('.Carbon\Carbon::parse($transaction->valid_till_date)->format('d-m-Y').')' : '' }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                {{-- End Broker Details, PO Number --}}
            </div>

            {{-- Custom Fields Section Start --}}
            @if (count($customFieldValues) > 0)
                @php
                    $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
                @endphp
                <table cellpadding="0" class="item-table">
                    @foreach ($customFields->chunk(3) as $chunk)
                        <tr class="border-bottom">
                            @foreach ($chunk as $customField)
                                <td class="{{ !$loop->last ? 'border-right' : '' }}" style="padding: 6px 8px; width:150px; ">
                                    <span style="font-weight: bold;">{{ $customField['label_name'] ?? '' }}</span> : {{ $customField['value'] ?? '' }}
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </table>
            @endif
            {{-- Custom Fields Section End --}}

            {{-- Item Table Section Start --}}
                <table cellpadding="0" class="item-table" style="flex-grow: 1;">
                    <tbody>
                        <tr class="border-bottom">
                            @php
                                $printableHeadings = collect($rearrangeItems['headings'])
                                    ->filter(fn($h) => $h['is_show_in_print'])
                                    ->values(); // reset keys to get accurate $loop indexing
                            @endphp
                            @foreach ($printableHeadings as $headings)
                                @if($headings['is_show_in_print'])
                                    <td class="table-headings-font-size {{ !$loop->last ? 'border-right' : '' }} whitespace-nowrap  {{ $headings['class'] }}"
                                        style="padding: 6px 8px; font-weight: bold;">
                                        {{ $headings['name'] }}
                                    </td>
                                @endif
                            @endforeach
                        </tr>
                        @foreach ($rearrangeItems['detail'] as $key => $items)
                            <tr>
                                @php
                                    $lastVisibleKey = collect($items)->filter(fn($h) => $h['is_show_in_print'])->keys()->last();
                                @endphp
                                @foreach ($items as  $key => $item)
                                    @if($item['is_show_in_print'])
                                        <td class="py-1 table-contents-font-size whitespace-nowrap {{ $key != $lastVisibleKey ? ' border-right' : '' }} {{ ($key == 'item_name') ? 'min-width-150' : 'text-center'}}"
                                            style="padding: 4px 8px 0 8px; {{ ($key == 'primary_quantity' || $key == 'secondary_quantity') ? 'white-space: nowrap;' : '' }}">
                                            {{ $item['value'] }}
                                            @if($key == 'item_name')
                                                @if($item['show_sku'])
                                                    <p class="description-font-size">Item Code:
                                                        {{ $item['sku'] ?? null }}</p>
                                                @endif
                                                @if($item['show_consolidating_items'])
                                                    <p style="word-break: break-word; " class="description-font-size">
                                                        {!! $item['consolidating_items'] !!}
                                                    </p>
                                                @endif
                                                @if($item['show_additional_description'])
                                                    <p style="word-break: break-word;" class="description-font-size">
                                                         {!! $item['additional_description'] !!}
                                                    </p>
                                                @endif
                                                @if($item['show_item_image'])
                                                    <div><img src="{{ $item['item_image'] }}" width="60"  height="60" style="margin-top: 4px"></div>
                                                @endif
                                            @endif
                                        </td>
                                    @endif
                                @endforeach
                            </tr>
                        @endforeach
                        <tr class="border-bottom border-top">
                            @php
                                $printablefooter = collect($rearrangeItems['footer'])
                                ->filter(fn($h) => $h['is_show_in_print'])
                                ->values();
                            @endphp
                            @foreach ($printablefooter as $key => $footer)
                                @if($footer['is_show_in_print'])
                                    <td class="table-headings-font-size fw-6 {{ ($loop->index === $loop->count - 1) ? '' : 'border-right' }}  fw-6 {{ ($key == 'item_name') ? 'min-width-150 ps-2' : 'text-center'}}"
                                    style="">{{ $footer['value'] }}</td>
                                @endif
                            @endforeach
                        </tr>
                    </tbody>
                </table>
            {{-- Item Table Section End --}}

            <table cellpadding="0">
                <tbody>
                    <tr class="border-bottom">
                        {{-- term_and_condition and narration --}}
                        <td>
                            @if ($transaction->term_and_condition || $transaction->narration)
                                <div>
                                    @if(($showPrintSettings['show_estimate_narration'] ?? true) && $transaction->narration)
                                        <div class="{{ ($showPrintSettings['show_sale_terms_and_conditions'] ?? true) && $transaction->term_and_condition ? 'border-bottom' : '' }}">
                                            <h4 class="note-font-size fw-6" style="padding: 8px 0 0 8px;">
                                                {{ $changeLabel['estimate_narration'] ?? 'Note' }}:
                                            </h4>
                                            <div style="padding: 4px 8px;">
                                                <p class="note-font-size">
                                                    {!! nl2br($transaction->narration) !!}
                                                </p>
                                            </div>
                                        </div>
                                    @endif
                                    @if(($showPrintSettings['show_estimate_terms_and_conditions'] ?? true) && $transaction->term_and_condition)
                                        <div>
                                            <h4 class="terms-and-conditions-font-size fw-6" style="padding: 8px 0 0 8px;">
                                                {{ $changeLabel['estimate_terms_and_conditions'] ?? 'Terms and Conditions' }}:
                                            </h4>
                                            <div style="padding: 4px 8px" class="terms-and-conditions-font-size">
                                                <p class="terms-and-conditions-font-size">
                                                    {!! nl2br($transaction->term_and_condition) !!}
                                                </p>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            @endif
                        </td>
                        {{-- End term_and_condition and narration --}}
                        {{-- Additional Charges and other Charges --}}
                        <td class="vertical-top" style="width: 231px; border-left: 1px solid black">
                            <table>
                                <tbody>
                                    @foreach ($additionalCharges as $additionalCharge)
                                        <tr class="{{ $loop->last ? 'border-bottom' : '' }}">
                                            <td class="table-headings-font-size vertical-top fw-6" style="padding: 3px 8px 2px 8px">
                                                {{ $additionalCharge['ledger_name'] }}
                                            </td>
                                            <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 3px 8px 2px 8px">
                                                {{ $pdfSymbol.getCurrencyFormat($additionalCharge['amount'] ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endforeach
                                    <tr class="">
                                        <td class="table-headings-font-size vertical-top fw-7" style="padding: 4px 8px 2px 8px">
                                            {{ $changeLabel['estimate_sub_total'] ?? ($isCompanyGstApplicable ? 'Taxable Value' : 'Sub Total') }}
                                        </td>
                                        <td class="table-headings-font-size vertical-top text-end fw-7" style="padding: 4px 8px 2px 8px">
                                            {{ $pdfSymbol.getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                        </td>
                                    </tr>
                                    @if($isCompanyGstApplicable)
                                        @if ($transaction->cgst != 0)
                                            <tr>
                                                <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px 0 8px">
                                                    {{ $changeLabel['estimate_cgst'] ?? 'CGST' }}
                                                </td>
                                                <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px 0 8px">
                                                    {{ $pdfSymbol.getCurrencyFormat(round($transaction->cgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                </td>
                                            </tr>
                                        @endif
                                        @if ($transaction->sgst != 0)
                                            <tr>
                                                <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px 0 8px">
                                                    {{ $changeLabel['estimate_sgst'] ?? 'SGST' }}
                                                </td>
                                                <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px 0 8px">
                                                    {{ $pdfSymbol.getCurrencyFormat(round($transaction->sgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                </td>
                                            </tr>
                                        @endif
                                        @if ($transaction->igst != 0)
                                            <tr>
                                                <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px 0 8px">
                                                    {{ $changeLabel['estimate_igst'] ?? 'IGST' }}
                                                </td>
                                                <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px 0 8px">
                                                    {{ $pdfSymbol.getCurrencyFormat(round($transaction->igst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                </td>
                                            </tr>
                                        @endif
                                    @endif
                                    @if ($transaction->cess != 0)
                                        <tr>
                                            <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px 0 8px">
                                                {{ $changeLabel['estimate_cess'] ?? 'Cess' }}
                                            </td>
                                            <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px 0 8px">
                                                {{ $pdfSymbol.getCurrencyFormat($transaction->cess ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endif
                                    @if ($transaction->tcs_amount != 0)
                                        <tr>
                                            <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px 0 8px">
                                                {{ $changeLabel['estimate_tcs'] ?? 'TCS' }}
                                            </td>
                                            <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px 0 8px">
                                                {{ $pdfSymbol.getCurrencyFormat($transaction->tcs_amount ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endif
                                    {{-- @if ($transaction->tds_amount != 0)
                                        <tr>
                                            <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px 0 8px">
                                                {{ $changeLabel['tds'] ?? 'TDS' }}
                                            </td>
                                            <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px 0 8px">
                                                {{ $pdfSymbol.getCurrencyFormat($transaction->tds_amount ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endif --}}
                                    @php
                                        if (empty($addLess)) {
                                            $total = $transaction->grand_total;
                                        } else {
                                            $addLessSum = collect($addLess)->sum('amount');
                                            $total = $transaction->grand_total - $addLessSum;
                                            $addLessSumTotal = collect($addLess)->where('is_show_in_print',1)->sum('amount');
                                            $total = $total + $addLessSumTotal;
                                        }
                                    @endphp
                                    @foreach (collect($addLess)->where('is_show_in_print',1) as $addLessItem)
                                        <tr>
                                            <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px 0 8px">
                                                {{ $addLessItem['ledger_name'] }}
                                            </td>
                                            <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px 0 8px">
                                                {{ $pdfSymbol.getCurrencyFormat($addLessItem['amount'] ?? '0.0') }}
                                            </td>
                                        </tr>
                                    @endforeach
                                    <tr>
                                        <td class="table-headings-font-size vertical-top fw-6" style="padding: 2px 8px">
                                            {{ $changeLabel['estimate_round_off'] ?? 'Round off' }}
                                        </td>
                                        <td class="table-headings-font-size vertical-top text-end fw-6" style="padding: 2px 8px">
                                            {{ $pdfSymbol.getCurrencyFormat($transaction->round_off_amount ?? '0.0') }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        {{-- End Additional Charges and other Charges --}}
                    </tr>
                </tbody>
            </table>

            <table cellpadding="0">
                <tbody>
                    <tr class="{{ ($invoiceSetting['estimate_qr_code'] ?? false) || ($invoiceSetting['estimate_bank_details'] ?? true) || ($showPrintSettings['show_estimate_authorized_signatory'] ?? true) || ($invoiceSetting['estimate_signature'] ?? true) ? 'border-bottom' : '' }}">
                        <td class="vertical-bottom">
                            @if($showPrintSettings['show_estimate_in_words'] ?? true)
                                <p class="mb-0 table-contents-font-size fw-6" style="display: flex; padding: 6px 8px">
                                    {{ $changeLabel['estimate_in_words'] ?? 'In Words' }}:
                                    <span class="table-contents-font-size" style="margin-left: 10px; font-weight: 400">
                                        {{ getAmountToWord($total ?? '0.0') }} Only
                                    </span>
                                </p>
                            @endif
                        </td>
                        <td style="width: 231px; border-left: 1px solid black">
                            <table class="vertical-bottom">
                                <tbody>
                                    <tr>
                                        <td class="text-primary total-font-size"
                                            style="padding: 3px 8px; font-weight: bold;">
                                            {{ $changeLabel['estimate_total'] ?? 'Total' }}
                                        </td>
                                        <td class="text-primary text-end total-font-size"
                                            style="padding: 3px 8px; font-weight: bold;">
                                            {{ $pdfSymbol.getCurrencyFormat($total ?? '0.0') }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>

            {{-- HSN Summary --}}
            @if ($isCompanyGstApplicable && !empty($checkHsnCodeExist) && ($invoiceSetting['estimate_hsn_summary'] ?? true))
                <div>
                    <table cellpadding="0">
                        <tbody>
                            <tr class="border-bottom" style="width: 100%">
                                <td class="text-center footer-headings-font-size border-right"
                                    style="padding: 4px 8px; font-weight: bold">
                                    SN
                                </td>
                                <td class="text-center footer-headings-font-size border-right"
                                    style="padding: 4px 8px; font-weight: bold">
                                    HSN/SAC
                                </td>
                                <td class="text-center footer-headings-font-size border-right"
                                    style="padding: 4px 8px; font-weight: bold">
                                    Taxable Amount
                                </td>
                                <td class="text-center footer-headings-font-size border-right"
                                    style="padding: 4px 8px; font-weight: bold">
                                    GST (%)
                                </td>
                                @if ($cgst != 0.0)
                                    <td class="text-center footer-headings-font-size border-right"
                                        style="padding: 4px 8px; font-weight: bold">
                                        CGST
                                    </td>
                                @endif
                                @if ($sgst != 0.0)
                                    <td class="text-center footer-headings-font-size border-right"
                                        style="padding: 4px 8px; font-weight: bold; min-width:68px;">
                                        SGST
                                    </td>
                                @endif
                                @if ($igst != 0.0)
                                    <td class="text-center footer-headings-font-size border-right"
                                        style="padding: 4px 8px; font-weight: bold; min-width:68px;">
                                        IGST
                                    </td>
                                @endif
                                <td class="text-center footer-headings-font-size"
                                    style="padding: 4px 8px; font-weight: bold">
                                    Total Tax
                                </td>
                            </tr>
                            @php
                                $uniqueKey = 1;
                            @endphp
                            @foreach ($checkHsnCodeExist as $key => $item)
                                @foreach ($item as $hsnCode => $data)
                                    <tr>
                                        <td class="text-center footer-contents-font-size border-right" style="padding: 2px 8px">
                                            {{ $uniqueKey++ }}
                                        </td>
                                        <td class="text-center footer-contents-font-size border-right" style="padding: 2px 8px">
                                            {{ !empty($hsnCode) ? $hsnCode : '-' }}
                                        </td>
                                        <td class="text-center footer-contents-font-size border-right" style="padding: 2px 8px">
                                            {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['taxableValue'][$hsnCode], getCompanyFixedDigitNumber()) ?? 0) }}
                                        </td>
                                        <td class="text-center footer-contents-font-size border-right" style="padding: 2px 8px">
                                            {{ !empty($key) ? $key : '-' }}
                                        </td>
                                        @if ($cgst != 0.0)
                                            <td class="text-center footer-contents-font-size border-right" style="padding: 2px 8px">
                                                {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['cgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                            </td>
                                        @endif
                                        @if ($sgst != 0.0)
                                            <td class="text-center footer-contents-font-size border-right" style="padding: 2px 8px">
                                                {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['sgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                            </td>
                                        @endif
                                        @if ($igst != 0.0)
                                            <td class="text-center footer-contents-font-size border-right" style="padding: 2px 8px">
                                                {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['igst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                            </td>
                                        @endif
                                        @php
                                            $totalTax =
                                                round($checkTAXtExist[$key]['cgst'][$hsnCode], getCompanyFixedDigitNumber()) +
                                                round($checkTAXtExist[$key]['sgst'][$hsnCode], getCompanyFixedDigitNumber()) +
                                                round($checkTAXtExist[$key]['igst'][$hsnCode], getCompanyFixedDigitNumber());
                                        @endphp
                                        <td class="text-center footer-contents-font-size" style="padding: 2px 8px">
                                            {{ $pdfSymbol.getCurrencyFormat(round($totalTax ?? 0, getCompanyFixedDigitNumber())) }}
                                        </td>
                                    </tr>
                                @endforeach
                            @endforeach
                            <tr class="fs-12 border-bottom border-top fw-6" style="padding: 2px 8px">
                                <td class="text-center footer-headings-font-size border-right fw-6" style="padding: 4px 8px"></td>
                                <td class="text-center footer-headings-font-size border-right fw-6" style="padding: 4px 8px">
                                    Total
                                </td>
                                <td class="text-center footer-headings-font-size border-right fw-6" style="padding: 4px 8px">
                                    {{ $pdfSymbol.getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber())) }}
                                </td>
                                <td class="text-center footer-headings-font-size border-right fw-6" style="padding: 4px 8px"></td>
                                @if ($cgst != 0.0)
                                    <td class="text-center footer-headings-font-size border-right fw-6" style="padding: 4px 8px">
                                        {{ $pdfSymbol.getCurrencyFormat(round($cgst, getCompanyFixedDigitNumber())) }}
                                    </td>
                                @endif
                                @if ($sgst != 0.0)
                                    <td class="text-center footer-headings-font-size border-right fw-6" style="padding: 4px 8px">
                                        {{ $pdfSymbol.getCurrencyFormat(round($sgst, getCompanyFixedDigitNumber())) }}
                                    </td>
                                @endif
                                @if ($igst != 0.0)
                                    <td class="text-center footer-headings-font-size fw-6 border-right" style="padding: 4px 8px">
                                        {{ $pdfSymbol.getCurrencyFormat(round($igst, getCompanyFixedDigitNumber())) }}
                                    </td>
                                @endif
                                <td class="text-center footer-headings-font-size fw-6" style="padding: 4px 8px">
                                    @php
                                        $grandTotalTax = $cgst + $sgst + $igst;
                                    @endphp
                                    {{ $pdfSymbol.getCurrencyFormat(round($grandTotalTax, getCompanyFixedDigitNumber())) }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            @endif
            {{-- End HSN Summary --}}

            {{-- Bank Details / Signature Section Start --}}
            @if (($invoiceSetting['estimate_qr_code'] ?? false) || ($invoiceSetting['estimate_bank_details'] ?? true) || ($showPrintSettings['show_estimate_authorized_signatory'] ?? true) || ($invoiceSetting['estimate_signature'] ?? true))
                <table cellpadding="0" style="page-break-inside: avoid !important">
                    <tbody>
                        <tr>
                            @if (($invoiceSetting['estimate_qr_code'] ?? false) || ($invoiceSetting['estimate_bank_details'] ?? true))
                                <td class="{{ ($showPrintSettings['show_estimate_authorized_signatory'] ?? true) || ($invoiceSetting['estimate_signature'] ?? true) ? 'border-right': '' }}">
                                    <table>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <div style="display: flex; padding: 4px 5px 4px 5px; height: 100%;">
                                                        @if ((isset($invoiceSetting['estimate_qr_code']) && $invoiceSetting['estimate_qr_code'] == 1 ? true : false) && isset($bankDetail->upi_id))
                                                            <div class="qr-code"
                                                                style=" padding-right: 10px; white-space: nowrap; width:100px; height:100px; min-width:100px;">
                                                                <img src="data:image/svg+xml;base64,{{ base64_encode(generateUPIQRCode($bankDetail->upi_id, $transaction->due_amount)) }}"
                                                                    width="75" height="75" />
                                                            </div>
                                                        @endif
                                                        @if ((isset($invoiceSetting['estimate_bank_details']) && $invoiceSetting['estimate_bank_details'] == 1 ? true : false))
                                                            <div>
                                                                <table style="white-space: nowrap">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td class="fs-12 fw-6 footer-headings-font-size" style=" padding: 2px 8px 0 8px;">
                                                                                Bank:
                                                                            </td>
                                                                            <td class="fs-12 footer-contents-font-size"
                                                                                style="padding: 2px 8px 0 0px;  white-space: normal">
                                                                                {{ !empty($bankDetail) ? $bankDetail->bank_name : null }}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="fs-12 fw-6 footer-headings-font-size" style=" padding: 2px 8px 0 8px;">
                                                                                IFSC Code:
                                                                            </td>
                                                                            <td class="fs-12 footer-contents-font-size" style="padding: 2px 8px 0 0px;">
                                                                                {{ !empty($bankDetail) ? $bankDetail->ifsc_code : null }}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="fs-12 fw-6 footer-headings-font-size" style="padding: 2px 8px 0 8px;">
                                                                                A/C Number:
                                                                            </td>
                                                                            <td class="fs-12 footer-contents-font-size" style="padding: 2px 8px 0 0px;">
                                                                                {{ $accountNumber }}
                                                                            </td>
                                                                        </tr>
                                                                        @if (!empty($bankDetail) && $bankDetail->swift_code != null)
                                                                            <tr>
                                                                                <td class="fs-12 fw-6 footer-headings-font-size" style="padding: 2px 8px 0 8px;">
                                                                                    Swift Code:
                                                                                </td>
                                                                                <td class="fs-12 footer-contents-font-size"
                                                                                    style="padding: 2px 8px 0 0px; white-space: normal">
                                                                                    {{ $bankDetail->swift_code }}
                                                                                </td>
                                                                            </tr>
                                                                        @endif
                                                                        @if (isset($bankDetail->account_holder_name))
                                                                            <tr>
                                                                                <td class="fs-12 fw-6 footer-headings-font-size" style="padding: 2px 8px 0 8px;">
                                                                                    A/C Name:
                                                                                </td>
                                                                                <td class="fs-12 footer-contents-font-size"
                                                                                    style="padding: 2px 8px 0 0px; white-space: normal">
                                                                                    {{ $bankDetail->account_holder_name }}
                                                                                </td>
                                                                            </tr>
                                                                        @endif
                                                                        <tr>
                                                                            <td class="fs-12 fw-6 footer-headings-font-size" style="padding: 2px 8px 0 8px;">
                                                                                Bank Branch:
                                                                            </td>
                                                                            <td class="fs-12 footer-contents-font-size"
                                                                                style="padding: 2px 8px 0 0px; white-space: normal">
                                                                                {{ $branchName }}
                                                                            </td>
                                                                        </tr>
                                                                        @if(isset($bankDetail->upi_id))
                                                                            <tr>
                                                                                <td class="fs-12 fw-6 footer-headings-font-size" style="padding: 2px 8px 0 8px;">
                                                                                    UPI ID:
                                                                                </td>
                                                                                <td class="fs-12 footer-contents-font-size"
                                                                                    style="padding: 2px 8px 0 0px; white-space: normal">
                                                                                    {{ $bankDetail->upi_id }}
                                                                                </td>
                                                                            </tr>
                                                                        @endif
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            @endif
                            @if(($showPrintSettings['show_estimate_authorized_signatory'] ?? true) || ($invoiceSetting['estimate_signature'] ?? true))
                                <td class="vertical-bottom border-right" style="width: 165px; position: relative">
                                    <div style="padding:4px 8px; margin-left:auto;">
                                        <div class="text-end signature" style="max-width:140px; height:70px;"></div>
                                        <p class="mb-2 verical-bottom text-end fs-12 fw-5 footer-contents-font-size">
                                            Receiver Signatory
                                        </p>
                                    </div>
                                </td>
                                <td class="vertical-bottom" style="width: 166px; position: relative">
                                    <div style="padding:4px 8px; margin-left:auto;">
                                        @if ($showPrintSettings['show_estimate_authorized_signatory'] ?? true)
                                            <p class="fs-12 fw-7 footer-contents-font-size" style="margin-top:5px;">
                                                For, {{ strtoupper($currentCompany->trade_name) }}
                                            </p>
                                        @endif
                                        <div class="text-end signature" style="max-width:140px; height:70px;">
                                            @if (($invoiceSetting['estimate_signature'] ?? false) &&
                                                    $currentCompany->company_signature != asset('images/preview-img.png'))
                                                <img src="{{ $currentCompany->company_signature ?? null }}" alt="company-img"
                                                    style="max-width:100%; height:100%;  object-fit:contain; margin-left:auto;">
                                            @endif
                                        </div>
                                        @if ($showPrintSettings['show_estimate_authorized_signatory'] ?? true)
                                            <p class="mb-2 verical-bottom text-end fs-12 fw-5 footer-contents-font-size">
                                                {{ $changeLabel['estimate_authorized_signatory'] ?? 'Authorized Signatory' }}
                                            </p>
                                        @endif
                                    </div>
                                </td>
                            @endif
                        </tr>
                    </tbody>
                </table>
            @endif
            {{-- End Bank Details / Signature Section Start --}}
    </div>
</body>

</html>
