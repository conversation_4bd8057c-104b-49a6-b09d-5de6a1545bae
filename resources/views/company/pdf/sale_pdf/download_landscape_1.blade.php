<!DOCTYPE html>
<html lang="en">
@php
    $pdfSymbol = getPrintPdfCurrencySymbol($currentCompany->id);
    $dockereEnabled = dockerEnabled() && !isset($preview_enabled);
    $fontStyleName = $dockereEnabled ? 'Arial Unicode MS' : 'Arial-unicode-ms';
    $fontCssName = $dockereEnabled ? 'font-docker.css' : 'font.css';
@endphp

<head>
    <meta charset="UTF-8" />
    <title>{{ isset($fileName) ? $fileName : $transaction->full_invoice_number }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/' . $fontCssName) }}">
    @include('company.pdf.sale_pdf.print_custom_font_css_variables')
    <style>
        * {
            margin: 0;
            padding: 0;
            text-indent: 0;
            font-family: "{{ $fontStyleName }}";
            font-size: 13px;
            font-weight: 400;
            box-sizing: border-box;
        }

        @page {
            margin: 20px !important;
        }


        .me-4 {
            margin-right: 24px;
        }

        .mt-1 {
            margin-top: 4px !important;
        }

        .mt-2 {
            margin-top: 8px !important;
        }

        .mb-2 {
            margin-bottom: 8px !important;
        }

        .mb-0 {
            margin-bottom: 0px !important;
        }

        .mb-1 {
            margin-bottom: 4px;
        }

        .mb-3 {
            margin-bottom: 16px !important;
        }

        .mb-4 {
            margin-bottom: 24px !important;
        }

        .p-2 {
            padding: 8px;
        }

        .px-2 {
            padding-left: 8px !important;
            padding-right: 8px !important;
        }

        .px-1 {
            padding-left: 4px !important;
            padding-right: 4px !important;
        }

        .px-3 {
            padding-left: 16px !important;
            padding-right: 16px !important;
        }

        .py-1 {
            padding: 4px 0;
        }

        .p-1 {
            padding: 4px;
        }

        .py-2 {
            padding: 8px 0;
        }

        .ps-1 {
            padding-left: 4px;
        }

        .ps-3 {
            padding-left: 16px;
        }

        .pe-0 {
            padding-right: 0;
        }

        .pe-3 {
            padding-right: 16px;
        }

        .pe-4 {
            padding-right: 24px;
        }

        .pe-5 {
            padding-right: 48px;
        }

        .w-20 {
            width: 20% !important;
        }

        .w-25 {
            width: 25% !important;
        }

        .w-50 {
            width: 50% !important;
        }

        .w-75 {
            width: 75% !important;
        }

        .w-80 {
            width: 80% !important;
        }

        .w-100 {
            width: 100% !important;
        }

        .mb-30px {
            margin-bottom: 30px;
        }

        .fw-medium {
            font-weight: 500;
        }

        .fw-semibold {
            font-weight: 600;
        }

        .fw-bold {
            font-weight: 700;
        }

        .h-100 {
            height: 100% !important;
        }

        .w-100 {
            width: 100%;
        }

        .w-25 {
            width: 25%;
        }

        .w-50 {
            width: 50%;
        }

        .w-60 {
            width: 60%;
        }

        .w-300px {
            width: 250px;
        }

        .w-40 {
            width: 40%;
        }

        .justify-content-end {
            justify-content: flex-end;
        }

        .justify-content-between {
            justify-content: space-between;
        }

        .align-items-center {
            align-items: center !important;
        }

        .align-items-end {
            align-items: end !important;
        }

        .fs-42 {
            font-size: 42px !important;
        }

        .fs-14 {
            font-size: 14px !important;
        }

        .fs-12 {
            font-size: 12px !important;
        }

        .fs-15 {
            font-size: 15px !important;
        }

        .fs-13 {
            font-size: 13px !important;
        }

        .fs-18 {
            font-size: 18px !important;
        }

        .fs-20 {
            font-size: 20px !important;
        }

        .fs-24 {
            font-size: 24px !important;
        }

        .gap-3 {
            column-gap: 24px;
        }

        .word-break {
            word-break: break-all;
        }

        .vertical-align-baseline {
            vertical-align: baseline;
        }

        .vertical-align-top {
            vertical-align: top;
        }

        .d-flex {
            display: flex !important;
        }

        .text-start {
            text-align: start !important;
        }

        .text-center {
            text-align: center !important;
        }

        .text-end {
            text-align: end;
        }

        .border-bottom {
            border-bottom: 1px solid black;
        }

        .border-top {
            border-top: 1px solid black !important;
        }

        .border-left {
            border-left: 1px solid black !important;
        }

        .border-right {
            border-right: 1px solid black !important;
        }

        .border-right-0 {
            border-right: 0 !important;
        }

        .max-width-400px{
            max-width:400px;
            min-width:400px;
            width:100%;
        }
        .max-width-275px{
            max-width:275px;
            min-width:275px;
            width:100%;
        }
        .my-3 {
            margin: 24px 0px;
        }

        .mx-2 {
            margin: 0px 8px !important;
        }

        .my-2 {
            margin: 8px 0px !important;
        }

        .pb-2 {
            padding-bottom: 8px;
        }

        .gap-15 {
            gap: 15px;
        }

        .text-nowrap {
            white-space: nowrap;
        }

        .flex-column {
            flex-direction: column !important;
        }

        @page {
            margin: 20px !important;
        }

        .table-container {
            display: flex;
            flex-direction: column;
        }

        .main-table {
            border: 2px solid black;
            border-collapse: collapse;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .item-table-height {
            display: flex;
            flex-grow: 1;
        }

        .content-table tbody tr:last-child td {
            height: 100%;
        }

        .content-table tbody td {
            border-bottom: none !important;
            border-top: none !important;
        }

        .head-table {
            vertical-align: top;
            padding: 15px 10px;
        }

        .logo-hisab {
            letter-spacing: -2px;
        }

        .hisab {
            color: var(--color-theme);
        }

        .kitab {
            margin-left: -5px;
        }

        .font-italic {
            font-style: italic;
        }

        .contact-link {
            text-decoration: none;
            color: black;
            outline: 0 !important;
        }

        .text-primary {
            color: #ffffff !important;
        }

        .bg-primary {
            background-color: #4f158c !important;
        }

        /* .lh-sm {
            line-height: 15px;
        } */

        .pe-1 {
            padding-right: 4px;
        }

        .w-5 {
            /* max-width: 30px;
            width: 30px; */
            min-width: 30px;
        }

        .text-primary {
            color: #4f158c !important;
        }

        table {
            border-collapse: collapse;
        }

        .bg-light {
            background-color: #ede8f3 !important;
        }

        .tax {
            border-bottom: 1px solid black;
        }

        .money {
            font-style: italic;
            color: #4f158c;
        }

        .pdf-img {
            height: 110px;
            min-width: 120px;
        }

        .qr-blur-section {
            width: 465px;
        }

        .qr-blur-img {
            max-height: 130px;
            height: 100%;
            min-width: 100px;
            max-width: 100px;
            width: 130px;
        }

        .qr-img {
            width: 90px;
            height: 90px;
        }

        .sign {
            margin: 0 auto;
            height: 135px;
            width: 165px;
            justify-content: center;
            overflow: hidden;
        }

        .ms-auto {
            margin-left: auto;
        }

        .bill-table {
            border: 1px;
            border-style: solid none none none;
            border-color: black;
        }

        .content-table {
            border-style: none;
        }

        @media print {
            .qr-table {
                /* page-break-before: always; */
                page-break-inside: avoid;
            }
        }

        .content-table th,
        .tax-table thead th {
            border-left: 1px solid black;
            border-bottom: 1px solid black;
            border-right: 1px solid black;
        }

        .tax-table {
            border-top: none !important;
        }

        .tax-table td {
            border-top: none !important;
            border-bottom: none !important;
        }

        .tax-table tfoot td {
            border: 1px solid black !important;
        }

        .content-table td:nth-child(2) {
            width: 100%
        }

        .tax-table {
            border: 1px;
            border-style: none none solid none;
            border-color: black;
        }

        .qr-table td :last-child {
            border-right: 0 !important;
        }

        .qr-table td {
            border-right: 1px solid black;
        }

        .total-table td {
            border-right: 0 !important;
        }

        .content-table td {
            border: 1px solid black;
        }

        .pdf-intro {
            width: 450px;
        }

        .bill-table td,
        .bill-table th {
            border: 1px solid black;
        }


        .bill-table th:first-child,
        .bill-table td:first-child,
        .content-table th:first-child,
        .content-table td:first-child,
        .tax-table td:first-child,
        .tax-table th:first-child {
            border-left: 0 !important;
        }

        .bill-table th:last-child,
        .bill-table td:last-child,
        .content-table th:last-child,
        .content-table td:last-child,
        .tax-table td:last-child,
        .tax-table th:last-child,
        .qr-table td:last-child {
            border-right: 0 !important;
        }

        .total-content {
            border-right: 1px solid black;
        }

        .table-intro,
        .table-intro td {
            border: none !important;
        }

        .after-border-content {
            position:relative;
        }

        .after-border-content::after {
            content:"";
            position:absolute;
            left:0;
            top:0;
            width:1px;
            height:100vh;
            border-left:1px solid black;
        }

        .overflow-hidden {
            overflow:hidden;
        }
    </style>
      @include('company.pdf.sale_pdf.print_custom_font_css')
</head>

<body>
    <div>
        <div wire:loading.class="page-loader">
        </div>
        <div class="main-table w-100">

            {{-- slogan --}}
            @if (($invoiceSetting['show_slogan'] ?? false) && isset($invoiceSetting['slogan']))
                <div class="text-center">
                    <p class="text-center company-address-font-size  border-bottom py-1">{{ $invoiceSetting['slogan'] }}</p>
                </div>
            @endif

            {{-- invoice Type --}}
            <h2 class="text-center fs-14 py-1 border-bottom fw-medium">
                {{ $taxInvoice }} ({#INVOICE_TYPE#})
            </h2>

            {{-- company details --}}
            <table class="w-100 ">
                <tbody>
                    <tr>
                        <td class="head-table vertical-align-baseline {{ !empty($eInvoice) ? 'w-50' : 'w-80' }}">
                            <div class="d-flex align-items-center">
                                @if (isset($currentCompany->company_logo) &&
                                        ($invoiceSetting['logo'] ?? true) &&
                                        $currentCompany->company_logo != asset('images/preview-img.png'))
                                    <img src={{ $currentCompany->company_logo ?? '' }} alt="Logo"
                                        style="object-fit: contain;margin: 0px 3px;" width="100">
                                @endif
                                <div class="pdf-intro">
                                    <h1 class="mt-2 company-name-font-size company-font-customization">{{ strtoupper($currentCompany->trade_name) }}</h1>
                                    @if(($invoiceSetting['prop_details'] ?? true) && isset($customProp))
                                        <p class="mt-1 fw-6 company-address-font-size">{{ $customProp->label_name.' : '.$customProp->label_value }}</p>
                                    @endif
                                    @if ($currentCompany->is_gst_applicable)
                                        <p class="company-address-font-size lh-sm mb-0">
                                            {{ $changeLabel['gstin'] ?? 'GSTIN' }}:
                                            {{ '  ' . $currentCompany->companyTax->gstin ?? null }}
                                        </p>
                                    @endif
                                    <p class="mb-0 company-address-font-size" >
                                        {{ strtoupper($companyBillingAddress->address_1 ?? null) }},
                                        {{ strtoupper($companyBillingAddress->address_2 ?? null) }},
                                        {{ strtoupper(getCityName($companyBillingAddress->city_id ?? null)) }},
                                        {{ strtoupper(getStateName($companyBillingAddress->state_id ?? null)) }},
                                        {{ strtoupper(getCountryName($companyBillingAddress->country_id ?? null)) }},
                                        {{ $companyBillingAddress->pin_code ?? null }}
                                    </p>
                                    @if (($invoiceSetting['mobile_number'] ?? true) || ($invoiceSetting['email'] ?? true))
                                        <p class="company-address-font-size lh-sm mb-0">
                                            @if ($invoiceSetting['mobile_number'] ?? true)
                                                {{ $changeLabel['tel'] ?? 'Tel' }} :
                                                {{ (isset($invoiceSetting['region_code']) ? '+' . $invoiceSetting['region_code'] . ' ' : '') . ($invoiceSetting['alternate_phone'] ?? ($currentCompany->phone ?? null)) }}
                                                {{ isset($invoiceSetting['region_code_2']) && isset($invoiceSetting['alternate_phone_2']) ? ', ' . '+' . $invoiceSetting['region_code_2'] . ' ' . $invoiceSetting['alternate_phone_2'] : (isset($invoiceSetting['alternate_phone_2']) ? ', ' . $invoiceSetting['alternate_phone_2'] : '') }}
                                            @endif
                                            @if (
                                                ($invoiceSetting['mobile_number'] ?? true) &&
                                                    ($invoiceSetting['email'] ?? true) &&
                                                    ($invoiceSetting['alternate_email'] ?? ($currentCompany->user->email ?? null)) != null)
                                                |
                                            @endif
                                            @if ($invoiceSetting['email'] ?? true)
                                                {{ $alternate_email ?? ($currentCompany->user->email ?? null) }}
                                            @endif
                                        </p>
                                    @endif
                                    @foreach (printCustomPDFLabelsForSale() as $key => $customLabel)
                                        <p class="company-address-font-size lh-sm mb-0">
                                            {{ $key ?? null }}: {{ $customLabel ?? null }}
                                        </p>
                                    @endforeach
                                </div>
                            </div>
                        </td>
                        <td class="head-table vertical-align-baseline {{ !empty($eInvoice) ? 'w-50' : 'w-20' }}">
                            <div class="ms-auto qr-blur-section">
                                <div class="me-4 d-flex align-items-center justify-content-between gap-3">
                                    <table class="w-100 ">
                                        <tbody>
                                            @if (!empty($eInvoice))
                                                <tr>
                                                    <td class="header-contents-font-size fw-semibold pe-1 lh-sm vertical-align-top">
                                                        {{ $changeLabel['ack_no'] ?? 'Ack No' }}:
                                                    </td>
                                                    <td class="header-contents-font-size fw-medium word-break lh-sm vertical-align-top">
                                                        {{ !empty($eInvoice) ? $eInvoice->ack_no : '' }}</td>
                                                </tr>
                                                <tr>
                                                    <td
                                                        class="header-contents-font-size fw-semibold pe-1 lh-sm text-nowrap vertical-align-top">
                                                        {{ $changeLabel['ack_date'] ?? 'Ack Date' }}
                                                    </td>
                                                    <td class="header-contents-font-size fw-medium lh-sm  text-nowrap vertical-align-top">
                                                        {{ !empty($eInvoice) ? \Carbon\Carbon::parse($eInvoice->ack_date)->format('d-m-Y') : '' }}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td
                                                        class="header-contents-font-size fw-semibold pe-1 lh-sm text-nowrap vertical-align-top">
                                                        {{ $changeLabel['irn'] ?? 'IRN' }}:</td>
                                                    <td class="header-contents-font-size fw-medium word-break lh-sm vertical-align-top">
                                                        {{ !empty($eInvoice) ? $eInvoice->irn : '' }}
                                                    </td>
                                                </tr>
                                            @endif
                                            <tr>
                                                <td class="header-contents-font-size fw-semibold pe-1 lh-sm vertical-align-top">
                                                    {{ $changeLabel['invoice_number'] ?? 'Invoice no' }}:</td>
                                                <td class="header-contents-font-size fw-medium lh-sm text-nowrap vertical-align-top">
                                                    {{ $transaction->full_invoice_number }}</td>
                                            </tr>
                                            <tr>
                                                <td class="header-contents-font-size fw-semibold pe-1 lh-sm text-nowrap vertical-align-top">
                                                    {{ $changeLabel['invoice_date'] ?? 'Invoice Date' }}:</td>
                                                <td class="header-contents-font-size fw-medium lh-sm text-nowrap vertical-align-top">
                                                    {{ $invoiceDate }}</td>
                                            </tr>
                                            @if(isset($originalInvoiceNumber) && !empty($originalInvoiceNumber) && isset($originalInvoiceDate) && !empty($originalInvoiceDate))
                                            <tr>
                                                <td class="header-contents-font-size fw-semibold pe-1 lh-sm vertical-align-top">
                                                    Original Invoice No::</td>
                                                <td class="header-contents-font-size fw-medium lh-sm text-nowrap vertical-align-top">
                                                    {{ Illuminate\Support\Str::limit($originalInvoiceNumber, 20, '...')  }}</td>
                                            </tr>
                                            <tr>
                                                <td class="header-contents-font-size fw-semibold pe-1 lh-sm text-nowrap vertical-align-top">
                                                    Original Invoice Date:</td>
                                                <td class="header-contents-font-size fw-medium lh-sm text-nowrap vertical-align-top">
                                                    {{ isset($originalInvoiceDate) ? \Carbon\Carbon::parse($originalInvoiceDate)->format('d-m-Y') : null }}</td>
                                            </tr>
                                            @endif
                                            @if (isset($deliveryChallanInvoiceNumber) && !empty($deliveryChallanInvoiceNumber))
                                                <tr>
                                                    <td
                                                        class="header-contents-font-size fw-semibold pe-1 lh-sm text-nowrap vertical-align-top">
                                                        {{ $changeLabel['delivery_challan'] ?? 'Delivery Challan No' }}:
                                                    </td>
                                                    <td class="header-contents-font-size fw-medium lh-sm text-nowrap vertical-align-top">
                                                        {{ Illuminate\Support\Str::limit($deliveryChallanInvoiceNumber, 20, '...') }}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td
                                                        class="header-contents-font-size fw-semibold pe-1 lh-sm text-nowrap vertical-align-top">
                                                        {{ $changeLabel['delivery_challan'] ?? 'Delivery Date' }}:
                                                    </td>
                                                    <td class="header-contents-font-size fw-medium lh-sm text-nowrap vertical-align-top">
                                                        {{ $deliveryChallanInvoiceDate }}
                                                    </td>
                                                </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                    @if (!empty($eInvoice))
                                        <div class="qr-blur-img ms-auto">
                                            @php
                                                $qrCode = (string) QrCode::format('svg')
                                                    ->margin(0)
                                                    ->size(200)
                                                    ->generate($eInvoice->signed_qr_code);
                                            @endphp
                                            <img src="data:image/svg+xml;base64,{{ base64_encode($qrCode) }}"
                                                width="100" height="100" style="object-fit: contain;" />
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>

            {{-- customer Detail --}}
            <table class="w-100">
                <tbody>
                    <tr>
                        <td class="w-100">
                            <div>
                                <table class="bill-table w-100">
                                    <thead>
                                        <tr>
                                            <th class="px-2">
                                                <p class="fw-bold header-labels-font-size text-start text-primary py-1">
                                                    {{ $changeLabel['bill_to'] ?? 'Bill to' }}:
                                                </p>
                                            </th>
                                            @if ($invoiceSetting['ship_to_details'] ?? true)
                                                <th class="px-2">
                                                    <p class="fw-bold header-labels-font-size text-start text-primary py-1">
                                                        {{ $changeLabel['ship_to'] ?? 'Ship to' }}:
                                                    </p>
                                                </th>
                                            @endif
                                            @if (
                                                ($invoiceSetting['transport_details'] ?? true) ||
                                                    isset($eWayBill) ||
                                                    !empty($transaction->transporter_vehicle_number))
                                                <th class="px-2">
                                                    <p class="fw-bold header-labels-font-size text-start text-primary py-1">
                                                        Transport Details:
                                                    </p>
                                                </th>
                                            @endif
                                            <th class="px-2">
                                                <p class="fw-bold header-labels-font-size text-start text-primary py-1">
                                                    Other Details:
                                                </p>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="w-25 px-2 vertical-align-top">
                                                <div>
                                                    <p class="header-contents-font-size mb-1 lh-sm lh-sm fw-semibold">
                                                        {{ strtoupper($customerDetail->name) }}</p>
                                                    <p class="header-contents-font-size mb-1">
                                                        @if ($showGst)
                                                            <p class="header-contents-font-size">
                                                                <span class="fw-medium header-contents-font-size">GSTIN</span>
                                                                : {{ $transaction->gstin ?? $customerDetail->model->gstin ?? null }}
                                                            </p>
                                                        @endif
                                                        @if (!empty($panNumber) && $showPanNumber)
                                                            <p class="header-contents-font-size">
                                                                PAN: {{ $panNumber ?? null }}
                                                            </p>
                                                        @endif
                                                    </p>
                                                    @if (isset($billingAddress))
                                                        <p class="header-contents-font-size mb-1  lh-sm">
                                                            @if (isset($billingAddress) && $billingAddress->address_1 != null)
                                                                {{ strtoupper($billingAddress->address_1) }}
                                                            @endif
                                                            @if (isset($billingAddress) && $billingAddress->address_2 != null)
                                                                {{ strtoupper($billingAddress->address_2) }},
                                                            @endif
                                                            {{isset($billingAddress) && $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                                                            {{isset($billingAddress) && $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                                                            {{ strtoupper(getCountryName($billingAddress->country_id ?? null)) }},
                                                            {{ $billingAddress->pin_code ?? null }}
                                                        </p>
                                                    @endif
                                                    <div class="header-contents-font-size d-flex lh-sm">
                                                        <p class="header-contents-font-size">
                                                            @if (!empty($transaction->party_phone_number))
                                                                Contact No:
                                                                    +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                                                            @elseif (!empty($customerDetail->model->phone_1))
                                                                Contact No:
                                                                    +{{ $customerDetail->model->region_code_1 ?? '' }} {{ $customerDetail->model->phone_1 ?? null }}
                                                            @endif
                                                            @if (!empty($customerDetail->model->phone_2) && ($customerDetail->model->phone_1 !== $customerDetail->model->phone_2))
                                                                {{ empty($customerDetail->model->phone_1) && empty($transaction->party_phone_number) ? 'Contact No:' : "|" }}
                                                                <span class="text-nowrap header-contents-font-size">
                                                                    +{{ $customerDetail->model->region_code_2 ?? '' }} {{ $customerDetail->model->phone_2 ?? null }}
                                                                </span>
                                                            @endif
                                                        </p>
                                                    </div>
                                                    @if (!empty($customerDetail->model->person_email))
                                                        <p class="header-contents-font-size">
                                                            Email: {{ $customerDetail->model->person_email ?? null }}
                                                        </p>
                                                    @endif
                                                </div>
                                            </td>
                                            @if ($invoiceSetting['ship_to_details'] ?? true)
                                                <td class="w-25 px-2 vertical-align-top">
                                                    <div>
                                                        <p class="header-contents-font-size mb-1 lh-sm lh-sm fw-semibold">
                                                            {{ strtoupper($customerDetail->name) }}</p>
                                                        <p class="header-contents-font-size mb-1">
                                                            @if ($isCompanyGstApplicable && !empty($transaction->shipping_gstin))
                                                                <p class="header-contents-font-size">
                                                                    <span class="fw-medium header-contents-font-size">GSTIN</span>
                                                                    : {{ $transaction->shipping_gstin ?? null }}
                                                                </p>
                                                            @endif
                                                            @if (!empty($panNumber) && $showPanNumber)
                                                                <p class="header-contents-font-size">
                                                                    PAN: {{ $panNumber ?? null }}
                                                                </p>
                                                            @endif
                                                        </p>

                                                        <p class="header-contents-font-size mb-1  lh-sm">
                                                            @if (isset($shippingAddress->address_1))
                                                                {{ strtoupper($shippingAddress->address_1) }},
                                                            @endif
                                                            @if (isset($shippingAddress->address_2))
                                                                {{ strtoupper($shippingAddress->address_2) }},
                                                            @endif
                                                            {{ isset($shippingAddress->city_id) ? strtoupper(getCityName($shippingAddress->city_id)) . ',' : '' }}
                                                            {{ isset($shippingAddress->state_id) ? strtoupper(getStateName($shippingAddress->state_id)) . ',' : '' }}
                                                            {{ isset($shippingAddress->country_id) ? strtoupper(getCountryName($shippingAddress->country_id)) : ''  }},
                                                            {{ $shippingAddress->pin_code ?? null }}
                                                        </p>
                                                        <div class="header-contents-font-size d-flex lh-sm">
                                                            <p class="header-contents-font-size">
                                                                @if (!empty($transaction->party_phone_number))
                                                                    Contact No:
                                                                        +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                                                                @endif
                                                            </p>
                                                        </div>
                                                    </div>
                                                </td>
                                            @endif
                                            @if (
                                                ($invoiceSetting['transport_details'] ?? true) ||
                                                    isset($eWayBill) ||
                                                    !empty($transaction->transporter_vehicle_number))
                                                <td class="w-25 px-2 vertical-align-top">
                                                    <div class="">
                                                        <table class="table-intro">
                                                            <tbody>
                                                                @if ($invoiceSetting['transport_details'] ?? true)
                                                                    <tr>
                                                                        <td class="vertical-align-baseline">
                                                                            <p
                                                                                class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                {{ $changeLabel['transport_name'] ?? 'Transport Name' }}
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <div
                                                                                class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                <span class="header-contents-font-size">:</span>
                                                                                <p class="ps-1 header-contents-font-size">
                                                                                    {{ $transaction->transport->transporter_name ?? '' }}
                                                                                </p>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>
                                                                            <p class="header-contents-font-size mb-1 pe-3 lh-sm lh-sm">
                                                                                {{ $changeLabel['document_no'] ?? 'Document No' }}
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <div
                                                                                class="header-contents-font-size d-flex word-break lh-sm lh-sm">
                                                                                <span class="header-contents-font-size">:</span>
                                                                                <p class="ps-1 header-contents-font-size">
                                                                                    {{ $transaction->transporter_document_number ?? '' }}
                                                                                </p>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>
                                                                            <p class="header-contents-font-size mb-1 pe-3 lh-sm lh-sm">
                                                                                {{ $changeLabel['document_date'] ?? 'Document Date' }}
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <div
                                                                                class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                <span class="header-contents-font-size">:</span>
                                                                                <p class="ps-1 header-contents-font-size">
                                                                                    {{ !empty($transaction->transporter_document_date) ? \Carbon\Carbon::parse($transaction->transporter_document_date)->format('d-m-Y') : '' }}
                                                                                </p>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                @endif
                                                                @if (isset($eWayBill) || !empty($transaction->transporter_vehicle_number) || (isset($transaction->eway_bill_number) || isset($transaction->eway_bill_date)))
                                                                    @if (isset($eWayBill) || (isset($transaction->eway_bill_number) || isset($transaction->eway_bill_date)))
                                                                        <tr>
                                                                            <td>
                                                                                <p class="header-contents-font-size mb-1 pe-3 lh-sm lh-sm3">
                                                                                    {{ $changeLabel['e_way_bill_no'] ?? 'E-way Bill No' }}:
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <div
                                                                                    class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                    <span class="header-contents-font-size">:</span>
                                                                                    <p class="ps-1 header-contents-font-size">
                                                                                        {{ $eWayBill?->eway_bill_no ?? $transaction->eway_bill_number ?? null }}
                                                                                    </p>
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>
                                                                                <p class="header-contents-font-size mb-1 pe-3 lh-sm lh-sm">
                                                                                    {{ $changeLabel['e_way_bill_date'] ?? 'E-way Bill Date' }}
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <div
                                                                                    class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                    <span class="header-contents-font-size">:</span>
                                                                                    <p class="ps-1 header-contents-font-size">
                                                                                        {{ $eWayBill?->eway_bill_date ?? $transaction->eway_bill_date ?? null  }}
                                                                                    </p>
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                    @endif
                                                                    @if (!empty($transaction->transporter_vehicle_number))
                                                                        <tr>
                                                                            <td>
                                                                                <p class="header-contents-font-size pe-3 lh-sm lh-sm">
                                                                                    {{ $changeLabel['transport_vehicle_number'] ?? 'Vehicle No' }}
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <div
                                                                                    class="header-contents-font-size d-flex word-break lh-sm lh-sm">
                                                                                    <span class="header-contents-font-size">:</span>
                                                                                    <p class="ps-1 header-contents-font-size">
                                                                                        {{ $transaction->transporter_vehicle_number ?? '' }}
                                                                                    </p>
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                    @endif
                                                                @endif
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </td>
                                            @endif
                                            <td class="w-25 px-2 pe-0 vertical-align-top">
                                                <div class="">
                                                    <table class="table-intro">
                                                        <tbody>
                                                            @if ($invoiceSetting['broker_details'] ?? true)
                                                                <tr>
                                                                    <td>
                                                                        <p
                                                                            class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                            {{ $changeLabel['broker'] ?? 'Broker' }}
                                                                        </p>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                            <span class="header-contents-font-size">:</span>
                                                                            <p class="ps-1 header-contents-font-size">
                                                                                {{ $transaction->brokerDetails->broker_name ?? '' }}
                                                                            </p>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                @if($isCompanyGstApplicable)
                                                                <tr>
                                                                    <td>
                                                                        <p
                                                                            class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                            {{ $changeLabel['broker_gstin'] ?? 'GSTIN' }}
                                                                        </p>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                            <span class="header-contents-font-size">:</span>
                                                                            <p class="ps-1 header-contents-font-size">
                                                                                {{ $transaction->brokerDetails->gstin ?? '' }}
                                                                            </p>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                @endif
                                                            @endif

                                                            @if($invoiceSetting['show_due_date'] ?? true)
                                                            <tr>
                                                                <td>
                                                                    <p
                                                                        class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                        {{ $changeLabel['due_date'] ?? 'Due Date' }}
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <div
                                                                        class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                        <span class="header-contents-font-size">:</span>
                                                                        <p class="ps-1 header-contents-font-size">{{ $dueDate }}</p>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            @endif
                                                            @if($invoiceSetting['show_credit_period'] ?? true)
                                                            <tr>
                                                                <td>
                                                                    <p
                                                                        class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                        {{ $changeLabel['credit_period'] ?? 'Credit Period' }}
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <div
                                                                        class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                        <span class="header-contents-font-size">:</span>
                                                                        <p class="ps-1 header-contents-font-size">{{ $creditPeriod }}</p>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            @endif
                                                            @if ($invoiceSetting['po_number'] ?? true)
                                                                <tr>
                                                                    <td>
                                                                        <p
                                                                            class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                            {{ $changeLabel['po_number_label'] ?? 'PO No' }}
                                                                        </p>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                            <span class="header-contents-font-size">:</span>
                                                                            <p class="ps-1 header-contents-font-size">
                                                                                {{ $transaction->po_no }}
                                                                            </p>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                @endif
                                                            @if ($invoiceSetting['show_po_date'] ?? true)
                                                                <tr>
                                                                    <td>
                                                                        <p
                                                                            class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                            {{ $changeLabel['po_date'] ?? 'PO Date' }}
                                                                        </p>
                                                                    </td>
                                                                    <td>
                                                                        <div
                                                                            class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                            <span class="header-contents-font-size">:</span>
                                                                            <p class="ps-1 header-contents-font-size">
                                                                                {{ isset($transaction->po_date) ? \Carbon\Carbon::parse($transaction->po_date)->format('d-m-Y') : null }}
                                                                            </p>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            @endif
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>

            {{-- Custom Fields Section Start --}}
            @if (count($customFieldValues) > 0)
                @php
                    $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
                @endphp
                <table cellpadding="0" class="w-100">
                    @foreach ($customFields->chunk(3) as $chunk)
                        <tr class="border-bottom">
                            @foreach ($chunk as $customField)
                                <td class="{{ $loop->last ? '' : 'border-right' }}" style="padding: 6px 8px; width:150px; ">
                                    <span style="font-weight: bold;">{{ $customField['label_name'] ?? '' }}</span> : {{ $customField['value'] ?? '' }}
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </table>
            @endif
            {{-- Custom Fields Section End --}}

            <div class="item-table-height">
                <table class="content-table w-100">
                    <thead>
                        <tr class="vertical-align-baseline">
                            @foreach ($rearrangeItems['headings'] as $headings)
                                @if($headings['is_show_in_print'])
                                    <th class="table-headings-font-size fw-medium lh-sm fw-bold text-nowrap {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }} {{ $headings['class'] }}"
                                        style="padding: 6px 8px; font-weight: bold;">
                                        {{ $headings['name'] }}
                                    </th>
                                @endif
                            @endforeach
                        </tr>
                    </thead>
                    <tbody>
                            @foreach ($rearrangeItems['detail'] as $key => $items)
                                <tr class="data1 vertical-align-baseline">
                                    @foreach ($items as  $key => $item)
                                        @if($item['is_show_in_print'])
                                            <td class="table-contents-font-size p-1 word-break lh-sm fw-medium w-5 text-nowrap {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }} {{ ($key == 'item_name') ? 'min-width-150' : 'text-center'}}"
                                                style="padding: 4px 8px 0 8px; {{ ($key == 'primary_quantity' || $key == 'secondary_quantity') ? 'white-space: nowrap;' : '' }} ">
                                                {{ $item['value'] }}
                                                @if($key == 'item_name')
                                                    @if($item['show_sku'])
                                                        <p class="description-font-size">Item Code:
                                                            {{ $item['sku'] ?? null }}</p>
                                                    @endif
                                                    @if($item['show_consolidating_items'])
                                                        <p style="word-break: break-word; " class="description-font-size">
                                                            {!! $item['consolidating_items'] !!}
                                                        </p>
                                                    @endif
                                                    @if($item['show_additional_description'])
                                                        <p style="word-break: break-word;" class="description-font-size">
                                                            {!! $item['additional_description'] !!}
                                                        </p>
                                                    @endif
                                                    @if($item['show_item_image'])
                                                        <div><img src="{{ $item['item_image'] }}" width="60"  height="60" style="margin-top: 4px"></div>
                                                    @endif
                                                @endif
                                            </td>
                                        @endif
                                    @endforeach
                                </tr>
                            @endforeach
                    </tbody>
                    <tfoot>
                        <tr>
                            @foreach ($rearrangeItems['footer'] as $key => $footer)
                                @if($footer['is_show_in_print'])
                                    <td class="table-headings-font-size fw-medium lh-sm fw-bold text-nowrap py-1 px-2 {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }}  fw-6 {{ ($key == 'item_name') ? 'min-width-150 ps-2' : 'text-center'}}"
                                    style="">{{ $footer['value'] }}</td>
                                @endif
                            @endforeach
                        </tr>
                    </tfoot>
                </table>
            </div>

            <table class="w-100 qr-table min-h-100">
                <tbody>
                    <tr>
                        @if (($isCompanyGstApplicable && ($invoiceSetting['hsn_summary'] ?? true)) ||
                                ($invoiceSetting['qr_code'] ?? true) ||
                                ($invoiceSetting['bank_details'] ?? true) ||
                                ($invoiceSetting['signature'] ?? true) ||
                                ($showPrintSettings['show_sale_authorized_signatory'] ?? true))
                            <td class="w-100 vertical-align-baseline min-h-100 {{ ($showPrintSettings['show_sale_in_words'] ?? true) || (($showPrintSettings['show_sale_narration'] ?? true) && $transaction->narration) || (($showPrintSettings['show_sale_terms_and_conditions'] ?? true) && $transaction->term_and_condition) ? 'border-right'  : 'border-right-0' }}">
                                <div class="d-flex flex-column justify-content-between min-h-100">
                                    @if ($isCompanyGstApplicable && !empty($checkHsnCodeExist) && ($invoiceSetting['hsn_summary'] ?? true))
                                        <div class="mb-30px">
                                            <table class="w-100 tax-table">
                                                <thead>
                                                    <tr>
                                                        <th
                                                            class="footer-headings-font-size ps-3 px-2 py-1 fw-medium vertical-align-baseline text-center lh-sm fw-bold">
                                                            SN</th>
                                                        <th
                                                            class="footer-headings-font-size px-2 py-1 fw-medium vertical-align-baseline text-center lh-sm fw-bold">
                                                            HSN<span class="table-headings-font-size">/</span>SAC</th>
                                                        <th
                                                            class="footer-headings-font-size px-2 py-1 fw-medium vertical-align-baseline text-center lh-sm fw-bold text-nowrap">
                                                            Taxable Amount</th>
                                                        <th
                                                            class="footer-headings-font-size px-2 py-1 fw-medium vertical-align-baseline text-center lh-sm fw-bold">
                                                            GST (%)
                                                        </th>
                                                        @if ($cgst != 0.0)
                                                            <th
                                                                class="footer-headings-font-size px-2 py-1 fw-medium vertical-align-baseline text-center lh-sm fw-bold">
                                                                CGST
                                                            </th>
                                                        @endif
                                                        @if ($sgst != 0.0)
                                                            <th
                                                                class="footer-headings-font-size px-2 py-1 fw-medium vertical-align-baseline text-center lh-sm fw-bold">
                                                                SGST
                                                            </th>
                                                        @endif
                                                        @if ($igst != 0.0)
                                                            <th
                                                                class="footer-headings-font-size px-2 py-1 fw-medium vertical-align-baseline text-center lh-sm fw-bold">
                                                                IGST
                                                            </th>
                                                        @endif
                                                        <th
                                                            class="footer-headings-font-size px-2 py-1 fw-medium vertical-align-baseline text-center lh-sm fw-bold text-nowrap">
                                                            Total Tax</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @php
                                                        $uniquekey = 1;
                                                    @endphp
                                                    @foreach ($checkHsnCodeExist as $key => $item)
                                                        @foreach ($item as $hsnCode => $data)
                                                            <tr>
                                                                <td class="py-1 footer-contents-font-size text-center fw-medium lh-sm">
                                                                    {{ $uniquekey++ }}</td>
                                                                <td class="py-1 footer-contents-font-size text-center fw-medium lh-sm">
                                                                    {{ !empty($hsnCode) ? $hsnCode : '-' }}</td>
                                                                <td class="py-1 footer-contents-font-size text-center fw-medium lh-sm text-nowrap">
                                                                    {{ $pdfSymbol . getCurrencyFormat(round($checkTAXtExist[$key]['taxableValue'][$hsnCode], getCompanyFixedDigitNumber()) ?? 0) }}
                                                                </td>
                                                                <td class="py-1 footer-contents-font-size text-center fw-medium lh-sm">
                                                                    {{ !empty($key) ? $key : '-' }}</td>
                                                                @if ($cgst != 0.0)
                                                                    <td class="py-1 footer-contents-font-size text-center fw-medium lh-sm px-2 text-nowrap">
                                                                        {{ $pdfSymbol . getCurrencyFormat(round($checkTAXtExist[$key]['cgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                                                    </td>
                                                                @endif
                                                                @if ($sgst != 0.0)
                                                                    <td class="py-1 footer-contents-font-size text-center fw-medium lh-sm px-2 text-nowrap">
                                                                        {{ $pdfSymbol . getCurrencyFormat(round($checkTAXtExist[$key]['sgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                                                    </td>
                                                                @endif
                                                                @if ($igst != 0.0)
                                                                    <td class="py-1 footer-contents-font-size text-center fw-medium lh-sm px-2 text-nowrap">
                                                                        {{ $pdfSymbol . getCurrencyFormat(round($checkTAXtExist[$key]['igst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                                                    </td>
                                                                @endif
                                                                @php
                                                                    $totalTax =
                                                                        round(
                                                                            $checkTAXtExist[$key]['cgst'][$hsnCode],
                                                                            getCompanyFixedDigitNumber(),
                                                                        ) +
                                                                        round(
                                                                            $checkTAXtExist[$key]['sgst'][$hsnCode],
                                                                            getCompanyFixedDigitNumber(),
                                                                        ) +
                                                                        round(
                                                                            $checkTAXtExist[$key]['igst'][$hsnCode],
                                                                            getCompanyFixedDigitNumber(),
                                                                        );
                                                                @endphp
                                                                <td class="py-1 footer-contents-font-size text-center fw-medium lh-sm px-2">
                                                                    {{ $pdfSymbol . getCurrencyFormat(round($totalTax ?? 0, getCompanyFixedDigitNumber())) }}
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    @endforeach
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                        <td class="py-1 footer-headings-font-size text-center fw-medium lh-sm"></td>
                                                        <td class="py-1 footer-headings-font-size text-center fw-medium lh-sm">Total
                                                        </td>
                                                        <td class="py-1 footer-headings-font-size text-center fw-medium lh-sm text-nowrap">
                                                            {{ $pdfSymbol . getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber())) }}
                                                        </td>
                                                        <td class="py-1 footer-headings-font-size text-center fw-medium lh-sm"></td>
                                                        @if ($cgst != 0.0)
                                                            <td class="py-1 footer-headings-font-size text-center fw-medium lh-sm text-nowrap">
                                                                {{ $pdfSymbol . getCurrencyFormat(round($cgst, getCompanyFixedDigitNumber())) }}
                                                            </td>
                                                        @endif
                                                        @if ($sgst != 0.0)
                                                            <td class="py-1 footer-headings-font-size text-center fw-medium lh-sm text-nowrap">
                                                                {{ $pdfSymbol . getCurrencyFormat(round($sgst, getCompanyFixedDigitNumber())) }}
                                                            </td>
                                                        @endif
                                                        @if ($igst != 0.0)
                                                            <td class="py-1 footer-headings-font-size text-center fw-medium lh-sm text-nowrap">
                                                                {{ $pdfSymbol . getCurrencyFormat(round($igst, getCompanyFixedDigitNumber())) }}
                                                            </td>
                                                        @endif
                                                        @php
                                                            $grandTotalTax = $cgst + $sgst + $igst;
                                                        @endphp
                                                        <td class="py-1 footer-headings-font-size text-center fw-medium lh-sm text-nowrap">
                                                            {{ $pdfSymbol . getCurrencyFormat(round($grandTotalTax, getCompanyFixedDigitNumber())) }}
                                                        </td>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    @endif
                                    <div class="d-flex px-2 mb-3 mt-2">
                                        <div class="bank-code d-flex">
                                            @if (isset($bankDetail->upi_id))
                                                @if ($invoiceSetting['qr_code'] ?? true)
                                                    <div class="qr-code text-center w-100"
                                                        style="margin-top: 10px; padding-right: 10px;white-space: nowrap;">
                                                        <img src="data:image/svg+xml;base64,{{ base64_encode(generateUPIQRCode($bankDetail->upi_id, $transaction->due_amount)) }}"
                                                            width="80" height="80" />
                                                        <p class="footer-contents-font-size" style="margin-top: 8px; font-size: 12px;">
                                                            {{ $bankDetail->upi_id }}
                                                        </p>
                                                    </div>
                                                @endif
                                            @endif
                                            @if (($invoiceSetting['bank_details'] ?? true) && !empty($bankDetail))
                                                <div class="bank-detali">
                                                    <p class="footer-headings-font-size  mb-1 fw-semibold lh-sm">Bank Details</p>
                                                    <p class="footer-contents-font-size  mb-1 lh-sm">
                                                        <span class="fw-semibold footer-headings-font-size">Bank:</span>
                                                        <span class="fw-semibold footer-contents-font-size">
                                                            {{ !empty($bankDetail) ? $bankDetail->bank_name : null }}
                                                        </span>
                                                    </p>
                                                    <p class="footer-headings-font-size  mb-1 lh-sm">
                                                        <span class="fw-semibold footer-headings-font-size">Account no. :</span>
                                                        <span class="fw-semibold footer-contents-font-size">
                                                        {{ $accountNumber }}
                                                        </span>
                                                    </p>
                                                    @if (!empty($bankDetail) && $bankDetail->swift_code != null)
                                                        <p class="footer-contents-font-size  mb-1 lh-sm">
                                                            <span class="fw-semibold footer-headings-font-size">Swift Code:
                                                            </span>
                                                            <span class="fw-semibold footer-contents-font-size">
                                                            {{ $bankDetail->swift_code }}
                                                            </span>
                                                        </p>
                                                    @endif
                                                    <p class="footer-contents-font-size  mb-1 lh-sm">
                                                        <span class="fw-semibold footer-headings-font-size">IFSC :
                                                        </span>
                                                        <span class="fw-semibold footer-contents-font-size">
                                                        {{ !empty($bankDetail) ? $bankDetail->ifsc_code : null }}
                                                    </span>
                                                    </p>
                                                    @if (isset($bankDetail->account_holder_name))
                                                        <p class="footer-contents-font-size mb-1 lh-sm">
                                                            <span class="fw-semibold footer-headings-font-size">A/C Name:</span>
                                                            <span class="fw-semibold footer-contents-font-size">{{ $bankDetail->account_holder_name }}</span>
                                                        </p>
                                                    @endif
                                                    <p class="footer-contents-font-size  mb-1 lh-sm">
                                                        <span class="fw-semibold footer-headings-font-size">Branch :
                                                        </span>
                                                        <span class="fw-semibold footer-contents-font-size">{{ $branchName }}
                                                        </span>
                                                        </p>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="sign-section ms-auto">
                                            @if ($showPrintSettings['show_sale_authorized_signatory'] ?? true)
                                                <p class="footer-contents-font-size fw-medium text-center lh-sm mb-1">
                                                    For, {{ strtoupper($currentCompany->trade_name) }}
                                                </p>
                                            @endif
                                            <div class="sign d-flex align-items-center p-2">
                                                <div class="d-flex align-items-center">
                                                    @if (($invoiceSetting['signature'] ?? false) && $currentCompany->company_signature != asset('images/preview-img.png'))
                                                        <img src="{{ $currentCompany->company_signature ?? null }}"
                                                            alt="company_signature"
                                                            style="max-width:100%; height:100%;  object-fit:contain; margin-left:auto;">
                                                    @endif
                                                </div>
                                            </div>
                                            @if ($showPrintSettings['show_sale_authorized_signatory'] ?? true)
                                                <p class="footer-contents-font-size fw-medium text-center lh-sm">
                                                    {{ $changeLabel['authorized_signatory'] ?? 'Authorized Signatory' }}
                                                </p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </td>
                        @endif
                        @php
                            if (empty($addLess)) {
                                $total = $transaction->grand_total;
                            } else {
                                $addLessSum = array_sum(array_column($addLess, 'amount'));
                                $total =  $transaction->grand_total - $addLessSum;
                                $addLessSumTotal = collect($addLess)->where('is_show_in_print',1)->sum('amount');
                                $total = $total + $addLessSumTotal;
                            }
                        @endphp
                        @if (($showPrintSettings['show_sale_in_words'] ?? true) || (($showPrintSettings['show_sale_narration'] ?? true) && $transaction->narration) || (($showPrintSettings['show_sale_terms_and_conditions'] ?? true) && $transaction->term_and_condition))
                            <td class="vertical-align-top border-right-0 max-width-400px">
                                <div class="p-2 total-head">
                                    @if ($showPrintSettings['show_sale_in_words'] ?? true)
                                        <p class="table-contents-font-size mb-2 lh-sm fw-medium fw-bold">
                                            {{ $changeLabel['in_words'] ?? 'In Words' }}:
                                        </p>
                                        <p class="table-contents-font-size mb-3 money fw-bold text-primary lh-sm">
                                            {{ getAmountToWord($total ?? '0.0') }} Only
                                        </p>
                                    @endif
                                    @if ($transaction->term_and_condition || $transaction->narration)
                                        @if ($showPrintSettings['show_sale_narration'] ?? true)
                                            @if ($transaction->narration)
                                                <p class="note-font-size fw-medium mb-2 lh-sm fw-bold">
                                                    {{ $changeLabel['narration'] ?? 'Note' }}:
                                                </p>
                                                <p class="note-font-size mb-2 lh-sm">
                                                    {!! nl2br($transaction->narration) !!}
                                                </p>
                                            @endif
                                        @endif
                                        @if ($showPrintSettings['show_sale_terms_and_conditions'] ?? true)
                                            @if ($transaction->term_and_condition)
                                                <p class="fw-medium terms-and-conditions-font-size mb-2 lh-sm fw-bold">
                                                    {{ $changeLabel['terms_and_conditions'] ?? 'Terms and Conditions' }}:
                                                </p>
                                                <p class="terms-and-conditions-font-size  mb-2 lh-sm">
                                                    {!! nl2br($transaction->term_and_condition) !!}
                                                </p>
                                            @endif
                                        @endif
                                    @endif
                                </div>
                                @if($invoiceSetting['show_payment_status'] ?? false)
                                @if($transaction->payment_status == 'Partially Unpaid')
                           <div style="display: flex; justify-content:center; align-items:center;">
                             <div style="border:2px solid #4f158c !important; width:fit-content; margin:8px auto; padding:4px 8px; display:inline-block;">
                                <h5 class="text-primary text-center mb-0" style="font-weight:700; font-size:14px;"> PARTLY</br>
                                 PAID</h5>
                            </div>
                           </div>
                        @elseif ($transaction->payment_status == 'Paid')
                            <div class="flex-grow-1">
                                <div style="display: flex; justify-content:center; align-items:center;">
                                <div
                                    style="padding:2px; width:fit-content; background: linear-gradient(to top,#43ad52,#a6d052); margin:8px auto; display:inline;">
                                    <div style="background-color: white; padding:4px 8px;">
                                        <h4 class="text-center"
                                            style="font-weight:700; font-size:14px; background: -webkit-linear-gradient(#a6d052, #43ad52); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                                            PAID</h4>
                                    </div>
                                </div>
                                </div>
                            </div>
                        @elseif($transaction->payment_status == 'Unpaid')
                            <div style="display: flex; justify-content:center; align-items:center;" class="flex-grow-1">
                                <div style="padding:2px; width:fit-content; background: linear-gradient(to top,#801520,#c30a17); margin:8px auto; display:inline;">
                                    <div style="background-color: white; padding:4px 8px;">
                                        <h4 class="text-center"
                                            style="font-weight:700; font-size:14px; background: -webkit-linear-gradient(#c30a17, #801520); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                                            UNPAID</h4>
                                    </div>
                                </div>
                            </div>
                        @endif
                        @endif
                            </td>
                        @endif
                        <td class="vertical-align-top h-100 max-width-275px overflow-hidden">
                            <div class="total-head py-2 max-width-275px ms-auto h-100 after-border-content">
                                <table class="w-100 total-table">
                                    <tbody>
                                        @foreach ($additionalCharges as $additionalCharge)
                                            <tr class="{{ $loop->last ? 'border-bottom' : '' }}">
                                                <td>
                                                    <p class="table-contents-font-size mb-1 fw-medium text-start lh-sm px-2">
                                                        {{ $additionalCharge['ledger_name'] }}:
                                                    </p>
                                                </td>
                                                <td>
                                                    <p class="table-contents-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                        {{ $pdfSymbol . getCurrencyFormat($additionalCharge['amount'] ?? '0.0') }}
                                                    </p>
                                                </td>
                                            </tr>
                                        @endforeach
                                        <tr>
                                            <td>
                                                <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                    {{ $isCompanyGstApplicable ? $changeLabel['sub_total'] ?? 'Taxable Value' : $changeLabel['sub_total'] ?? 'Sub Total' }}:
                                                </p>
                                            </td>
                                            <td>
                                                <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                    {{ $pdfSymbol . getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber())) }}
                                                </p>
                                            </td>
                                        </tr>
                                        @if ($isCompanyGstApplicable)
                                            @if ($transaction->cgst != 0)
                                                <tr>
                                                    <td>
                                                        <p
                                                            class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                            {{ $changeLabel['cgst'] ?? 'CGST' }}:
                                                        </p>
                                                    </td>
                                                    <td>
                                                        <p
                                                            class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                            {{ $pdfSymbol . getCurrencyFormat(round($transaction->cgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                        </p>
                                                    </td>
                                                </tr>
                                            @endif
                                            @if ($transaction->sgst != 0)
                                                <tr>
                                                    <td>
                                                        <p
                                                            class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                            {{ $changeLabel['sgst'] ?? 'SGST' }}:
                                                        </p>
                                                    </td>
                                                    <td>
                                                        <p
                                                            class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                            {{ $pdfSymbol . getCurrencyFormat(round($transaction->sgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                        </p>
                                                    </td>
                                                </tr>
                                            @endif
                                            @if ($transaction->igst != 0)
                                                <tr>
                                                    <td>
                                                        <p
                                                            class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                            {{ $changeLabel['igst'] ?? 'IGST' }}:
                                                        </p>
                                                    </td>
                                                    <td>
                                                        <p
                                                            class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                            {{ $pdfSymbol . getCurrencyFormat(round($transaction->igst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                        </p>
                                                    </td>
                                                </tr>
                                            @endif
                                        @endif
                                        @if ($transaction->tcs_amount != 0)
                                            <tr>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                        {{ $changeLabel['tcs'] ?? 'TCS' }}:
                                                    </p>
                                                </td>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                        {{ $pdfSymbol . getCurrencyFormat($transaction->tcs_amount ?? '0.0') }}
                                                    </p>
                                                </td>
                                            </tr>
                                        @endif
                                        @if ($transaction->cess != 0)
                                            <tr>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                        {{ $changeLabel['cess'] ?? 'Cess' }}:
                                                    </p>
                                                </td>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                        {{ $pdfSymbol . getCurrencyFormat($transaction->cess ?? '0.0') }}
                                                    </p>
                                                </td>
                                            </tr>
                                        @endif
                                        <tr>
                                            <td>
                                                <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                    {{ $changeLabel['round_off'] ?? 'Round off' }}:
                                                </p>
                                            </td>
                                            <td>
                                                <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                    {{ $pdfSymbol . getCurrencyFormat($transaction->rounding_amount ?? '0.0') }}
                                                </p>
                                            </td>
                                        </tr>

                                    @foreach (collect($addLess)->where('is_show_in_print',1) as $addLessItem)
                                            <tr class="{{ $loop->first ? 'border-top' : '' }}">
                                                <td>
                                                    <p class="table-headings-font-size mb-1 fw-medium text-start lh-sm px-2">
                                                        {{ $addLessItem['ledger_name'] }}
                                                    </p>
                                                </td>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                        {{ $pdfSymbol . getCurrencyFormat($addLessItem['amount'] ?? '0.0') }}
                                                    </p>
                                                </td>
                                            </tr>
                                        @endforeach
                                        <tr class="border-top">
                                            <td>
                                                <p
                                                    class="total-font-size text-primary mb-2 text-nowrap fw-medium text-start lh-sm px-2 mt-1">
                                                    {{ $changeLabel['total'] ?? 'Total' }}:
                                                </p>
                                            </td>
                                            <td>
                                                <p
                                                    class="total-font-size mb-2 text-nowrap text-end text-primary fw-bold lh-sm px-2 mt-1">
                                                    {{ $pdfSymbol . getCurrencyFormat($total ?? '0.0') }}
                                                </p>
                                            </td>
                                        </tr>
                                        @if (($invoiceSetting['current_outstanding'] ?? false) && ($currentOutstanding ?? false))
                                            <tr>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                        Balance:
                                                    </p>
                                                </td>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium  text-end lh-sm px-2">
                                                        {{ $pdfSymbol . getCurrencyFormat($balance) }}
                                                    </p>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                        Previous O/S:
                                                    </p>
                                                </td>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium  text-end lh-sm px-2">
                                                        {{ $pdfSymbol . getCurrencyFormat($totalDueAmount) }}
                                                    </p>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                        Current O/S:
                                                </td>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium  text-end lh-sm px-2">
                                                        {{ $pdfSymbol . getCurrencyFormat($currentBalance) }}
                                                    </p>
                                                </td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>

</html>
