<!DOCTYPE html>
<html lang="en">
@php

    if (!isset($isA5Pdf)) {
        $isA5Pdf = false;
        if (getCompanyPdfFormat() == \App\Models\CompanySetting::PDF_FORMAT[\App\Models\CompanySetting::A5]) {
            $isA5Pdf = true;
        }
    }
    $pdfSymbol = getPrintPdfCurrencySymbol($currentCompany->id);
    $dockereEnabled = dockerEnabled() && !isset($preview_enabled);
    $fontStyleName = $dockereEnabled ? 'Arial Unicode MS' : 'Arial-unicode-ms';
    $fontCssName = $dockereEnabled ? 'font-docker.css' : 'font.css';
@endphp

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/' . $fontCssName) }}">
    <title>{{ isset($fileName) ? $fileName : $transaction->document_number }}</title>
    @include('company.pdf.sale_pdf.print_custom_font_css_variables')
    <style>
        h1 {
            font-size: 27px;
        }

        * {
            margin: 0;
            padding: 0;
            text-indent: 0;
            font-family: "{{ $fontStyleName }}";
            font-size: 13px;
            font-weight: 400;
            box-sizing: border-box;
        }

        @page {
            margin: 20px;
        }

        .main-table {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            width: 100%;
            box-sizing: border-box;
            border: 2px solid black;
        }

        table {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }

        .custom-table-heading {
            padding: 6px 8px;
            font-weight: bold;
            width: 100px;
            font-size: {{ $isA5Pdf ? '8px' : '12px' }};
            line-height: {{ $isA5Pdf ? '8px' : '12px' }};
        }

        .text-primary {
            color: #4f158c;
        }

        .address {
            font-size: 11px;
            /* line-height: 14px; */
            padding-left: 8px;
            padding-right: 8px;
        }

        .phone {
            font-size: 11px;
            /* line-height: 14px; */
            padding-left: 8px;
            padding-right: 8px;
        }

        td {
            vertical-align: top;
        }

        .fs-13 {
            font-size: 13px;
        }

        .fs-12 {
            font-size: 12px;
        }

        .fw-6 {
            font-weight: 600;
        }

        .whitespace-nowrap {
            white-space: nowrap;
        }

        .border-bottom {
            border-bottom: 1px solid black;
        }

        .border-right {
            border-right: 1px solid black;
        }

        .border-top {
            border-top: 1px solid black;
        }

        .border-left {
            border-left: 1px solid black;
        }

        .vertical-top {
            vertical-align: top;
        }

        .vertical-middle {
            vertical-align: middle;
        }

        .vertical-bottom {
            vertical-align: bottom;
        }

        .text-center {
            text-align: center;
        }

        .text-start {
            text-align: left;
        }

        .text-end {
            text-align: right;
        }

        .table-heading {
            padding: 3px 8px;
            text-align: left;
            position: relative;
            /* background-color: #eeeeee !important; */
        }

        .signature {
            max-width: 210px;
            height: 100px;
            margin-left: auto;
        }

        .desc {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-size: 12px;
            position: relative;
            padding-left: 5px;
            padding-right: 5px;
        }

        .desc::before {
            position: absolute;
            content: "(";
            top: 0;
            left: 0;
            font-size: 12px;
        }

        .desc::after {
            position: absolute;
            content: ")";
            bottom: 2px;
            right: 0;
            font-size: 12px;
        }

        .item-table tr:nth-last-child(-n+2):not(:last-child) {
            font-size: 12px;
            border-bottom: 1px solid black;
            padding: 4px 8px 0 4px;
            height: 100%;
            vertical-align: top;
        }

        .d-none {
            display: none;
        }

        /*.for-preview {
                padding:20px;
                box-sizing:border-box
            }*/

        .min-width-250 {
            min-width: 250px !important;
        }

        .min-width-150 {
            min-width: 150px !important;
        }
        .ps-2{
            padding-left: 5px
        }

        .w-25px{
            width: 25px !important;
        }
        .w-100px {
            width:100px !important;
        }

        .w-80px {
            width: 80px !important;
        }
    </style>
    @include('company.pdf.sale_pdf.print_custom_font_css')
</head>

<body>

    <div class="main-table">
        {{-- Slogan Section Start --}}
        @if(($invoiceSetting['show_slogan'] ?? false) && isset($invoiceSetting['slogan']))
            <div class="text-center border-bottom" style="padding: 4px 2px 2px 2px">
                <h6 class="mb-1 company-address-font-size" style="font-size: 12px">
                    {{ $invoiceSetting['slogan'] }}
                </h6>
            </div>
        @endif
        {{-- Slogan Section End --}}

        {{-- Invoice Type Section Start --}}
        <div class="text-center border-bottom" style="padding: 4px 2px 2px 2px">
            <h6 class="fw-6 text-primary" style="font-size: 15px">
                {{ $taxInvoice }} ({#INVOICE_TYPE#})
            </h6>
        </div>
        {{-- Invoice Type Section End --}}

        {{-- Logo / Company Name Section Start --}}
        <table cellpadding="0">
            <tr>
                <td class="vertical-middle" style="padding: 10px 25px; width: 100px; max-width:100px; height: 100px">
                    @if (isset($currentCompany->company_logo) &&
                            ($invoiceSetting['logo'] ?? true) &&
                            $currentCompany->company_logo != asset('images/preview-img.png'))
                        <img src={{ $currentCompany->company_logo ?? '' }} alt="Logo" style="object-fit: contain;"
                            width="100">
                    @endif
                </td>
                <td class="vertical-middle text-center" style="margin-left:-50px; width:60%;">
                    <h1 class="company-name-font-size company-font-customization">{{ strtoupper($currentCompany->trade_name) }}</h1>
                    @if(($invoiceSetting['prop_details'] ?? true) && isset($customProp))
                        <p class="mt-1 fw-6 company-address-font-size">{{ $customProp->label_name.' : '.$customProp->label_value }}</p>
                    @endif
                    <p class="company-address-font-size">
                        {{ isset($companyBillingAddress->address_1) ? strtoupper($companyBillingAddress->address_1 .',') : null }}
                        {{ isset($companyBillingAddress->address_2) ? strtoupper($companyBillingAddress->address_2 .',') : null }}
                        {{ isset($companyBillingAddress->city_id) ?  strtoupper(getCityName($companyBillingAddress->city_id).',') : null }}
                        {{ isset($companyBillingAddress->state_id) ? strtoupper(getStateName($companyBillingAddress->state_id).',') : null }}
                        {{ isset($companyBillingAddress->country_id) ? strtoupper(getCountryName($companyBillingAddress->country_id).',') : null }}
                        {{ $companyBillingAddress->pin_code ?? null }}
                    </p>
                    <p class="company-address-font-size">
                        @if ($invoiceSetting['mobile_number'] ?? true)
                            {{ $changeLabel['tel'] ?? 'Tel' }} :
                            {{
                                (isset($invoiceSetting['region_code']) ? '+' . $invoiceSetting['region_code'] . ' ' : '') .
                                (isset($invoiceSetting['alternate_phone']) ? $invoiceSetting['alternate_phone'] : ('+' . $currentCompany->user->region_code . ' ' . $currentCompany->user->phone)) .
                                (isset($invoiceSetting['region_code_2']) && isset($invoiceSetting['alternate_phone_2']) ? ', +' . $invoiceSetting['region_code_2'] . ' ' : '') .
                                (isset($invoiceSetting['alternate_phone_2']) ? (isset($invoiceSetting['region_code_2']) ? '' : ', ') . $invoiceSetting['alternate_phone_2'] : '')
                            }}
                        @endif
                        @if (($invoiceSetting['mobile_number'] ?? true) && ($invoiceSetting['email'] ?? true) && (($invoiceSetting['alternate_email'] ?? ($currentCompany->user->email ?? null)) != null))
                            |
                        @endif

                        @if ($invoiceSetting['email'] ?? true)
                            {{ $invoiceSetting['alternate_email'] ?? ($currentCompany->user->email ?? null) }}
                        @endif
                    </p>
                    @if ($currentCompany->is_gst_applicable)
                        <p class="company-address-font-size">{{ $changeLabel['gstin'] ?? 'GSTIN' }}:
                            {{ '  ' . $currentCompany->companyTax->gstin ?? null }}</p>
                    @endif
                    @foreach (printCustomPDFLabelsForSale() as $key => $customLabel)
                        <p class="company-address-font-size">{{ $key ?? null }}:
                            {{ $customLabel ?? null }}</p>
                    @endforeach
                </td>
                <td class="vertical-middle text-end"
                    style="padding: 10px 25px; width: 100px; max-width:100px; height: 100px">
                    @if (!empty($eInvoice))
                        @php
                            $qrCode = (string) QrCode::format('svg')
                                ->margin(0)
                                ->size(200)
                                ->generate($eInvoice->signed_qr_code);
                        @endphp
                        <img src="data:image/svg+xml;base64,{{ base64_encode($qrCode) }}" width="100"
                            height="100" />
                    @endif
                </td>
            </tr>
        </table>
        {{-- Logo / Company Name Section End --}}

        {{-- Bill To / Ship to / Dispatch Address Section Start --}}
        <table cellpadding="0">
            <tr class="border-bottom">
                <td class="border-right vertical-top" style="width:33.33%;">
                    <p class="text-primary border-bottom border-top fw-6 table-heading header-labels-font-size" >
                        {{ $changeLabel['bill_to'] ?? 'Bill to' }}:
                    </p>
                    <h4 class="fw-6 header-contents-font-size" style="padding: 4px 0 1px 8px;" >
                        {{ strtoupper($customerDetail->name) }}
                    </h4>
                    @if (isset($billingAddress))
                        <p class="address header-contents-font-size">
                            @if ($billingAddress->address_1 != null)
                                {{ strtoupper($billingAddress->address_1) }}
                            @endif
                            @if ($billingAddress->address_2 != null)
                                {{ strtoupper($billingAddress->address_2) }},
                            @endif
                            {{ $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                            {{ $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                            {{ strtoupper(getCountryName($billingAddress->country_id ?? null)) }},
                            {{ $billingAddress->pin_code ?? null }}
                        </p>
                    @endif
                    <p class="phone header-contents-font-size">
                        @if (!empty($transaction->party_phone_number))
                            Contact No:
                                +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                        @elseif (!empty($customerDetail->model->phone_1))
                            Contact No:
                                +{{ $customerDetail->model->region_code_1 ?? '' }} {{ $customerDetail->model->phone_1 ?? null }}
                        @endif
                        @if (!empty($customerDetail->model->phone_2) && ($customerDetail->model->phone_1 !== $customerDetail->model->phone_2))
                            {{ empty($customerDetail->model->phone_1) && empty($transaction->party_phone_number) ? 'Contact No:' : "|" }}
                            <span class="whitespace-nowrap header-contents-font-size">
                                +{{ $customerDetail->model->region_code_2 ?? '' }} {{ $customerDetail->model->phone_2 ?? null }}
                            </span>
                        @endif
                    </p>
                    @if (!empty($customerDetail->model->person_email))
                        <p class="fs-12 header-contents-font-size" style="padding: 2px 0px 0px 8px;">
                            Email: {{ $customerDetail->model->person_email ?? null }}
                        </p>
                    @endif
                    @if ($showGst)
                        <p class="fs-12 header-contents-font-size"  style="padding: 2px 0px 0px 8px;">
                            GSTIN: {{ $transaction->gstin ?? $customerDetail->model->gstin ?? null }}
                        </p>
                    @endif
                    @if (!empty($panNumber) && $showPanNumber)
                        <p class="fs-12 header-contents-font-size" style="padding: 2px 0px 0px 8px; " >
                            PAN: {{ $panNumber ?? null }}
                        </p>
                    @endif
                </td>
                @if ($invoiceSetting['ship_to_details'] ?? true)
                    <td class="border-right vertical-top" style="width:33.33%;">
                        <p class="text-primary border-bottom border-top fw-6 table-heading header-labels-font-size">
                            {{ $changeLabel['ship_to'] ?? 'Ship to' }}:
                        </p>

                        <h4 class="fw-6 header-contents-font-size" style="padding: 4px 0 1px 8px;">
                            {{ strtoupper($transaction->shipping_name ?? $customerDetail->name) }}
                        </h4>
                        <p class="address header-contents-font-size" >
                            @if (isset($shippingAddress->address_1))
                                {{ strtoupper($shippingAddress->address_1) }},
                            @endif
                            @if (isset($shippingAddress->address_2))
                                {{ strtoupper($shippingAddress->address_2) }},
                            @endif
                            {{ isset($shippingAddress->city_id) ? strtoupper(getCityName($shippingAddress->city_id)) . ',' : ''  }}
                            {{ isset($shippingAddress->state_id) ? strtoupper(getStateName($shippingAddress->state_id)) . ',' : '' }}
                            {{ isset($shippingAddress->country_id) ? strtoupper(getCountryName($shippingAddress->country_id)) . ',' : '' }},
                            {{ $shippingAddress->pin_code ?? null }}
                        </p>
                        <p class="phone header-contents-font-size" >
                            @if (!empty($transaction->party_phone_number))
                                Contact No:
                                    +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                            @endif
                        </p>
                        @if ($isCompanyGstApplicable && !empty($transaction->shipping_gstin))
                            <p class="fs-12 header-contents-font-size" style="padding: 2px 0px 0px 8px;">
                                GSTIN: {{ $transaction->shipping_gstin ?? null }}
                            </p>
                        @endif
                        @if (!empty($panNumber) && $showPanNumber)
                            <p class="fs-12 header-contents-font-size"
                                style="padding: 0px 0px 2px 8px; ">
                                PAN: {{ $panNumber ?? null }}
                            </p>
                        @endif
                    </td>
                @endif

                <td class="vertical-top border-left" style="width: 33.33%">
                    <p class="text-primary border-bottom border-top fw-6 table-heading header-labels-font-size" >
                        {{ $invoiceDetailLabel }}:
                    </p>
                        <div style="padding:0 8px !important;">
                            <table class="table">
                                <tr>
                                    <td class="header-contents-font-size fw-6"
                                        style="padding: 4px 8px 0 3px;">
                                        {{ $invoiceNumberLabel }}:
                                    </td>
                                    <td class="header-contents-font-size text-end"
                                        style="padding: 4px 1px 1px 1px;">
                                        {{ $transaction->full_invoice_number }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="header-contents-font-size fw-6"
                                        style="padding: 3px 8px 0 3px;">
                                        {{ $invoiceDateLabel }}:
                                    </td>
                                    <td class="header-contents-font-size text-end"
                                        style="{{ $invoiceSetting['po_number'] ?? true ? 'padding: 4px 1px 1px 1px;' : 'padding: 4px 1px 4px 1px;' }} ">
                                        {{ $invoiceDate }}
                                    </td>
                                </tr>
                                @if(isset($originalInvoiceNumber) && !empty($originalInvoiceNumber) && isset($originalInvoiceDate) && !empty($originalInvoiceDate))
                                    <tr>
                                        <td class="header-contents-font-size fw-6"
                                            style="padding: 3px 8px 0 3px;">
                                            Original Invoice No:
                                        </td>
                                        <td class="header-contents-font-size text-end"
                                            style="padding: 4px 1px 1px 1px;">
                                            {{ Illuminate\Support\Str::limit($originalInvoiceNumber, 20, '...')  }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="header-contents-font-size fw-6"
                                            style="padding: 3px 8px 0 3px;">
                                           Original Invoice Date:
                                        </td>
                                        <td class="header-contents-font-size text-end"
                                            style="padding: 4px 1px 1px 1px;">
                                            {{ isset($originalInvoiceDate) ? \Carbon\Carbon::parse($originalInvoiceDate)->format('d-m-Y') : null }}
                                        </td>
                                    </tr>
                                @endif
                                @if(isset($deliveryChallanInvoiceNumber) && !empty($deliveryChallanInvoiceNumber))
                                    <tr>
                                        <td class="header-contents-font-size fw-6"
                                            style="padding: 3px 8px 0 3px;">
                                            {{ $changeLabel['delivery_challan'] ?? 'Delivery Challan No' }}:
                                        </td>
                                        <td class="header-contents-font-size text-end"
                                            style="padding: 4px 1px 1px 1px;">
                                            {{ Illuminate\Support\Str::limit($deliveryChallanInvoiceNumber, 20, '...')  }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="header-contents-font-size fw-6"
                                            style="padding: 3px 8px 0 3px;">
                                            {{ $changeLabel['delivery_challan'] ?? 'Delivery Date' }}:
                                        </td>
                                        <td class="header-contents-font-size text-end"
                                            style="padding: 4px 1px 1px 1px;">
                                            {{ $deliveryChallanInvoiceDate }}
                                        </td>
                                    </tr>
                                @endif
                                @if ($invoiceSetting['po_number'] ?? true)
                                    <tr>
                                        <td class="header-contents-font-size fw-6"
                                            style="padding: 3px 8px 0 3px; ">
                                            {{ $changeLabel['po_number_label'] ?? 'PO No' }}:
                                        </td>
                                        <td class="header-contents-font-size text-end"
                                            style="padding: 4px 1px 1px 1px; ">
                                            {{ $transaction->po_no }}
                                        </td>
                                    </tr>
                                @endif
                                @if ($invoiceSetting['show_po_date'] ?? true)
                                    <tr>
                                        <td
                                            class="header-contents-font-size fw-6" style="padding: 3px 8px 0 3px; ">
                                            {{ $changeLabel['po_date'] ?? 'PO Date' }}:
                                        </td>
                                        <td class="header-contents-font-size text-end"
                                            style="padding: 4px 1px 4px 1px; ">
                                            {{ isset($transaction->po_date) ? \Carbon\Carbon::parse($transaction->po_date)->format('d-m-Y') : null }}
                                        </td>
                                    </tr>
                                @endif
                            </table>
                        </div>
                    </td>

            </tr>
        </table>
        {{-- Bill To / Ship to / Dispatch Address Section End --}}

        {{-- Invoice Details /  rt Details / EInvoice / EWay Section Start --}}
            <table>
                <tr class="">
                    @if (isset($transaction['dispatch_address_id']) && isset($dispatchAddress) && (($invoiceSetting['dispatch_from_details'] ?? false)))
                    <td class="vertical-top border-right border-bottom" style="width: 33.33%">
                        <p class="text-primary border-bottom fw-6 table-heading header-labels-font-size"
                            style="">
                            {{ $changeLabel['dispatch_from'] ?? 'Dispatch from' }}:
                        </p>
                        <p class="address header-contents-font-size" style="margin-top: 0.5rem ;">
                            {{ isset($dispatchAddress->address_1) ? strtoupper($dispatchAddress->address_1 .',') : null }}
                            {{ isset($dispatchAddress->address_2) ? strtoupper($dispatchAddress->address_2 .',') : null }}
                            {{ isset($dispatchAddress->city_id) ?  strtoupper(getCityName($dispatchAddress->city_id).',') : null }}
                            {{ isset($dispatchAddress->state_id) ? strtoupper(getStateName($dispatchAddress->state_id).',') : null }}
                            {{ isset($dispatchAddress->country_id) ? strtoupper(getCountryName($dispatchAddress->country_id).',') : null }}
                            {{ $dispatchAddress->pin_code ?? null }}
                        </p>
                        </td>
                    @endif
                    @if (($invoiceSetting['transport_details'] ?? true) || isset($eWayBill) || !empty($transaction->transporter_vehicle_number) || (isset($transaction->eway_bill_number) || isset($transaction->eway_bill_date)))
                        <td class="vertical-top border-bottom" style="width:33.33%;">
                            <div>
                                @if($invoiceSetting['transport_details'] ?? true)
                                    <div style="display: flex; gap:8px;">
                                        <div class="header-contents-font-size fw-6" style="padding: 3px 0 0 8px;  max-width:120px; width:100%; ">
                                            {{ $changeLabel['transport_name'] ?? 'Transport Name' }}:
                                        </div>
                                        <div class="header-contents-font-size" style="padding: 3px 0 0 8px; ">
                                            {{ $transaction->transport->transporter_name ?? '' }}
                                        </div>
                                    </div>
                                    <div style="display: flex; gap:8px;">
                                        <div class="header-contents-font-size" style="padding: 3px 0 0 8px;font-weight: bold; max-width:120px; width:100%;">
                                            {{ $changeLabel['document_no'] ?? 'Document No' }}:
                                        </div>
                                        <div class="header-contents-font-size" style="padding: 3px 0 0 8px; ">
                                            {{ $transaction->transporter_document_number ?? '' }}
                                        </div>
                                    </div>
                                    <div style="display: flex; gap:8px;">
                                        <div class="header-contents-font-size" style="padding: 3px 0 0 8px;font-weight: bold; max-width:120px; width:100%; ">
                                            {{ $changeLabel['document_date'] ?? 'Document Date' }}:
                                        </div>
                                        <div style="padding: 3px 0 0 8px;" class="header-contents-font-size">
                                            {{ !empty($transaction->transporter_document_date) ? \Carbon\Carbon::parse($transaction->transporter_document_date)->format('d-m-Y') : ' ' }}
                                        </div>
                                    </div>
                                @endif
                                @if (isset($eWayBill) || (isset($transaction->eway_bill_number) || isset($transaction->eway_bill_date)))
                                    <div style="display: flex; gap:8px;">
                                        <div class="header-contents-font-size" style="padding: 3px 0 0 8px;font-weight: bold; max-width:120px; width:100%; ">
                                            {{ $changeLabel['e_way_bill_no'] ?? 'E-way Bill No' }}:
                                        </div>
                                        <div class="header-contents-font-size" style="padding: 3px 0 0 8px; ">
                                            {{ $eWayBill?->eway_bill_no ?? $transaction->eway_bill_number ?? null}}
                                        </div>
                                    </div>
                                    <div style="display: flex; gap:8px;">
                                        <div class="header-contents-font-size" style="padding: 3px 0 0 8px;font-weight: bold; max-width:120px; width:100%;">
                                            {{ $changeLabel['e_way_bill_date'] ?? 'E-way Bill Date' }}:
                                        </div>
                                        <div class="header-contents-font-size" style="padding: 3px 0 0 8px;">
                                            {{ \Carbon\Carbon::parse($eWayBill?->eway_bill_date)->format('d-m-Y') ?? \Carbon\Carbon::parse($transaction->eway_bill_date)->format('d-m-Y') ?? null }}
                                        </div>
                                    </div>
                                @endif
                                @if (!empty($transaction->transporter_vehicle_number))
                                    <div style="display: flex; gap:8px;">
                                        <div class="header-contents-font-size" style="padding: 3px 0 0 8px;font-weight: bold; max-width:120px; width:100%;">
                                            {{ $changeLabel['transport_vehicle_number'] ?? 'Vehicle No' }}:
                                        </div>
                                        <div class="header-contents-font-size" style="padding: 3px 0 0 8px; ">
                                            {{ $transaction->transporter_vehicle_number ?? '' }}
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </td>
                    @endif
                    @if (!empty($eInvoice))
                        <td class="{{ $invoiceSetting['transport_details'] ?? true ? 'border-left border-bottom' : 'border-bottom' }}" style="width:33.33%;">
                            <table style="display: table">
                                <tr>
                                    <td class="whitespace-nowrap header-contents-font-size"
                                        style="padding: 3px 0 0 8px; font-weight: bold;" class="">
                                        {{ $changeLabel['ack_no'] ?? 'Ack No' }}:
                                    </td>
                                    <td
                                        style="padding: 3px 0 0 4px;  " class="header-contents-font-size">
                                        {{ !empty($eInvoice) ? $eInvoice->ack_no : '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="whitespace-nowrap header-contents-font-size"
                                        style="padding: 3px 0 0 8px; font-weight: bold;">
                                        {{ $changeLabel['ack_date'] ?? 'Ack Date' }}:
                                    </td>
                                    <td style="padding: 3px 0 0 4px;" class="header-contents-font-size">
                                        {{ !empty($eInvoice) ? \Carbon\Carbon::parse($eInvoice->ack_date)->format('d-m-Y') : '' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="whitespace-nowrap vertical-top header-contents-font-size"
                                        style="padding: 3px 0 3px 8px; font-weight: bold;">
                                        {{ $changeLabel['irn'] ?? 'IRN' }}:
                                    </td>
                                    <td class="vertical-top header-contents-font-size"
                                        style="padding: 3px 0 3px 4px; word-break: break-all">
                                        {{ !empty($eInvoice) ? $eInvoice->irn : '' }}
                                    </td>
                                </tr>
                            </table>
                        </td>
                    @endif
                </tr>
            </table>
        {{-- Invoice Details / Transport Details / EInvoice / EWay Section End --}}

        {{-- Custom Fields Section Start --}}
        @if (count($customFieldValues) > 0)
            @php
                $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
            @endphp
            <table cellpadding="0" class="item-table">
                @foreach ($customFields->chunk(3) as $chunk)
                    <tr class="border-bottom">
                        @foreach ($chunk as $customField)
                            <td class="{{ $loop->last ? '' : 'border-right' }}" style="padding: 6px 8px; width:150px; {{ $isA5Pdf ? 'font-size: 9px; line-height: 8px;' : '' }}">
                                <span style="font-weight: bold;{{ $isA5Pdf ? 'font-size: 9px; line-height: 8px;' : '' }}">{{ $customField['label_name'] ?? '' }}</span> : {{ $customField['value'] ?? '' }}
                            </td>
                        @endforeach
                    </tr>
                @endforeach
            </table>
        @endif
        {{-- Custom Fields Section End --}}

        {{-- Item Table Section Start --}}
            <table cellpadding="0" style="flex-grow:1;" class="item-table">
                <tr class="border-bottom">
                    @foreach ($rearrangeItems['headings'] as $headings)
                        @if($headings['is_show_in_print'])
                            <td class="table-headings-font-size {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }} whitespace-nowrap  {{ $headings['class'] }}"
                                style="padding: 6px 8px; font-weight: bold;">
                                {{ $headings['name'] }}
                            </td>
                        @endif
                    @endforeach
                </tr>
                @foreach ($rearrangeItems['detail'] as $items)
                    <tr>
                        @foreach ($items as  $key => $item)
                            @if($item['is_show_in_print'])
                                <td class="table-contents-font-size  {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }} {{ ($key == 'item_name') ? 'min-width-150' : 'text-center'}}"
                                    style="padding: 4px 8px 0 8px; {{ ($key == 'primary_quantity' || $key == 'secondary_quantity') ? 'white-space: nowrap;' : '' }} ">
                                    {{ $item['value'] }}
                                    @if($key == 'item_name')
                                        @if($item['show_sku'])
                                            <p class="description-font-size">Item Code:
                                                {{ $item['sku'] ?? null }}</p>
                                        @endif
                                        @if($item['show_consolidating_items'])
                                            <p style="word-break: break-word; " class="description-font-size">
                                                {!! $item['consolidating_items'] !!}
                                            </p>
                                        @endif
                                        @if($item['show_additional_description'])
                                            <p style="word-break: break-word;" class="description-font-size">
                                                {!! $item['additional_description'] !!}
                                            </p>
                                        @endif
                                        @if($item['show_item_image'])
                                            <div><img src="{{ $item['item_image'] }}" width="60"  height="60" style="margin-top: 4px"></div>
                                        @endif
                                    @endif
                                </td>
                            @endif
                        @endforeach
                    </tr>
                @endforeach
                <tr class="border-bottom">
                    @foreach ($rearrangeItems['footer'] as $key => $footer)
                            @if($footer['is_show_in_print'])
                                <td class="table-headings-font-size {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }}  fw-6 {{ ($key == 'item_name') ? 'min-width-150 ps-2' : 'text-center'}}"
                                style="">{{ $footer['value'] }}</td>
                            @endif
                    @endforeach
                </tr>
            </table>

        {{--  Terms Of Payment & Notes / Broker Section Start / Total Section Start --}}
        <table cellpadding="0" style="page-break-inside: avoid !important">
            <tr class="border-bottom">
                <td class="{{ ((($invoiceSetting['qr_code'] ?? false) && isset($bankDetail->upi_id)) || ($invoiceSetting['bank_details'] ?? true)) ? 'border-right' : '' }}">
                @if (((($invoiceSetting['qr_code'] ?? false) && isset($bankDetail->upi_id)) || ($invoiceSetting['bank_details'] ?? true)))
                        <table>
                            <tr>
                                <td>
                                    <div class="" style="display: flex; padding: 4px 5px 4px 5px; height: 100%;">
                                    @if ((isset($invoiceSetting['qr_code']) && $invoiceSetting['qr_code'] == 1 ? true : false) && isset($bankDetail->upi_id))
                                        <div class="qr-code"
                                            style="margin-top: 10px; padding-right: 10px; white-space: nowrap;">
                                            <img src="data:image/svg+xml;base64,{{ base64_encode(generateUPIQRCode($bankDetail->upi_id, $transaction->due_amount)) }}"
                                                width="75" height="75" />
                                        </div>
                                    @endif
                                    @if ((isset($invoiceSetting['bank_details']) && $invoiceSetting['bank_details'] == 1 ? true : false))
                                        <div class="">
                                            <table style="white-space: nowrap">
                                                <tr>
                                                    <td class="footer-headings-font-size fw-6"
                                                        style=" padding: 2px 8px 2px 8px;">
                                                        Bank:
                                                    </td>
                                                    <td class="footer-contents-font-size"
                                                        style="padding: 2px 8px 2px 0px;   white-space: normal">
                                                        {{ !empty($bankDetail) ? $bankDetail->bank_name : null }}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="footer-headings-font-size fw-6"
                                                        style=" padding: 2px 8px 2px 8px;">
                                                        IFSC Code:
                                                    </td>
                                                    <td class="footer-contents-font-size"
                                                        style="padding: 2px 8px 2px 0px; ">
                                                        {{ !empty($bankDetail) ? $bankDetail->ifsc_code : null }}
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="footer-headings-font-size fw-6"
                                                        style="padding: 2px 8px 2px 8px;">
                                                        A/C Number:
                                                    </td>
                                                    <td class="footer-contents-font-size"
                                                        style="padding: 2px 8px 2px 0px; ">
                                                        {{ $accountNumber }}
                                                    </td>
                                                </tr>
                                                @if (!empty($bankDetail) && $bankDetail->swift_code != null)
                                                    <tr>
                                                        <td class="footer-headings-font-size fw-6"
                                                            style="padding: 2px 8px 2px 8px;">
                                                            Swift Code:
                                                        </td>
                                                        <td class="footer-contents-font-size"
                                                            style="padding: 2px 8px 2px 0px; ">
                                                            {{ $bankDetail->swift_code }}
                                                        </td>
                                                    </tr>
                                                @endif
                                                <tr>
                                                    <td class="footer-headings-font-size fw-6"
                                                        style="padding: 2px 8px 2px 8px;">
                                                        Bank Branch:
                                                    </td>
                                                    <td class="footer-contents-font-size"
                                                        style="padding: 2px 8px 2px 0px; white-space: normal">
                                                        {{ $branchName }}
                                                    </td>
                                                </tr>
                                                @if (isset($bankDetail->account_holder_name))
                                                    <tr>
                                                        <td class="footer-headings-font-size fw-6"
                                                            style="padding: 2px 8px 2px 8px;">
                                                            A/C Name:
                                                        </td>
                                                        <td class="footer-contents-font-size"
                                                            style="padding: 2px 8px 2px 0px; ">
                                                            {{ $bankDetail->account_holder_name }}
                                                        </td>
                                                    </tr>
                                                @endif
                                                <tr>
                                                    @if(isset($bankDetail->upi_id))
                                                        <td class="footer-headings-font-size fw-6"
                                                            style="padding: 2px 8px 2px 8px;">
                                                            UPI ID:
                                                        </td>
                                                        <td class="footer-contents-font-size"
                                                            style="padding: 2px 8px 2px 0px;  white-space: normal">
                                                            {{ $bankDetail->upi_id }}
                                                        </td>
                                                    @endif
                                                </tr>
                                            </table>
                                        </div>
                                    @endif
                                    </div>
                                </td>
                            </tr>
                        </table>

                @endif
                </td>
                @if (((($invoiceSetting['show_credit_period'] ?? true) || ($invoiceSetting['show_due_date'] ?? true)) && ($showCreditPeriod ?? true)) || ($invoiceSetting['broker_details'] ?? true) || ($invoiceSetting['show_payment_status'] ?? false))
                    <td class="vertical-top" style="padding: 8px 0px 8px 0px; ">
                        @if (($invoiceSetting['show_credit_period'] ?? true) || ($invoiceSetting['show_due_date'] ?? true))
                            <div style="{{ $isA5Pdf ? 'min-height: 40px; min-width: 40px' : 'min-height: 60px' }}">
                            <table>
                                @if ($invoiceSetting['show_credit_period'] ?? true)
                                <tr>
                                    <td class="header-contents-font-size whitespace-nowrap vertical-bottom fw-6"
                                        style="padding: 5px 8px 0px 8px; ">
                                        {{ $changeLabel['credit_period'] ?? 'Credit Period' }}:
                                    </td>
                                    <td class="header-contents-font-size whitespace-nowrap vertical-bottom"
                                        style="padding: 5px 8px 0px 0px; ">
                                        {{ $creditPeriod }}
                                    </td>
                                </tr>
                                @endif
                                @if($invoiceSetting['show_due_date'] ?? true)
                                <tr class="vertical-top">
                                    <td class="header-contents-font-size whitespace-nowrap fw-6"
                                        style="padding: 3px 8px 0px 8px; ">
                                        {{ $changeLabel['due_date'] ?? ' Due Date' }}:
                                    </td>
                                    <td class="header-contents-font-size"
                                        style="padding: 3px 8px 0px 0px; white-space: nowrap;">
                                        {{ $dueDate }}
                                    </td>
                                </tr>
                                @endif
                            </table>
                            </div>
                        @endif
                        <div>
                            @if ($invoiceSetting['broker_details'] ?? true)
                            <table class="vertical-top">
                                <tr class="{{ ((($invoiceSetting['show_due_date'] ?? true) || ($invoiceSetting['show_due_date'] ?? true)) && (($showCreditPeriod ?? true) || ($invoiceSetting['broker_details'] ?? true))) ? 'border-top  ' : '' }} vertical-top">
                                    <td class="header-contents-font-size vertical-top fw-6"
                                        style="padding: 5px 8px 0px 8px; ">
                                        Broker:
                                    </td>
                                    <td class="header-contents-font-size vertical-bottom"
                                        style="padding: 5px 8px 0px 0px white-space: normal; ">

                                        {{ $transaction->brokerDetails->broker_name ?? '' }}
                                    </td>
                                </tr>
                                @if($isCompanyGstApplicable)
                                <tr>
                                    <td class="header-contents-font-size whitespace-nowrap fw-6"
                                        style="padding: 3px 8px 1px 8px; ">
                                        GSTIN:
                                    </td>
                                    <td class="header-contents-font-size"
                                        style="padding: 3px 8px 1px 0px;white-space: nowrap;">
                                        {{ $transaction->brokerDetails->gstin ?? '' }}
                                    </td>
                                </tr>
                                @endif
                            </table>
                            @endif
                            @if($invoiceSetting['show_payment_status'] ?? false)
                            @if($transaction->payment_status == 'Partially Unpaid')
                           <div style="display: flex; justify-content:center; align-items:center;">
                             <div style="border:2px solid #4f158c; width:fit-content; margin:8px auto; padding:4px 8px; display:inline-block;">
                                <h5 class="text-primary text-center mb-0" style="font-weight:700; font-size:14px;"> PARTLY</br>
                                 PAID</h5>
                            </div>
                           </div>
                            @elseif ($transaction->payment_status == 'Paid')
                            <div class="flex-grow-1">
                                <div style="display: flex; justify-content:center; align-items:center;">
                                <div
                                    style="padding:2px; width:fit-content; background: linear-gradient(to top,#43ad52,#a6d052); margin:8px auto; display:inline;">
                                    <div style="background-color: white; padding:4px 8px;">
                                        <h4 class="text-center"
                                            style="font-weight:700; font-size:14px; background: -webkit-linear-gradient(#a6d052, #43ad52); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                                            PAID</h4>
                                    </div>
                                </div>
                                </div>
                            </div>
                            @elseif($transaction->payment_status == 'Unpaid')
                            <div style="display: flex; justify-content:center; align-items:center;" class="flex-grow-1">
                                <div style="padding:2px; width:fit-content; background: linear-gradient(to top,#801520,#c30a17); margin:8px auto; display:inline;">
                                    <div style="background-color: white; padding:4px 8px;">
                                        <h4 class="text-center"
                                            style="font-weight:700; font-size:14px; background: -webkit-linear-gradient(#c30a17, #801520); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                                            UNPAID</h4>
                                    </div>
                                </div>
                            </div>
                            @endif
                            @endif
                        </div>
                    </td>
                @endif
                <td class="vertical-top" style="{{ $isA5Pdf ? 'width: 150px' : 'width: 250px' }}; border-left: 1px solid black">
                    <table>
                        @foreach ($additionalCharges as $additionalCharge)
                            <tr class="{{ $loop->last ? 'border-bottom' : '' }}">
                                <td class="vertical-top table-contents-font-size" style="padding: 4px 8px 2px 8px; ">{{ $additionalCharge['ledger_name'] }}</td>
                                <td class="table-contents-font-size vertical-top  text-end" style="padding: 4px 8px 2px 8px; ">{{ $pdfSymbol.getCurrencyFormat($additionalCharge['amount'] ?? '0.0') }}</td>
                            </tr>
                        @endforeach
                        <tr class="">
                            <td class="table-contents-font-size vertical-top fw-bold fw-6"
                                style="padding: 4px 8px 2px 8px; ">
                                {{ $isCompanyGstApplicable ? $changeLabel['sub_total'] ?? 'Taxable Value' : $changeLabel['sub_total'] ?? 'Sub Total' }}:
                            </td>
                            <td class="table-contents-font-size vertical-top text-end fw-6"
                                style="padding: 4px 8px 2px 8px; ">
                                {{ $pdfSymbol.getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber()) ?? '0.0') }}
                            </td>
                        </tr>
                        @if ($isCompanyGstApplicable)
                            @if ($transaction->cgst != 0)
                                <tr>
                                    <td class="table-contents-font-size vertical-top"
                                        style="padding: 3px 8px 2px 8px; ">
                                        {{ $changeLabel['cgst'] ?? 'CGST' }}:
                                    </td>
                                    <td class="table-contents-font-size vertical-top text-end"
                                        style="padding: 3px 8px 2px 8px; ">
                                        {{ $pdfSymbol.getCurrencyFormat(round($transaction->cgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                    </td>
                                </tr>
                            @endif
                            @if ($transaction->sgst != 0)
                                <tr>
                                    <td class="table-contents-font-size vertical-top"
                                        style="padding: 3px 8px 2px 8px;">
                                        {{ $changeLabel['sgst'] ?? 'SGST' }}:
                                    </td>
                                    <td class="table-contents-font-size vertical-top text-end"
                                        style="padding: 3px 8px 2px 8px;">
                                        {{ $pdfSymbol.getCurrencyFormat(round($transaction->sgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                    </td>
                                </tr>
                            @endif
                            @if ($transaction->igst != 0)
                                <tr>
                                    <td class="table-contents-font-size vertical-top"
                                        style="padding: 3px 8px 2px 8px; ">
                                        {{ $changeLabel['igst'] ?? 'IGST' }}:
                                    </td>
                                    <td class="table-contents-font-size vertical-top text-end"
                                        style="padding: 3px 8px 2px 8px; ">
                                        {{ $pdfSymbol.getCurrencyFormat(round($transaction->igst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                    </td>
                                </tr>
                            @endif
                        @endif
                        @if ($transaction->tcs_amount != 0)
                            <tr>
                                <td class="table-contents-font-size vertical-top"
                                    style="padding: 3px 8px 2px 8px; ">
                                    {{ $changeLabel['tcs'] ?? 'TCS' }}:
                                </td>
                                <td class="table-contents-font-size vertical-top text-end"
                                    style="padding: 3px 8px 2px 8px; ">
                                    {{ $pdfSymbol.getCurrencyFormat($transaction->tcs_amount ?? '0.0') }}
                                </td>
                            </tr>
                        @endif
                        @if ($transaction->cess != 0)
                            <tr>
                                <td class="table-contents-font-size vertical-top"
                                    style="padding: 3px 8px 2px 8px;">
                                    {{ $changeLabel['cess'] ?? 'Cess' }}:
                                </td>
                                <td class="table-contents-font-size vertical-top text-end"
                                    style="padding: 3px 8px 2px 8px; ">
                                    {{ $pdfSymbol.getCurrencyFormat($transaction->cess ?? '0.0') }}
                                </td>
                            </tr>
                        @endif
                        <tr>
                            <td class="table-contents-font-size vertical-top"
                                style="padding: 3px 8px 4px 8px;">
                                {{ $changeLabel['round_off'] ?? 'Round off' }}:
                            </td>
                            <td class="table-contents-font-size vertical-top text-end"
                                style="padding: 3px 8px 4px 8px;">
                                {{ $pdfSymbol.getCurrencyFormat($transaction->rounding_amount ?? '0.0') }}
                            </td>
                        </tr>
                        @php
                            if (empty($addLess)) {
                                $total = $transaction->grand_total;
                            } else {
                                $addLessSum = collect($addLess)->sum('amount');
                                $total = $transaction->grand_total - $addLessSum;
                                $addLessSumTotal = collect($addLess)->where('is_show_in_print',1)->sum('amount');
                                $total = $total + $addLessSumTotal;
                            }
                        @endphp
                        @foreach (collect($addLess)->where('is_show_in_print',1) as $addLessItem)
                        <tr>
                            <td class="table-contents-font-size vertical-top"
                                style="padding: 3px 8px 4px 8px;">
                                {{ $addLessItem['ledger_name'] }}
                            </td>
                            <td class="table-contents-font-size vertical-top text-end"
                                style="padding: 3px 8px 4px 8px;">
                                {{ $pdfSymbol.getCurrencyFormat($addLessItem['amount'] ?? '0.0') }}
                            </td>

                        </tr>
                    @endforeach
                    </table>
                </td>
            </tr>
        </table>
        {{-- Terms Of Payment & Notes / Broker Section End / Total Section End --}}

        {{-- Total Section Start --}}
        <table cellpadding="0">
            <tr class="{{ ($transaction->term_and_condition && ($showPrintSettings['show_sale_terms_and_conditions'] ?? true)) || ($transaction->narration && ($showPrintSettings['show_sale_narration'] ?? true)) || ($showPrintSettings['show_sale_authorized_signatory'] ?? true) || ($invoiceSetting['signature'] ?? true) ? 'border-bottom' : '' }}">
                <td class="border-right vertical-bottom">
                    @if($showPrintSettings['show_sale_in_words'] ?? true)
                        <p class="table-contents-font-size fw-6"
                            style="display: flex; padding: 6px 3px 6px 8px;">
                            {{ $changeLabel['in_words'] ?? 'In Words' }}:
                            <span class="table-contents-font-size"
                                style="margin-left: 3px; font-weight: 400; ">
                                {{ getAmountToWord($total ?? '0.0') }} Only
                            </span>
                        </p>
                    @endif
                </td>
                <td style="width: 250px">
                    <table class="vertical-bottom">
                        <tr>
                            <td class="text-primary total-font-size"
                                style="padding: 6px 8px; font-weight: bold;">
                                {{ $changeLabel['total'] ?? 'Total' }}:
                            </td>
                            <td class="text-primary text-end total-font-size"
                                style="padding: 6px 8px;  font-weight: bold;">
                                {{ $pdfSymbol.getCurrencyFormat($total ?? '0.0') }}
                            </td>
                        </tr>
                        @if (($invoiceSetting['current_outstanding'] ?? false) && ($currentOutstanding ?? false))
                            <tr>
                                <td class="table-contents-font-size vertical-top"
                                    style="padding: 2px 8px;">
                                    Balance:
                                </td>
                                <td class="table-contents-font-size vertical-top text-end"
                                    style="padding: 2px 8px; ">
                                    {{ $pdfSymbol.getCurrencyFormat($balance) }}
                                </td>
                            </tr>
                            <tr>
                                <td class="table-contents-font-size vertical-top "
                                    style="padding: 2px 8px; ">
                                    Previous O/S:
                                </td>
                                <td class="table-contents-font-size vertical-top text-end"
                                    style="padding: 2px 8px; ">
                                    {{ $pdfSymbol.getCurrencyFormat($totalDueAmount) }}
                                </td>
                            </tr>
                            <tr>
                                <td class="table-contents-font-size vertical-top"
                                    style="padding: 2px 8px;">
                                    Current O/S:
                                </td>
                                <td class="table-contents-font-size vertical-top text-end"
                                    style="padding: 2px 8px;">
                                    {{ $pdfSymbol.getCurrencyFormat($currentBalance) }}
                                </td>
                            </tr>
                        @endif
                    </table>
                </td>
            </tr>
        </table>
        {{-- Total Section End --}}

        {{-- GST Details Section Start --}}
        @if ($isCompanyGstApplicable && !empty($checkHsnCodeExist) && ($invoiceSetting['hsn_summary'] ?? true))
            <table cellpadding="0">
                <tr class="border-bottom" style="width: 100%">
                    <td class="footer-headings-font-size border-right text-center"
                        style="padding: 4px 8px; font-weight: bold;">
                        SN
                    </td>
                    <td class="footer-headings-font-size border-right text-center"
                        style="padding: 4px 8px; font-weight: bold;">
                        HSN/SAC
                    </td>
                    <td class="footer-headings-font-size border-right text-center"
                        style="padding: 4px 8px; font-weight: bold ;">
                        Taxable Amount
                    </td>
                    <td class="footer-headings-font-size border-right text-center"
                        style="padding: 4px 8px; font-weight: bold;">
                        GST (%)
                    </td>
                    @if ($cgst != 0.0)
                        <td class="footer-headings-font-size border-right text-center"
                            style="padding: 4px 8px; font-weight: bold;">
                            CGST
                        </td>
                    @endif
                    @if ($sgst != 0.0)
                        <td class="footer-headings-font-size border-right text-center"
                            style="padding: 4px 8px; font-weight: bold ;">
                            SGST
                        </td>
                    @endif
                    @if ($igst != 0.0)
                        <td class="footer-headings-font-size border-right text-center"
                            style="padding: 4px 8px; font-weight: bold;">
                            IGST
                        </td>
                    @endif
                    <td class="footer-headings-font-size text-center"
                        style="padding: 4px 8px; font-weight: bold;">
                        Total Tax
                    </td>
                </tr>
                @php
                    $uniqueKey = 1;
                @endphp
                @foreach ($checkHsnCodeExist as $key => $item)
                    @foreach ($item as $hsnCode => $data)
                        <tr>
                            <td class="footer-contents-font-size border-right text-center"
                                style="padding: 2px 8px;">
                                {{ $uniqueKey++ }}
                            </td>
                            <td class="footer-contents-font-size border-right text-center"
                                style="padding: 2px 8px;">
                                {{ !empty($hsnCode) ? $hsnCode : '-' }}
                            </td>
                            <td class="footer-contents-font-size border-right text-center"
                                style="padding: 2px 8px;">
                                {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['taxableValue'][$hsnCode], getCompanyFixedDigitNumber()) ?? 0) }}
                            </td>
                            <td class="footer-contents-font-size border-right text-center"
                                style="padding: 2px 8px;">
                                {{ !empty($key) ? $key : '-' }}
                            </td>
                            @if ($cgst != 0.0)
                                <td class="footer-contents-font-size border-right text-center"
                                    style="padding: 2px 8px;">
                                    {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['cgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                </td>
                            @endif
                            @if ($sgst != 0.0)
                                <td
                                    class="footer-contents-font-size border-right text-center"style="padding: 2px 8px;">
                                    {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['sgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                </td>
                            @endif
                            @if ($igst != 0.0)
                                <td
                                    class="footer-contents-font-size border-right text-center"style="padding: 2px 8px;">
                                    {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['igst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                </td>
                            @endif
                            @php
                                $totalTax =
                                    round($checkTAXtExist[$key]['cgst'][$hsnCode], getCompanyFixedDigitNumber()) +
                                    round($checkTAXtExist[$key]['sgst'][$hsnCode], getCompanyFixedDigitNumber()) +
                                    round($checkTAXtExist[$key]['igst'][$hsnCode], getCompanyFixedDigitNumber());
                            @endphp
                            <td class="footer-contents-font-size text-center"
                                style="padding: 2px 8px; ">
                                {{ $pdfSymbol.getCurrencyFormat(round($totalTax ?? 0, getCompanyFixedDigitNumber())) }}
                            </td>
                        </tr>
                    @endforeach
                @endforeach
                <tr class="footer-headings-font-size border-bottom border-top fw-6"
                    style="padding: 2px 8px; ">
                    <td class="footer-headings-font-size border-right text-center fw-6"
                        style="padding: 4px 8px; "></td>
                    <td class="footer-headings-font-size border-right text-center fw-6"
                        style="padding: 4px 8px; ">
                        Total
                    </td>
                    <td class="footer-headings-font-size border-right text-center fw-6"
                        style="padding: 4px 8px; ">
                        {{ $pdfSymbol.getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber())) }}
                    </td>
                    <td class="footer-headings-font-size border-right text-center fw-6"
                        style="padding: 4px 8px; "></td>
                    @if ($cgst != 0.0)
                        <td class="footer-headings-font-size border-right text-center fw-6"
                            style="padding: 4px 8px;">
                            {{ $pdfSymbol.getCurrencyFormat(round($cgst, getCompanyFixedDigitNumber())) }}
                        </td>
                    @endif
                    @if ($sgst != 0.0)
                        <td class="footer-headings-font-size border-right text-center fw-6"
                            style="padding: 4px 8px;">
                            {{ $pdfSymbol.getCurrencyFormat(round($sgst, getCompanyFixedDigitNumber())) }}
                        </td>
                    @endif
                    @if ($igst != 0.0)
                        <td class="footer-headings-font-size border-right text-center fw-6"
                            style="padding: 4px 8px;">
                            {{ $pdfSymbol.getCurrencyFormat(round($igst, getCompanyFixedDigitNumber())) }}
                        </td>
                    @endif
                    <td class="footer-headings-font-size text-center fw-6"
                        style="padding: 4px 8px;">
                        @php
                            $grandTotalTax = $cgst + $sgst + $igst;
                        @endphp
                        {{ $pdfSymbol.getCurrencyFormat(round($grandTotalTax, getCompanyFixedDigitNumber())) }}
                    </td>
                </tr>
            </table>
        @endif
        {{-- GST Details Section End --}}

        {{-- Bank Details / Signature Section Start --}}
        <table cellpadding="0" style="page-break-inside: avoid !important">
            <tr>
                @if ($transaction->term_and_condition || $transaction->narration)
                <td class="vertical-top border-right" >
                    @if($showPrintSettings['show_sale_terms_and_conditions'] ?? true)
                    @if ($transaction->term_and_condition)
                        <div class="">
                            <h4 class="terms-and-conditions-font-size fw-6 "
                                style="padding: 4px 8px;">
                                {{ $changeLabel['terms_and_conditions'] ?? 'Terms and Conditions' }}:
                            </h4>
                            <div style="padding: 4px 8px"
                                class="terms-and-conditions-font-size">
                                <p class="fs-12 terms-and-conditions-font-size" >
                                    {!! nl2br($transaction->term_and_condition) !!}
                                </p>
                            </div>
                        </div>
                    @endif
                    @endif
                    @if($showPrintSettings['show_sale_narration'] ?? true)
                    @if ($transaction->narration)
                        <div class="">
                            <h4 class="note-font-size {{ $transaction->term_and_condition ? 'border-top' : '' }} fw-6"
                                style="padding: 4px 8px;">
                                {{ $changeLabel['narration'] ?? 'Notes' }}:
                            </h4>
                            <div style="padding: 4px 8px;">
                                <p class="note-font-size" >
                                    {!! nl2br($transaction->narration) !!}
                                </p>
                            </div>
                        </div>
                    @endif
                    @endif
                </td>
            @endif
                @if(($showPrintSettings['show_sale_authorized_signatory'] ?? true) || ($invoiceSetting['signature'] ?? true))
                <td class="vertical-bottom" style="width: 217px; position: relative">
                    <div style="padding:4px 8px; margin-left:auto;">
                        @if ($showPrintSettings['show_sale_authorized_signatory'] ?? true)
                            <p class="footer-headings-font-size fw-6 text-end" style="margin-top:10px; ">
                                For, {{ strtoupper($currentCompany->trade_name) }}
                            </p>
                        @endif
                        <div class="text-end signature">
                            @if (
                                ($invoiceSetting['signature'] ?? false) &&
                                    $currentCompany->company_signature != asset('images/preview-img.png'))
                                <img src="{{ $currentCompany->company_signature ?? null }}" alt="company-img"
                                    style="max-width:100%; height:100%;  object-fit:contain; margin-left:auto;">
                            @endif
                        </div>
                        @if ($showPrintSettings['show_sale_authorized_signatory'] ?? true)
                            <p class="verical-bottom text-end footer-contents-font-size" style="">
                                {{ $changeLabel['authorized_signatory'] ?? 'Authorized Signatory' }}
                            </p>
                        @endif
                    </div>
                </td>
                @endif
            </tr>
        </table>
        {{-- Terms and Condition / Narration / Signature Section End --}}
    </div>
</body>

</html>
