<!DOCTYPE html>
<html>
@php
    $pdfSymbol = getPrintPdfCurrencySymbol($currentCompany->id);
    $dockereEnabled = dockerEnabled() && !isset($preview_enabled);
    $fontStyleName = $dockereEnabled ? 'Arial Unicode MS' : 'Arial-unicode-ms';
    $fontCssName = $dockereEnabled ? 'font-docker.css' : 'font.css';
@endphp

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/' . $fontCssName) }}">
    <title>{{ isset($fileName) ? $fileName : $transaction->document_number }}</title>
    @include('company.pdf.sale_pdf.print_custom_font_css_variables')
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "{{ $fontStyleName }}";
            font-size: 13px;
            font-weight: 400;
        }

        @page {
            margin: 20px !important;
        }

        .main-table {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        table {
            width: 100%;
            display: table;
            max-width: 100%;
        }

        table td,
        table th {
            display: table-cell;
        }

        .title-table {
            position: relative;
            padding: 16px 0px 20px 20px
        }

        .title-table::after {
            position: absolute;
            background-color: var(--color-theme);
            content: "";
            width: 100%;
            height: 100%;
            z-index: -1;
            top: 0px;
            right: 0;
            border-radius: 0px 0px 0px 90px;
        }

        .ls-1px {
            letter-spacing: 1px;
        }

        .mt-2 {
            margin-top: 12px;
        }

        .text-primary {
            color: var(--color-theme);
        }

        .bg-primary {
            background-color: var(--color-theme);
        }

        .bg-light {
            background-color: #f4f7fe;
        }

        .text-white {
            color: white;
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
            }
        }

        .fs-14 {
            font-size: 14px;
        }

        .fs-13 {
            font-size: 13px;
        }

        .fs-12 {
            font-size: 12px;
        }

        .fs-10 {
            font-size: 10px;
        }

        .fw-semibold {
            font-weight: 600;
        }

        .text-black {
            color: black;
        }

        .text-end {
            text-align: end;
        }

        .text-start {
            text-align: start;
        }

        .text-center {
            text-align: center;
        }

        .logo-img {
            max-width: 110px;
            max-height: 110px;
            height: 100%;
            aspect-ratio: 110/110;
            margin-bottom: 5px;
        }

        .text-gray {
            color: #d9d9d9 !important;
        }

        .fs-16 {
            font-size: 16px;
        }

        .w-100 {
            width: 100%
        }

        .mb-1 {
            margin-bottom: 4px;
        }

        .pb-1 {
            padding-bottom: 4px;
        }

        .ps-10px {
            padding-left: 14px;
        }

        .pb-10px {
            padding-bottom: 10px;
        }

        .pb-12px {
            padding-bottom: 12px;
        }

        address {
            font-style: normal;
        }

        .text-decoration-none {
            text-decoration: none
        }

        .px-4 {
            padding: 0 24px;
        }

        .text-nowrap {
            white-space: nowrap;
        }

        .w-44 {
            width: 44.5%;
        }

        .w-40 {
            width: 40%;
        }

        .vertical-align-top {
            vertical-align: top;
        }

        .lh-1-5 {
            line-height: 1.5;
        }

        .mb-1px {
            margin-bottom: 1px;
        }

        .pb-12px {
            padding-bottom: 12px;
        }

        .pb-4 {
            padding-bottom: 24px;
        }

        .justify-content-between {
            justify-content: space-between;
        }

        .align-items-center {
            align-items: center;
        }

        .justify-content-end {
            justify-content: end;
        }

        .d-flex {
            display: flex;
        }

        .flex-column {
            flex-direction: column;
        }

        .h-100 {
            height: 100%
        }

        .gap-3px {
            gap: 3px;
        }

        .fs-25 {
            font-size: 25px;
        }

        .mb-4 {
            margin-bottom: 24px;
        }

        .w-33 {
            width: 33.33%
        }

        .pe-4 {
            padding-right: 24px !important;
        }

        .mb-10px {
            margin-bottom: 10px;
        }

        .mb-6px {
            margin-bottom: 6px;
        }

        .px-3 {
            padding-left: 16px !important;
            padding-right: 16px !important;
        }

        .py-2 {
            padding-top: 8px !important;
            padding-bottom: 8px !important;
        }

        .item-table,
        .total-table {
            border-collapse: collapse;
        }

        .item-table th,
        .item-table tfoot td {
            border: 1px solid black;
        }

        .item-table tbody td {
            border-right: 1px solid black;
        }

        .item-table-main-content td:first-child {
            border-left: 1px solid black;
        }

        .total-foot {
            border-top: 1px solid black;
            border-bottom: 1px solid black;
        }

        .px-2 {
            padding: 0 8px;
        }

        .fw-normal {
            font-weight: 400;
        }

        .pe-1 {
            padding-right: 4px !important;
        }

        .pt-4 {
            padding-top: 24px;
        }

        .pt-3 {
            padding-top: 16px;
        }

        .pb-3 {
            padding-bottom: 16px
        }

        .w-66 {
            width: 66.9%;
        }

        .fs-11 {
            font-size: 11px;
        }

        .total-table td {
            border-right: 1px solid black;
            border-left: 1px solid black !important;
        }


        .py-1 {
            padding: 4px 0;
        }

        .gap-30px {
            gap: 30px
        }

        .position-relative {
            position: relative;
        }

        .qr-code {
            max-width: 75px;
            min-width: 75px;
            height: 75px;
        }

        .my-30px {
            margin-top: 12px;
            margin-bottom: 12px;
        }

        .sign {
            background-color: rgb(247 247 247);
            height: 135px;
            width: 200px;
            justify-content: center;
            overflow: hidden;
        }

        .ms-auto {
            margin-left: auto;
        }

        .p-2 {
            padding: 8px;
        }

        .w-50 {
            width: 50%;
        }

        .-mt-20px {
            margin-top: -20px;
        }

        .mt-20px {
            margin-top: 20px;
        }

        .mt-5 {
            margin-top: 48px
        }

        .sign-section {
            width: 200px;
        }

        .w-75 {
            width: 75%;
        }

        .pt-10px {
            padding-top: 10px;
        }

        .pt-1 {
            padding-top: 4px;
        }

        table {
            border-collapse: collapse;
        }

        .w-25 {
            width: 25%;
        }

        .w-30 {
            width: 30%;
        }

        .w-70 {
            width: 70%;
        }

        .tax-name {
            margin-top: -75px;
            margin-bottom: 31px;
        }

        .flex-grow-1 {
            flex-grow: 1;
        }


        .item-table tbody tr:last-child td {
            height: 100%
        }

        .mb-2 {
            margin-bottom: 8px;
        }

        .pb-2 {
            padding-bottom: 8px;
        }

        .mb-2px {
            margin-bottom: 2px;
        }

        .px-1 {
            padding: 0 4px;
        }

        .mb-20px {
            margin-bottom: 20px;
        }

        .mb-3 {
            margin-bottom: 16px;
        }

        .fs-15 {
            font-size: 15px;
        }

        .ps-1 {
            padding-left: 4px;
        }

        .word-break-all {
            word-break: break-all;
        }

        .w-120px {
            width: 120px;
        }

        .w-550px {
            width: 550px;
        }

        .w-auto {
            width: auto;
        }
        .w-25px{
            width: 25px
        }
        .w-100px{
            width: 100px
        }

    </style>
     @include('company.pdf.sale_pdf.print_custom_font_css')
</head>

<body>
    <div class="main-table">
        {{-- INVOICE TYPE --}}
        <div class="position-relative">
            {{-- Slogan Section Start --}}
            @if (($invoiceSetting['show_slogan'] ?? false) && isset($invoiceSetting['slogan']))
                <h6 class="text-center text-black company-address-font-size mb-2px">
                        {{ $invoiceSetting['slogan'] }}
                </h6>
            @endif
            <h6 class="text-black fs-12 text-end mb-2px" style="letter-spacing:0.3px; position:absolute; right:0; top:0;">
                ({#INVOICE_TYPE#})
            </h6>
        </div>
        <div>
            <table class="mb-4 w-100 h-100">
                <tbody>
                    <tr>
                        {{-- LOGO / COMPANY NAME --}}
                        <td class="w-30 vertical-align-top">
                            <div class="ps-10px pb-12px">
                                @if (isset($currentCompany->company_logo) && ($invoiceSetting['logo'] ?? true) && $currentCompany->company_logo != asset('images/preview-img.png'))
                                    <div class="logo-img">
                                        <img src={{ $currentCompany->company_logo ?? '' }} alt="Logo"
                                            style="object-fit:contain;" class="h-100 w-100">
                                    </div>
                                @endif
                                <h2 class="mb-2 text-black company-name-font-size company-font-customization fw-semibold">
                                    {{ strtoupper($currentCompany->trade_name) }}
                                </h2>
                                @if(($invoiceSetting['prop_details'] ?? true) && isset($customProp))
                                    <p class="mt-1 fw-6 company-address-font-size">{{ $customProp->label_name.' : '.$customProp->label_value }}</p>
                                @endif
                                @if ($currentCompany->is_gst_applicable)
                                    <p class="text-black company-address-font-size fw-semibold text-nowrap">
                                        {{ $changeLabel['gstin'] ?? 'GSTIN' }}
                                        {{ $currentCompany->companyTax->gstin ?? ($customerDetail->model->gstin ?? null) }}
                                    </p>
                                @endif
                            </div>
                        </td>
                        {{-- CONTACT DETAILS --}}
                        <td class="w-80 vertical-align-top">
                            <div class="d-flex justify-content-between title-table">
                                <div class="w-70">
                                    @if ($invoiceSetting['email'] ?? true)
                                        <a class="mb-1 text-white d-flex company-address-font-size fw-semibold text-decoration-none word-break-all">
                                            {{ $invoiceSetting['alternate_email'] ?? ($currentCompany->user->email ?? null) }}
                                        </a>
                                    @endif
                                    @if ($invoiceSetting['mobile_number'] ?? true)
                                    <p class="px-2 pt-0 text-white company-address-font-size fw-semibold mb-1px text-nowrap d-flex flex-column">
                                        <span class="company-address-font-size fw-semibold">
                                            {{ $changeLabel['tel'] ?? 'Phone No' }}:
                                            {{ (isset($invoiceSetting['region_code']) ? '+' . $invoiceSetting['region_code'] . ' ' : '') .
                                                (isset($invoiceSetting['alternate_phone'])
                                                    ? $invoiceSetting['alternate_phone']
                                                    : '+' . $currentCompany->user->region_code . ' ' . $currentCompany->user->phone) }}
                                            @if(isset($invoiceSetting['alternate_phone_2']))
                                             /
                                            @endif
                                        </span>
                                        @if(isset($invoiceSetting['alternate_phone_2']))
                                            <span class="px-3 mb-1 text-white d-flex company-address-font-size fw-semibold word-break-all">
                                               {{ isset($invoiceSetting['region_code_2']) ? '+'.$invoiceSetting['region_code_2'].' ' : '' }}
                                                {{ $invoiceSetting['alternate_phone_2'] }}
                                            </span>
                                        @endif
                                    </p>
                                    @endif
                                </div>
                                <div>
                                    <address class="text-white fw-semibold lh-1-5 company-address-font-size">
                                        {{ isset($companyBillingAddress->address_1) ? strtoupper($companyBillingAddress->address_1 . ',') : null }}
                                        {{ isset($companyBillingAddress->address_2) ? strtoupper($companyBillingAddress->address_2 . ',') : null }}
                                        {{ isset($companyBillingAddress->city_id)
                                            ? strtoupper(getCityName($companyBillingAddress->city_id) . ',')
                                            : null }}
                                        {{ isset($companyBillingAddress->state_id)
                                            ? strtoupper(getStateName($companyBillingAddress->state_id) . ',')
                                            : null }}
                                        {{ isset($companyBillingAddress->country_id)
                                            ? strtoupper(getCountryName($companyBillingAddress->country_id) . ',')
                                            : null }}
                                        {{ isset($companyBillingAddress->pin_code) ? ' - ' . $companyBillingAddress->pin_code : null }}
                                    </address>
                                </div>
                            </div>
                            <h1 class="text-black text-end fs-25 ls-1px mt-20px">
                                {{ $taxInvoice }}
                            </h1>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mb-20px">

            <table>
                <tbody>
                    {{-- BILL TO --}}
                    <td class="w-33">
                        <div>
                            <p class="px-1 text-primary fw-semibold header-labels-font-size lh-1-5">
                                {{ $changeLabel['bill_to'] ?? 'Bill To' }}:
                            </p>
                            <p class="px-1 text-black header-contents-font-size fw-semibold lh-1-5">{{ strtoupper($customerDetail->name) }}
                            </p>
                            @if($showGst)
                            <p class="px-1 text-black header-contents-font-size fw-semibold lh-1-5">GSTIN :
                                {{ $transaction->gstin ?? ($customerDetail->model->gstin ?? null) }}
                            </p>
                            @endif
                            @if (isset($billingAddress))
                            <p class="px-1 text-black header-contents-font-size fw-semibold lh-1-5">
                                @if ($billingAddress->address_1 != null)
                                    {{ strtoupper($billingAddress->address_1) }}
                                @endif
                                @if ($billingAddress->address_2 != null)
                                    {{ strtoupper($billingAddress->address_2) }},
                                @endif
                                {{ $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                                {{ $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                                {{ strtoupper(getCountryName($billingAddress->country_id) ?? null) }}
                                {{ $billingAddress->pin_code ? ' - ' . $billingAddress->pin_code : null }}
                            </p>
                            @endif
                            <p class="px-1 text-black header-contents-font-size fw-semibold mb-1px text-nowrap d-flex lh-1-5">
                                @if (!empty($transaction->party_phone_number))
                                    Contact No:
                                        +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                                @elseif (!empty($customerDetail->model->phone_1))
                                    Contact No:
                                        +{{ $customerDetail->model->region_code_1 ?? '' }} {{ $customerDetail->model->phone_1 ?? null }}
                                @endif
                                @if (!empty($customerDetail->model->phone_2) && ($customerDetail->model->phone_1 !== $customerDetail->model->phone_2))
                                    {{ empty($customerDetail->model->phone_1) && empty($transaction->party_phone_number) ? 'Contact No:' : "|" }}
                                        +{{ $customerDetail->model->region_code_2 ?? '' }} {{ $customerDetail->model->phone_2 ?? null }}
                                @endif
                            </p>
                            @if (!empty($customerDetail->model->person_email))
                                <p class="px-1 text-black header-contents-font-size fw-semibold mb-1px text-nowrap d-flex lh-1-5">
                                    Email: {{ $customerDetail->model->person_email ?? null }}
                                </p>
                            @endif
                            @if (!empty($panNumber) && $showPanNumber)
                                <p class="px-1 text-black header-contents-font-size fw-semibold mb-1px text-nowrap d-flex lh-1-5">
                                    PAN: {{ $panNumber ?? null }}
                                </p>
                            @endif
                        </div>
                    </td>
                    {{-- TRANSPORT DETAIL --}}
                    <td class="w-33 vertical-align-top">
                        <div>
                            <table>
                                <tbody>
                                    @if (($invoiceSetting['transport_details'] ?? true) && !empty($transaction->transport))
                                        <tr>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4 text-nowrap">
                                                {{ $changeLabel['transport_name'] ?? 'Transport Name' }}
                                            </td>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                :{{ ' ' . $transaction->transport->transporter_name ?? '' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4 text-nowrap">
                                                GSTIN
                                            </td>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                :{{ ' ' . $transaction->transport->gstin ?? '' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4 text-nowrap">
                                                {{ $changeLabel['document_no'] ?? 'Document No.' }}
                                            </td>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                :{{ ' ' . $transaction->transporter_document_number ?? '' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4 text-nowrap">
                                                {{ $changeLabel['document_date'] ?? 'Document Date' }}
                                            </td>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                :{{ !empty($transaction->transporter_document_date)
                                                    ? ' ' . \Carbon\Carbon::parse($transaction->transporter_document_date)->format('d-m-Y')
                                                    : '' }}
                                            </td>
                                        </tr>
                                        @if (!empty($transaction->transporter_vehicle_number))
                                            <tr>
                                                <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4 text-nowrap">
                                                    {{ $changeLabel['transport_vehicle_number'] ?? 'Vehicle No.' }}
                                                </td>
                                                <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                    :{{ ' ' . $transaction->transporter_vehicle_number ?? '' }}
                                                </td>
                                            </tr>
                                        @endif
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </td>
                    <td class="w-33 vertical-align-top">
                        <div>
                            <table>
                                <tbody>
                                    <tr>
                                        <td class="px-1 text-black header-labels-font-size lh-1-5 fw-semibold pe-4 text-nowrap">
                                            {{ $invoiceNumberLabel }}
                                        </td>
                                        <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                            :{{ ' ' . $transaction->full_invoice_number }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-1 text-black header-labels-font-size lh-1-5 fw-semibold pe-4 text-nowrap">
                                            {{ $invoiceDateLabel }}
                                        </td>
                                        <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                            :{{ ' ' . $invoiceDate }}
                                        </td>
                                    </tr>
                                    @if(isset($originalInvoiceNumber) && !empty($originalInvoiceNumber) && isset($originalInvoiceDate) && !empty($originalInvoiceDate))
                                    <tr>
                                        <td class="px-1 text-black header-labels-font-size lh-1-5 fw-semibold pe-4 text-nowrap">
                                            Original Invoice No:
                                        </td>
                                        <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                            : {{ Illuminate\Support\Str::limit($originalInvoiceNumber, 20, '...')  }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-1 text-black header-labels-font-size lh-1-5 fw-semibold pe-4 text-nowrap">
                                            Original Invoice Date:
                                        </td>
                                        <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                            :{{ isset($originalInvoiceDate) ? \Carbon\Carbon::parse($originalInvoiceDate)->format('d-m-Y') : null }}
                                        </td>
                                    </tr>
                                    @endif
                                    @if($invoiceSetting['show_due_date'] ?? true)
                                        <tr>
                                            <td class="px-1 text-black header-labels-font-size lh-1-5 fw-semibold pe-4 text-nowrap">
                                                {{ $changeLabel['due_date'] ?? 'Due Date' }}
                                            </td>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                :{{ ' ' . $dueDate }}
                                            </td>
                                        </tr>
                                    @endif
                                    <tr>
                                        <td class="px-1 text-black header-labels-font-size lh-1-5 fw-semibold pe-4 text-nowrap">
                                            Place of Supply
                                        </td>
                                        <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                            :{{ !isset($shippingAddress->state_id)
                                                ? (isset($ledgerShippingAddress->state_id)
                                                    ? ' ' . strtoupper(getStateName($ledgerShippingAddress->state_id))
                                                    : '')
                                                : ' ' . strtoupper(getStateName($shippingAddress->state_id)) }}
                                        </td>
                                    </tr>

                                        <tr>
                                            @if ($invoiceSetting['show_credit_period'] ?? true)
                                            <td class="px-1 text-black header-labels-font-size lh-1-5 fw-semibold pe-4 text-nowrap">
                                                {{ $changeLabel['credit_period'] ?? 'Credit Period' }}
                                            </td>

                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                :{{ ' ' . $creditPeriod }}
                                            </td>
                                            @endif
                                        </tr>

                                </tbody>
                            </table>
                        </div>
                    </td>
                </tbody>
            </table>
        </div>
        <div class="mb-3">
            <table>
                <tbody>
                    @if ($invoiceSetting['ship_to_details'] ?? true)
                        <td class="w-33">
                            <div>
                                <p class="px-1 text-primary fw-semibold header-labels-font-size lh-1-5">
                                    {{ $changeLabel['ship_to'] ?? 'Ship To' }}:
                                </p>
                                <p class="px-1 text-black header-contents-font-size fw-semibold lh-1-5">
                                    {{ strtoupper($transaction->shipping_name ?? $customerDetail->name) }}
                                </p>
                                @if ($isCompanyGstApplicable && !empty($transaction->shipping_gstin))
                                <p class="px-1 text-black header-contents-font-size fw-semibold lh-1-5">
                                    GSTIN :
                                    {{ $transaction->shipping_gstin ?? null }}
                                </p>
                                @endif
                                <p class="px-1 text-black header-contents-font-size fw-semibold lh-1-5">
                                    @if (isset($shippingAddress->address_1))
                                        {{ strtoupper($shippingAddress->address_1) }},
                                    @endif
                                    @if (isset($shippingAddress->address_2))
                                        {{ strtoupper($shippingAddress->address_2) }},
                                    @endif
                                    {{ isset($shippingAddress->city_id)
                                        ? strtoupper(getCityName($shippingAddress->city_id)) . ',' : '' }}

                                    {{ isset($shippingAddress->state_id)
                                        ? strtoupper(getStateName($shippingAddress->state_id)) . ','
                                        : ''}}

                                    {{ isset($shippingAddress->country_id) ? strtoupper(getCountryName($shippingAddress->country_id)) : '' }}
                                    {{ isset($shippingAddress->pin_code) ? ' - ' . $shippingAddress->pin_code : null }}
                                </p>
                                @if (!empty($panNumber) && $showPanNumber)
                                    <p class="px-1 text-black header-contents-font-size fw-semibold mb-1px text-nowrap d-flex lh-1-5">
                                        PAN: {{ $panNumber ?? null }}
                                    </p>
                                @endif
                                <p class="px-1 text-black header-contents-font-size fw-semibold lh-1-5">
                                    @if (!empty($transaction->party_phone_number))
                                        Contact No:
                                            +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                                    @endif
                                </p>
                            </div>
                        </td>
                    @endif
                    @if ((($invoiceSetting['po_number'] ?? true) && (!empty($transaction->po_no ?? null) || !empty($transaction->po_date ?? null)))
                        || (isset($deliveryChallan) && !empty($deliveryChallan))
                        || (($invoiceSetting['broker_details'] ?? true) && !empty($transaction->brokerDetails)))
                        <td class="w-33 vertical-align-top">
                            <div>
                                <table>
                                    <tbody>
                                        @if (
                                            ($invoiceSetting['po_number'] ?? true) &&
                                                (!empty($transaction->po_no ?? null) || !empty($transaction->po_date ?? null)))
                                            <tr>
                                                <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4">
                                                    {{ $changeLabel['po_number_label'] ?? 'PO No.' }}
                                                </td>
                                                <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                    :{{ ' ' . $transaction->po_no }}
                                                </td>
                                            </tr>
                                        @endif
                                        @if($invoiceSetting['show_po_date'] ?? true)
                                            <tr>
                                                <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4">
                                                    {{ $changeLabel['po_date'] ?? 'PO Date' }}
                                                </td>
                                                <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                    :{{ !empty($transaction->po_date) ? ' ' . \Carbon\Carbon::parse($transaction->po_date)->format('d-m-Y') : null }}
                                                </td>
                                            </tr>
                                        @endif
                                        @if (isset($deliveryChallan) && !empty($deliveryChallan))
                                            <tr>
                                                <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4">
                                                    {{ $changeLabel['delivery_challan'] ?? 'Delivery Challan No' }}
                                                </td>
                                                <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                    :{{ ' ' . Illuminate\Support\Str::limit($deliveryChallanInvoiceNumber, 20, '...') }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4">
                                                    {{ $changeLabel['delivery_challan'] ?? 'Delivery Date' }}:
                                                </td>
                                                <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                    :{{ ' ' . $deliveryChallanInvoiceDate }}
                                                </td>
                                            </tr>
                                        @endif
                                        @if (($invoiceSetting['broker_details'] ?? true) && !empty($transaction->brokerDetails))
                                            <tr>
                                                <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4">
                                                    Broker
                                                </td>
                                                <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                    :{{ ' ' . $transaction->brokerDetails->broker_name ?? '' }}
                                                </td>
                                            </tr>
                                            @if($isCompanyGstApplicable)
                                            <tr>
                                                <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4">
                                                    GSTIN
                                                </td>
                                                <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                    :{{ ' ' . $transaction->brokerDetails->gstin ?? '' }}
                                                </td>
                                            </tr>
                                            @endif
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    @endif
                    <td class="w-33 vertical-align-top">
                        <div>
                            <table style="max-width: 450px">
                                <tbody>
                                    @if (isset($eWayBill) || (isset($transaction->eway_bill_number) || isset($transaction->eway_bill_date)))
                                        <tr>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4">
                                                {{ $changeLabel['e_way_bill_no'] ?? 'E-way Bill No.' }}
                                            </td>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                : {{ $eWayBill?->eway_bill_no ?? $transaction->eway_bill_number ?? null }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4">
                                                {{ $changeLabel['e_way_bill_date'] ?? 'E-way Bill Date' }}
                                            </td>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                : {{ \Carbon\Carbon::parse($eWayBill?->eway_bill_date)->format('d-m-Y') ?? \Carbon\Carbon::parse($transaction->eway_bill_date)->format('d-m-Y') ?? null }}
                                            </td>
                                        </tr>
                                    @endif
                                    @if (!empty($eInvoice))
                                        <tr>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4">
                                                {{ $changeLabel['ack_no'] ?? 'Ack No' }}
                                            </td>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                :{{ ' ' . $eInvoice->ack_no ?? '' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4">
                                                {{ $changeLabel['ack_date'] ?? 'Ack Date' }}
                                            </td>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                :{{ !empty($eInvoice->ack_date) ? ' ' . \Carbon\Carbon::parse($eInvoice->ack_date)->format('d-m-Y') : '' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4">
                                                {{ $changeLabel['irn'] ?? 'IRN' }}
                                            </td>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold" style="word-break: break-all">
                                                :{{ ' ' . $eInvoice->irn ?? '' }}
                                            </td>
                                        </tr>
                                    @endif
                                    @foreach (printCustomPDFLabelsForSale() as $key => $customLabel)
                                        <tr>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold pe-4">
                                                {{ $key ?? null }}
                                            </td>
                                            <td class="px-1 text-black header-contents-font-size lh-1-5 fw-semibold">
                                                :{{ ' ' . $customLabel ?? null }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tbody>
            </table>
        </div>

        {{-- Custom Fields Section Start --}}
        @if (count($customFieldValues) > 0)
            @php
                $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
            @endphp
            <div class="mb-3">
                <table cellpadding="0">
                    @foreach ($customFields->chunk(3) as $chunk)
                        <tr class="border-bottom">
                            @foreach ($chunk as $customField)
                                <td class="fs-13 px-1">
                                    <span style="font-weight: bold;">{{ $customField['label_name'] ?? '' }}</span> : {{ $customField['value'] ?? '' }}
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </table>
            </div>
        @endif
        {{-- Custom Fields Section End --}}

        <div class="flex-grow-1 d-flex">
            <table class="w-100 item-table">
                <thead>
                <tr class="border-bottom">
                    @foreach ($rearrangeItems['headings'] as $headings)
                        @if($headings['is_show_in_print'])
                            <th class="px-2 py-2 text-white bg-primary table-headings-font-size fw-normal {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }} {{ $headings['class'] }}"
                                style="padding: 6px 8px; font-weight: bold;">
                                {{ $headings['name'] }}
                            </th>
                        @endif
                    @endforeach
                </tr>
                </thead>
                <tbody>
                @foreach ($rearrangeItems['detail'] as $items)
                    <tr class="item-table-main-content">
                        @foreach ($items as  $key => $item)
                            @if($item['is_show_in_print'])
                                <td class="px-2 pt-1 pb-1 text-black table-contents-font-size vertical-align-top text-nowrap {{ ($key == 'item_name') ? 'min-width-150' : 'text-center'}}"
                                    style="padding: 4px 8px 0 8px; {{ ($key == 'primary_quantity' || $key == 'secondary_quantity') ? 'white-space: nowrap;' : '' }} ">
                                    {{ $item['value'] }}
                                    @if($key == 'item_name')
                                        @if($item['show_sku'])
                                            <p class="description-font-size">Item Code:
                                                {{ $item['sku'] ?? null }}</p>
                                        @endif
                                        @if($item['show_consolidating_items'])
                                            <p style="word-break: break-word; " class="description-font-size">
                                                {!! $item['consolidating_items'] !!}
                                            </p>
                                        @endif
                                        @if($item['show_additional_description'])
                                            <p style="word-break: break-word;" class="description-font-size">
                                                {!! $item['additional_description'] !!}
                                            </p>
                                        @endif
                                        @if($item['show_item_image'])
                                            <div><img src="{{ $item['item_image'] }}" width="60"  height="60" style="margin-top: 4px"></div>
                                        @endif
                                    @endif
                                </td>
                            @endif
                        @endforeach
                    </tr>
                @endforeach
                </tbody>
                <tfoot>
                <tr class="item-table-main-content">
                    @foreach ($rearrangeItems['footer'] as $key => $footer)
                            @if($footer['is_show_in_print'])
                                <td class="px-3 py-2 text-white table-headings-font-size bg-primary fw-normal total-foot vertical-align-top {{ ($loop->index === $loop->count - 2) ? '' : 'border-right' }}  fw-6 {{ ($key == 'item_name') ? 'min-width-150 ps-2' : 'text-center'}}"
                                style="">{{ $footer['value'] }}</td>
                            @endif
                    @endforeach
                </tr>
                </tfoot>
            </table>
        </div>
        {{-- Total Section Start --}}
        @php
            if (empty($addLess)) {
                $total = $transaction->grand_total;
            } else {
                $addLessSum =  array_sum(array_column($addLess, 'amount'));
                $total = $transaction->grand_total - $addLessSum;
                $addLessSumTotal = collect($addLess)->where('is_show_in_print',1)->sum('amount');
                $total = $total + $addLessSumTotal;
            }
        @endphp
        <div class="d-flex justify-content-between w-100">
            <div class="w-auto flex-grow-1" style="max-width: 630px">
                @if ($showPrintSettings['show_sale_in_words'] ?? true)
                    <div class="d-flex gap-3px my-30px">
                        <p class="text-black table-contents-font-size lh-1-5 fw-semibold text-nowrap">
                            {{ $changeLabel['in_words'] ?? 'Total In Words' }}:
                        </p>
                        <p class="table-contents-font-size text-primary fw-semibold ps-1">
                            {{ getAmountToWord($total ?? '0.0') }} Only
                        </p>
                    </div>
                @endif
                {{-- Bank & QR Code Start --}}
                @if (($invoiceSetting['qr_code'] ?? false && isset($bankDetail->upi_id)) ||
                ($invoiceSetting['bank_details'] ?? true) || ($invoiceSetting['show_payment_status'] ?? false))
                <div class="d-flex gap-30px {{ ($invoiceSetting['qr_code'] ?? false && isset($bankDetail->upi_id)) ||
                ($invoiceSetting['bank_details'] ?? true) ? 'align-items-center' : 'justify-content-end' }}">
                    @if ((isset($invoiceSetting['qr_code']) && $invoiceSetting['qr_code'] == 1 ? true : false) &&
                    isset($bankDetail->upi_id))
                    <div>
                        <div class="qr-code w-100 h-100">
                            <img src="data:image/svg+xml;base64,{{ base64_encode(generateUPIQRCode($bankDetail->upi_id, $transaction->due_amount)) }}"
                                width="85" height="85" />
                        </div>
                        <p class="pb-1 text-black footer-contents-font-size lh-1-5" style="margin-top: 12px;">
                            {{ $bankDetail->upi_id }}
                        </p>
                    </div>
                    @endif
                    @if ($invoiceSetting['bank_details'] ?? true)
                    <div class="flex-grow-1 "
                        style="{{ ($showPrintSettings['show_sale_in_words'] ?? true) ? '' : 'margin-top: 10px;' }}">
                        <p class="pb-1 text-black footer-headings-font-size lh-1-5 fw-semibold">
                            Bank Details
                        </p>
                        <p class="pb-1 text-black footer-contents-font-size lh-1-5">
                            <span class="fw-semibold footer-headings-font-size">Bank:</span>
                            <span class="fw-semibold footer-contents-font-size">
                             {{ !empty($bankDetail) ? $bankDetail->bank_name : null }}.
                            </span>
                        </p>
                        <p class="pb-1 text-black footer-contents-font-size lh-1-5">
                            <span class="fw-semibold footer-headings-font-size">Account no.:</span>
                            <span class="fw-semibold footer-contents-font-size">{{ $accountNumber }}</span>
                        </p>
                        <p class="pb-1 text-black footer-contents-font-size lh-1-5">
                            <span class="fw-semibold footer-headings-font-size">IFSC Code:</span>
                            <span class="fw-semibold footer-contents-font-size">{{ !empty($bankDetail) ? $bankDetail->ifsc_code : null }}</span>
                        </p>
                        @if (isset($bankDetail->account_holder_name))
                            <p class="pb-1 text-black footer-contents-font-size lh-1-5">
                                <span class="fw-semibold footer-headings-font-size">A/C Name:</span>
                                <span class="fw-semibold footer-contents-font-size"> {{ $bankDetail->account_holder_name }} </span>
                            </p>
                        @endif
                        @if (!empty($bankDetail) && $bankDetail->swift_code != null)
                        <p class="pb-1 text-black footer-contents-font-size lh-1-5">
                            <span class="fw-semibold footer-headings-font-size">Swift Code:</span>
                            <span class="fw-semibold footer-contents-font-size">{{ !empty($bankDetail) ? $bankDetail->swift_code : null }}</span>
                        </p>
                        @endif
                        <P class="text-black footer-contents-font-size lh-1-5">
                            <span class="fw-semibold footer-headings-font-size">Branch:</span>
                            <span class="fw-semibold footer-contents-font-size">{{ $branchName }}</span>

                        </P>
                    </div>
                    @endif
                    @if($invoiceSetting['show_payment_status'] ?? false)
                    @if($transaction->payment_status == 'Partially Unpaid')
                           <div style="display: flex; justify-content:center; align-items:center; margin-right: 10px;">
                             <div style="border:2px solid #4f158c; width:fit-content; margin:8px auto; padding:4px 8px; display:inline-block;">
                                <h5 class="text-primary text-center mb-0" style="font-weight:700; font-size:14px;"> PARTLY</br>
                                 PAID</h5>
                            </div>
                           </div>
                        @elseif ($transaction->payment_status == 'Paid')
                            <div class="flex-grow-1">
                                <div style="display: flex; justify-content:center; align-items:center; margin-right: 10px;">
                                <div
                                    style="padding:2px; width:fit-content; background: linear-gradient(to top,#43ad52,#a6d052); margin:8px auto; display:inline;">
                                    <div style="background-color: white; padding:4px 8px;">
                                        <h4 class="text-center"
                                            style="font-weight:700; font-size:14px; background: -webkit-linear-gradient(#a6d052, #43ad52); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                                            PAID</h4>
                                    </div>
                                </div>
                                </div>
                            </div>
                        @elseif($transaction->payment_status == 'Unpaid')
                            <div style="display: flex; justify-content:center; align-items:center; margin-right: 10px;" class="flex-grow-1">
                                <div style="padding:2px; width:fit-content; background: linear-gradient(to top,#801520,#c30a17); margin:8px auto; display:inline;">
                                    <div style="background-color: white; padding:4px 8px;">
                                        <h4 class="text-center"
                                            style="font-weight:700; font-size:14px; background: -webkit-linear-gradient(#c30a17, #801520); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                                            UNPAID</h4>
                                    </div>
                                </div>
                            </div>
                        @endif
                        @endif
                </div>
                @endif
                {{-- Bank & QR Code End --}}
                @if ($showPrintSettings['show_sale_narration'] ?? true)
                    @if ($transaction->narration)
                        <div>
                            <p class="pb-2 text-black note-font-size fw-semibold lh-1-5">
                                {{ $changeLabel['narration'] ?? 'Note' }}:
                            </p>
                            <p class="pb-4 text-black note-font-size lh-1-5">
                                {!! nl2br($transaction->narration) !!}
                            </p>
                        </div>
                    @endif
                @endif
                @if ($showPrintSettings['show_sale_terms_and_conditions'] ?? true)
                    @if ($transaction->term_and_condition)
                        <p class="pb-2 text-black terms-and-conditions-font-size fw-semibold lh-1-5">
                            {{ $changeLabel['terms_and_conditions'] ?? 'Terms and Conditions' }}:
                        </p>
                        <p class="pb-1 text-black terms-and-conditions-font-size lh-1-5">
                            {!! nl2br($transaction->term_and_condition) !!}
                        </p>
                    @endif
                @endif
            </div>
            <div>
                <div>
                    <table class="w-100 total-table">
                        <tbody>
                            @if (!empty($additionalCharges) && is_array($additionalCharges))
                                @foreach ($additionalCharges as $key => $addCharge)
                                    <tr>
                                        <td class="px-2 text-black bg-light table-contents-font-size fw-semibold lh-1-5">
                                            {{ $addCharge['ledger_name'] ?? '' }}
                                        </td>
                                        <td class="text-black bg-light table-contents-font-size fw-semibold pe-1 text-end lh-1-5 w-120px">
                                            {{ $pdfSymbol . getCurrencyFormat($addCharge['amount'] ?? '0.0') }}</td>
                                    </tr>
                                @endforeach
                            @endif
                            <tr>
                                <td class="px-2 text-white bg-primary table-contents-font-size fw-semibold lh-1-5 total-foot text-nowrap"
                                    style="{{ empty($additionalCharges) ? 'border-top: none !important' : '' }}">
                                    Taxable Amount</td>
                                <td class="py-1 text-white bg-primary table-contents-font-size fw-semibold pe-1 text-end lh-1-5 total-foot w-120px"
                                    style="{{ empty($additionalCharges) ? 'border-top: none !important' : '' }}">
                                    {{ $pdfSymbol . getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                </td>
                            </tr>
                            @if ($isCompanyGstApplicable)
                                @if ($transaction->cgst != 0)
                                    <tr>
                                        <td class="px-2 text-black bg-light table-contents-font-size fw-semibold lh-1-5">
                                            {{ $changeLabel['cgst'] ?? 'CGST' }}
                                        </td>
                                        <td class="text-black bg-light table-contents-font-size fw-semibold pe-1 text-end lh-1-5 w-120px">
                                            {{ $pdfSymbol . getCurrencyFormat(round($transaction->cgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endif
                                @if ($transaction->sgst != 0)
                                    <tr>
                                        <td class="px-2 text-black bg-light table-contents-font-size fw-semibold lh-1-5">
                                            {{ $changeLabel['sgst'] ?? 'SGST' }}
                                        </td>
                                        <td class="text-black bg-light table-contents-font-size fw-semibold pe-1 text-end lh-1-5 w-120px">
                                            {{ $pdfSymbol . getCurrencyFormat(round($transaction->sgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endif
                                @if ($transaction->igst != 0)
                                    <tr>
                                        <td class="px-2 text-black bg-light table-contents-font-size fw-semibold lh-1-5">
                                            {{ $changeLabel['igst'] ?? 'IGST' }}
                                        </td>
                                        <td class="text-black bg-light table-contents-font-size fw-semibold pe-1 text-end lh-1-5 w-120px">
                                            {{ $pdfSymbol . getCurrencyFormat(round($transaction->igst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endif
                            @endif
                            @if ($transaction->cess != 0)
                                <tr>
                                    <td class="px-2 text-black bg-light table-contents-font-size fw-semibold lh-1-5">
                                        {{ $changeLabel['cess'] ?? 'Cess' }}
                                    </td>
                                    <td class="text-black bg-light table-contents-font-size fw-semibold pe-1 text-end lh-1-5 w-120px">
                                        {{ $pdfSymbol . getCurrencyFormat($transaction->cess ?? '0.0') }}
                                    </td>
                                </tr>
                            @endif
                            @if ($transaction->tcs_amount != 0)
                                <tr>
                                    <td class="px-2 text-black bg-light table-contents-font-size fw-semibold lh-1-5">
                                        {{ $changeLabel['tcs'] ?? 'TCS' }}
                                    </td>
                                    <td class="text-black bg-light table-contents-font-size fw-semibold pe-1 text-end lh-1-5 w-120px">
                                        {{ $pdfSymbol . getCurrencyFormat($transaction->tcs_amount ?? '0.0') }}
                                    </td>
                                </tr>
                            @endif
                            @if (!empty($addLess))
                                @foreach (collect($addLess)->where('is_show_in_print',1) as $key => $addLessCharge)
                                    <tr>
                                        <td class="px-2 text-black bg-light table-contents-font-size fw-semibold lh-1-5">
                                            {{ $addLessCharge['ledger_name'] ?? '' }}
                                        </td>
                                        <td class="text-black bg-light table-contents-font-size fw-semibold pe-1 text-end lh-1-5 w-120px">
                                            {{ $pdfSymbol . getCurrencyFormat($addLessCharge['amount'] ?? '0.0') }}
                                        </td>
                                    </tr>
                                @endforeach
                            @endif
                            <tr>
                                <td class="px-2 text-black bg-light table-contents-font-size fw-semibold lh-1-5">
                                    {{ $changeLabel['round_off'] ?? 'Round off' }}
                                </td>
                                <td class="text-black bg-light table-contents-font-size fw-semibold pe-1 text-end lh-1-5 w-120px">
                                    {{ $pdfSymbol . getCurrencyFormat($transaction->rounding_amount ?? '0.0') }}
                                </td>
                            </tr>
                            <tr>
                                <td class="px-2 text-white lh-1-5 bg-primary total-font-size total-foot fw-semibold">
                                    {{ $changeLabel['total'] ?? 'Total' }}
                                </td>
                                <td
                                    class="px-2 text-white pe-1 lh-1-5 bg-primary total-font-size total-foot text-end fw-semibold w-120px">
                                    {{ $pdfSymbol . getCurrencyFormat($total ?? '0.0') }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="ms-auto" style="align-content: flex-end;">
                    @if (($showPrintSettings['show_sale_authorized_signatory'] ?? true) || ($invoiceSetting['signature'] ?? true))
                        <div class="mt-5 sign-section ms-auto">
                            <div class="p-2 mb-1 sign d-flex align-items-center ms-auto">
                                <div class="d-flex">
                                    @if (($invoiceSetting['signature'] ?? false) && $currentCompany->company_signature != asset('images/preview-img.png'))
                                        <img src="{{ $currentCompany->company_signature ?? null }}" alt="signature"
                                            class="h-100 w-100" />
                                    @endif
                                </div>
                            </div>
                            @if ($showPrintSettings['show_sale_authorized_signatory'] ?? true)
                                <p class="mx-auto mb-0 text-center text-black footer-contents-font-size fw-semibold ">
                                    {{ $changeLabel['authorized_signatory'] ?? 'Authorized Signatory' }}
                                </p>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
        {{-- Total Section End --}}
    </div>
</body>

</html>
