<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{{ isset($fileName) ? $fileName : $transaction->full_invoice_number }}</title>
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/font.css') }}">
    <style>
        html {
            -webkit-print-color-adjust: exact;
        }

        .main-table {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 100%;
            width: 100%;
            box-sizing: border-box;
        }

        table {
            display: table;
            width: 100%;
            border-collapse: collapse;
        }

        * {
            font-size: 12px;
            font-weight: 400;
            box-sizing: border-box;
        }


        .address {
            font-size: 11px;
            line-height: 14px;
            padding-left: 8px;
            padding-right: 8px;
        }

        .phone {
            font-size: 11px;
            line-height: 14px;
            padding-left: 8px;
            padding-right: 8px;
        }

        td {
            vertical-align: top;
        }

        .fs-13 {
            font-size: 13px;
        }


        .fw-6 {
            font-weight: 600;
        }

        .whitespace-nowrap {
            white-space: nowrap;
        }

        .border-bottom {
            border-bottom: 1px solid black;
        }

        .border-right {
            border-right: 1px solid black;
        }

        .border-top {
            border-top: 1px solid black;
        }

        .border-left {
            border-left: 1px solid black;
        }

        .vertical-top {
            vertical-align: top;
        }

        .vertical-middle {
            vertical-align: middle;
        }

        .vertical-bottom {
            vertical-align: bottom;
        }

        .text-center {
            text-align: center;
        }


        .signature {
            height: 70px;
            width: 175px;
            background-color: white;
        }

        .text-end {
            text-align: right !important;
        }

        .px-0 {
            padding: auto 0 !important;
        }

        .w-50 {
            width: 50%;
        }

        .mb-0 {
            margin-bottom: 0 !important;
        }

        .m-0 {
            margin: 0 !important;
        }

        .border-top-gray {
            border-top: 1px solid #DDE0E4;
        }

        .border-bottom-gray {
            border-bottom: 1px solid #DDE0E4;
        }


        .table-collapsed thead tr td,
        .table-collapsed thead tr th,
        .table-collapsed tfoot tr td {
            font-size: 12px;
            padding: 6px 10px !important;
        }

        .table-collapsed tbody tr td {
            font-size: 12px;
            padding: 2px 10px !important;
        }

        .text-start {
            text-align: left;
        }


        .mb-2 {
            margin-bottom: 6px !important;
        }

        .amount-text {
            padding: 10px 15px !important;
        }

        .p-0 {
            padding: 0 !important;
        }

        .py-1 {
            padding: 2px auto !important;
        }

        .py-2 {
            padding: 4px auto !important;
        }

        .vertical-align-top {
            vertical-align: top !important;
        }

        .vertical-align-bottom {
            vertical-align: bottom !important;
        }

        @page {
            margin: 5px 8px 8px 8px !important;
            box-sizing: border-box;
        }

        .px-30 {
            padding: auto 30px !important;
        }

        .py-3 {
            padding: 15px auto !important;
        }

        .px-3 {
            padding: auto 15px !important;
        }

        .mb-20 {
            margin-bottom: 20px !important;
        }

        .fs-12 {
            font-size: 12px;
        }

        .text-primary {
            color: #4F158C;
        }

        .text-gray {
            color: #3F4254 !important;
        }

        .fs-14 {
            font-size: 14px;
        }

        .fw-bold {
            font-weight: bold;
        }

        body {
            font-family: "Arial-unicode-ms";
            color: #181C32;
            font-weight: 400;
        }
        * {
            margin: 0;
            padding: 0;
            text-indent: 0;
            font-family: "Arial-unicode-ms";
            font-size: 13px;
            font-weight: 400;
            box-sizing: border-box;
        }

        .text-orange {
            color: #F76600 !important;
        }

        .logo {
            height: 40px;
            margin-left: auto;
            padding: 0 30px;
        }

        .logo img {
            height: 100%;
            max-width: 100%;
        }

        .text-center {
            text-align: center;
        }

        .px-2 {
            padding: 0 4px !important;
        }


        .mb-3 {
            margin-bottom: 15px !important;
        }

        p {
            margin: 0;
        }

        .custom-font-family {
            font-family: DejaVu Sans, Poppins, "Helvetica", Arial, "Liberation Sans","Arial-unicode-ms", sans-serif !important;
        }

        .bg-light {
            background-color: #F5F8FA;
        }

        .bg-primary-light {
            background-color: #F6F3F9 !important;
        }

        .bg-primary {
            background-color: #4F158C;
        }

        .text-white {
            color: #ffffff;
        }

        .table-collapsed {
            border-collapse: collapse;
        }

        /* .authorized-signature {
            width: 170px;
        } */

        .ms-auto {
            margin-left: auto;
        }

        .br-5 {
            border-radius: 5px;
        }

        .overflow-hidden {
            overflow: hidden;
        }

        .p-10 {
            padding: 10px !important;
        }


        .w-100 {
            width: 100%;
        }

        .pb-1 {
            padding-bottom: 2px !important;
        }

        .mb-1 {
            margin-bottom: 4px !important;
        }

        .pt-0 {
            padding-top: 0 !important;
        }
    </style>
</head>@php
    $isCompanyGstApplicable = !$transaction->is_gst_na;
    $pdfSymbol = getPrintPdfCurrencySymbol($currentCompany->id);
@endphp

<body>
    <div class="main-table">
        <div style="margin:0 20px;">
            <table class="w-100 p-0">
                @if($isCompanyGstApplicable)
                <tr>
                    <td class="p-0">{{ $changeLabel['gstin'] ?? 'GSTIN' }}: {{ $currentCompany->companyTax->gstin ?? null }}</td>
                    <td class="text-end p-0">Original Copy</td>
                </tr>
                @endif
            </table>
            <table cellpadding="0">
                <tr>
                    <td class="vertical-middle" style="">
                        @if (!empty($eInvoice))
                        <div>
                            <div>
                                <span class="">Ack No</span>
                                <span class="">-</span>
                                <span class="">{{ !empty($eInvoice) ? $eInvoice->ack_no : "" }}</span>
                            </div>
                            <div>
                                <span class="">Ack Date</span>
                                <span class="">-</span>
                                <span class="">{{ !empty($eInvoice) ? \Carbon\Carbon::parse($eInvoice->ack_date)->format('d-m-Y') : "" }}</span>
                            </div>
                            <div>
                                <span class="">IRN</span>
                                <span class="">-</span>
                                <span class="">{{ !empty($eInvoice) ? $eInvoice->irn : "" }}</span>
                            </div>
                        </div>
                        @endif
                    </td>
                    <td class="vertical-middle text-center" style="padding-left: 20px">
                        @if (isset($currentCompany->company_logo) && ($invoiceSetting['logo'] ?? true) &&  $currentCompany->company_logo != asset('assets/images/company-logo.png'))
                            <img src={{ $currentCompany->company_logo ?? '' }} alt="Logo" style="object-fit: contain;" width="100" height="100">
                        @endif
                        <h1>{{ strtoupper($currentCompany->trade_name) }}</h1>
                        <p>
                            {{ strtoupper($companyBillingAddress->address_1 ?? null) }},
                            {{ strtoupper($companyBillingAddress->address_2 ?? null) }},
                            {{ strtoupper(getCityName($companyBillingAddress->city_id ?? null)) }},
                            {{ strtoupper(getStateName($companyBillingAddress->state_id ?? null)) }},
                            {{ strtoupper(getCountryName($companyBillingAddress->country_id ?? null)) }},
                            {{ $companyBillingAddress->pin_code ?? null }}
                        </p>

                        <p class="mb-2">
                            @if ($invoiceSetting['mobile_number'] ?? true)
                                {{ $changeLabel['tel'] ?? 'Tel' }} :
                                {{
                                    (isset($invoiceSetting['region_code']) ? '+' . $invoiceSetting['region_code'] . ' ' : '') .
                                    (isset($invoiceSetting['alternate_phone']) ? $invoiceSetting['alternate_phone'] : ('+' . $currentCompany->user->region_code . ' ' . $currentCompany->user->phone)) .
                                    (isset($invoiceSetting['region_code_2']) && isset($invoiceSetting['alternate_phone_2']) ? ', +' . $invoiceSetting['region_code_2'] . ' ' : '') .
                                    (isset($invoiceSetting['alternate_phone_2']) ? (isset($invoiceSetting['region_code_2']) ? '' : ', ') . $invoiceSetting['alternate_phone_2'] : '')
                                }}
                            @endif
                            @if (($invoiceSetting['mobile_number'] ?? true) && ($invoiceSetting['email'] ?? true))
                                |
                            @endif

                            @if ($invoiceSetting['email'] ??  true)
                                {{ $invoiceSetting['alternate_email'] ?? ($currentCompany->user->email ?? null) }}
                            @endif
                        </p>
                        @foreach (printCustomPDFLabelsForSale() as $key => $customLabel)
                                <p>{{ $key ?? null }}: {{ $customLabel ?? null }}</p>
                        @endforeach
                    </td>
                    <td class="vertical-middle text-end" style=" width: 100px;height: 100px" >
                        @if (!empty($eInvoice))
                        @php
                            $qrCode = (string) QrCode::format('svg')
                                ->margin(0)
                                ->size(200)
                                ->generate($eInvoice->signed_qr_code);
                        @endphp
                        <img src="data:image/svg+xml;base64,{{ base64_encode($qrCode) }}" width="100"
                            height="100" />
                    @endif
                    </td>
                </tr>
            </table>
            {{-- <div style="border-bottom:6px solid #F5F8FA; margin-bottom:14px;">

                <div class="text-center w-50" style=" margin-left:auto; margin-right:auto; margin-bottom:10px;">
                    @if (getCustomInvoiceSetting('logo'))
                        <div class="logo text-center">
                            <img src={{ $currentCompany->invoice_pdf_company_logo ?? '' }} alt="Logo">
                        </div>
                    @endif
                    <h2 class="mb-1 "style="font-size:16px; text-transform:uppercase; font-weight:600;">
                        {{ $currentCompany->trade_name }}</h2>
                    <p class="text-gray p-0 pb-1" style="font-size:14px;">
                        {{ $companyBillingAddress->address_1 ?? null }},<br>
                        {{ $companyBillingAddress->address_2 ?? null }},<br>
                        {{ getCityName($companyBillingAddress->city_id ?? null) }},
                        {{ getStateName($companyBillingAddress->state_id ?? null) }},
                        {{ getCountryName($companyBillingAddress->country_id ?? null) }},
                        {{ $companyBillingAddress->pin_code ?? null }}
                    </p>
                    @if (getCustomInvoiceSetting('mobile_number'))
                        <span class="text-gray fs-13">Tel:
                            {{ '  ' . (isset(getCompanySettings()['alternate_phone']) ? getCompanySettings()['alternate_phone'] : $currentCompany->phone) ?? null }}</span>
                    @endif
                    @if (getCustomInvoiceSetting('mobile_number') && getCustomInvoiceSetting('email'))
                        <span class="text-orange px-2  fs-13">|</span>
                    @endif
                    @if (getCustomInvoiceSetting('email'))
                        <span
                            class="text-gray fs-13">{{ (isset(getCompanySettings()['alternate_email']) ? getCompanySettings()['alternate_email'] : $currentCompany->user->email) ?? null }}</span>
                    @endif
                </div>
            </div> --}}
            <table class="mb-3 w-100">
                <tr>
                    <td width="49.5%" class="vertical-align-top bg-primary-light br-5 overflow-hidden"
                        style="padding:12px;">
                        <div>
                            <table class="mb-0 w-100">
                                <tr class="p-0">
                                    <td class="p-0">
                                        <p class="text-primary fw-6 m-0">{{ $changeLabel['bill_to'] ?? 'Bill to' }}:
                                        </p>
                                        <h2 class="fs-14 text-gray m-0 fw-bold">{{ $customerDetail->name }}</h2>
                                        @if (isset($billingAddress))
                                            <p class="fs-12 mb-0 pb-1">
                                                @if ($billingAddress->address_1 != null)
                                                    {{ strtoupper($billingAddress->address_1) }},<br>
                                                @endif
                                                @if ($billingAddress->address_2 != null)
                                                    {{ strtoupper($billingAddress->address_2) }},<br>
                                                @endif
                                                {{ $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                                                {{ $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                                                {{ strtoupper(getCountryName($billingAddress->country_id ?? null)) }},
                                                {{ $billingAddress->pin_code ?? null }}
                                            </p>
                                        @endif
                                        <p>
                                            @if ($customerDetail->model->phone_1 != null || $customerDetail->model->phone_2 != null)
                                                @if ($customerDetail->model->phone_1 != null)
                                                    <span
                                                        class="fs-12 px-0">+{{ $customerDetail->model->region_code_1 ?? '' }} {{ $customerDetail->model->phone_1 ?? null }}</span>
                                                @endif
                                                @if ($customerDetail->model->phone_1 != null && $customerDetail->model->phone_2 != null)
                                                    <span class="px-0 fs-12">|</span>
                                                @endif
                                                @if ($customerDetail->model->phone_2 != null)
                                                    <span
                                                        class="df-12 px-0">+{{ $customerDetail->model->region_code_2 ?? '' }} {{ $customerDetail->model->phone_2 }}</span>
                                                @endif
                                            @endif
                                        </p>
                                        @if (!empty($customerDetail->model->person_email))
                                            <p class="fs-12 fw-bold">
                                                Email: <span>{{ $customerDetail->model->person_email ?? null }}</span>
                                            </p>
                                        @endif
                                        @if ($isCompanyGstApplicable)
                                            <p class="fs-12 fw-bold">GSTIN /
                                                UIN: <span>{{ $transaction->gstin ?? null }}</span>
                                            </p>
                                        @endif
                                        @if (! empty($panNumber) && $showPanNumber)
                                            <p class="fs-12 fw-bold">
                                                PAN: <span>{{ $panNumber ?? null }}</span>
                                            </p>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </td>
                    <td width="1%">
                    </td>
                    <td width="49.5%" class="bg-primary-light br-5 overflow-hidden" style="padding:12px;">
                        <div>
                            <table class="text-end mb-0 w-100">
                                <tbody>
                                    <tr>
                                        <th class="text-start pb-1 fw-bold pt-0">{{ $invoiceDetailLabel }}:</th>
                                        <td class="text-end pb-1 pt-0">{{ $transaction->full_invoice_number }}</td>
                                    </tr>
                                    <tr>
                                        <th class="text-start pb-1 fw-bold pt-0">{{ $invoiceNumberLabel }}:</th>
                                        <td class="text-end pb-1 pt-0">{{ $invoiceDate }}</td>
                                    </tr>
                                    <tr>
                                        <th class="text-start pb-1 fw-bold pt-0">Doc Type:</th>
                                        <td class="text-end pb-1 pt-0">{{ $taxInvoice }}</td>
                                    </tr>
                                    @if(getCustomInvoiceSetting('po_number'))
                                    <tr>
                                        <th class="text-start pb-1 fw-bold pt-0">{{ $changeLabel['po_number_label'] ?? 'PO NO' }} :
                                        </th>
                                        <td class="text-end pb-1 pt-0"> {{ $transaction->po_no }}
                                        </td>
                                    </tr>
                                    @endif
                                    @if(getCustomInvoiceSetting('show_po_date'))
                                    <tr>
                                        <th class="text-start pb-1 fw-bold pt-0">{{ $changeLabel['po_date'] ?? 'PO Date' }}:
                                        </th>
                                        <td class="text-end pb-1 pt-0">  {{ isset($transaction->po_date) ? \Carbon\Carbon::parse($transaction->po_date)->format('d-m-Y') : null }}
                                        </td>
                                    </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tr>
            </table>

            {{-- Custom Fields Section Start --}}
            @if (count($customFieldValues) > 0)
                @php
                    $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
                @endphp
                <table cellpadding="0" class="bg-primary-light mb-3">
                    @foreach ($customFields->chunk(3) as $chunk)
                        <tr class="">
                            @foreach ($chunk as $customField)
                                <td class="fs-13" style="padding: 6px 8px; width:150px;">
                                    <span style="font-weight: bold;">{{ $customField['label_name'] ?? '' }}</span> : {{ $customField['value'] ?? '' }}
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </table>
            @endif
            {{-- Custom Fields Section End --}}

            <table class="table-collapsed mb-2 w-100">
                @if ($itemType == \App\Models\SaleTransaction::ITEM_INVOICE)
                    @php
                        $customFieldItemsValues = collect($transactionItems[0]['customItemsValues'])->where('is_show_in_print', true)->values();
                        $customFieldItemsHeaders = collect($customFieldItemsValues)->pluck('label_name')->toArray();
                        $cfNumberTypeFieldTotals = [];
                    @endphp
                    <thead class="border-top-gray border-bottom-gray text-white  bg-primary">
                        <tr>
                            <th scope="col" class="text-center fw-bold">{{ $changeLabel['sn'] ?? 'S.N' }}</th>
                            <th scope="col" class="text-center fw-bold">{{ $changeLabel['item_name'] ?? 'Item Name - HSN Code' }}</th>
                            @if (count($customFieldItemsHeaders) > 0)
                                @foreach ($customFieldItemsHeaders as $customFieldItemHeader)
                                    <th scope="col" class="text-center fw-bold">{{ $customFieldItemHeader ?? '' }}</th>
                                @endforeach
                            @endif
                            <th scope="col" class="text-center fw-bold">{{ $changeLabel['qty'] ?? 'Qty' }}</th>
                            @if ($transactionItems->sum('mrp') != 0.0)
                                <th scope="col" class="text-center fw-bold">{{ $changeLabel['mrp'] ?? 'MRP' }}</th>
                            @endif
                            @if ($isCompanyGstApplicable)
                            <th scope="col" class="text-center fw-bold">{{ $changeLabel['rate_with_gst'] ?? 'Rate With GST' }}</th>
                            @endif
                            <th scope="col" class="text-center fw-bold">{{ $changeLabel['rate'] ?? 'Rate' }}</th>
                            @if ($transactionItems->sum('total_discount_amount') != 0.0)
                                <th scope="col" class="text-center fw-bold">{{ $changeLabel['discount'] ?? 'Dis.' }}</th>
                                <th scope="col" class="text-nowrap text-center fw-bold">{{ $changeLabel['total_discount'] ?? 'Total Dis.' }}</th>
                            @endif
                            @if ($isCompanyGstApplicable)
                                <th scope="col" class="text-center fw-bold">{{ $changeLabel['taxable_value'] ?? 'Taxable Value' }}</th>
                                <th scope="col" class="text-center fw-bold">{{ $changeLabel['gst'] ?? 'GST (%)' }}</th>
                                <th scope="col" class="text-center fw-bold">{{ ($changeLabel['cgst'] ?? 'CGST' ).'/'.($changeLabel['sgst'] ?? 'SGST') }}</th>
                                <th scope="col" class="text-center fw-bold"> {{ $changeLabel['igst'] ?? 'IGST' }}</th>
                            @endif
                            <th scope="col" class="text-center fw-bold">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($transactionItems as $key => $item)
                            @php
                                $uniqueId = ++$key;
                                $ids = collect($customFieldItemsValues)->pluck('custom_field_item_id')->values();
                                $printCustomFields = collect($item['customItemsValues'])->where('is_show_in_print', true)->whereIn('custom_field_item_id', $ids)->values();
                                foreach ($item['customItemsValues'] as $customField) {
                                    $cfId = $customField['custom_field_id'];
                                    if ($customField['show_total'] && $customField['custom_field_type'] == \App\Models\ItemCustomField::CF_TYPE_NUMBER) {
                                        $value = (float) $customField['value'] ?? 0;
                                        if (! isset($cfNumberTypeFieldTotals[$cfId])) {
                                            $cfNumberTypeFieldTotals[$cfId] = 0;
                                        }
                                        $cfNumberTypeFieldTotals[$cfId] += $value;
                                    } else {
                                        $cfNumberTypeFieldTotals[$cfId] = null;
                                    }
                                }
                            @endphp
                            <tr>
                                <td scope="row" class="">{{ $uniqueId }}</td>
                                <td class="">
                                    {{ $item->items->item_name ?? null }}
                                    - {{ $item->hsn_code ?? $item->items->model->hsn_sac_code ?? null }}
                                    <p>
                                        {!! !empty($item->consolidating_items_to_invoice) ? '(' . consolidatingItemsToInvoice($item->consolidating_items_to_invoice) . ')' : null !!}
                                    </p>
                                    <p>
                                        {!! !empty($item->additional_description) ? nl2br('(' . $item->additional_description . ')') : null !!}
                                    </p>
                                </td>
                                @if (count($printCustomFields) > 0)
                                    @foreach ($printCustomFields as $customFieldItemsValue)
                                        <td class="text-center">
                                            {{ $customFieldItemsValue['value'] ?? '' }}
                                        </td>
                                    @endforeach
                                @endif
                                <td class="text-center">
                                    @if (!empty($item->consolidating_items_to_invoice))
                                        {{ getConsolidatingItemsQty($item) }}
                                    @else
                                        {{ $item->quantity }} {{ $item->unit->code }}
                                    @endif
                                </td>
                                @if ($transactionItems->sum('mrp') != 0.0)
                                    <td class="py-0 text-center">{{ $item->mrp ?? '-' }} </td>
                                @endif
                                @if ($isCompanyGstApplicable)
                                <td class="custom-font-family text-center">
                                    {{ $pdfSymbol.getCurrencyFormatFor3digit($item->rpu_with_gst) }}
                                </td>
                                @endif
                                <td class="custom-font-family text-center">
                                    {{ $pdfSymbol.getCurrencyFormatFor3digit($item->rpu_without_gst) }}</td>
                                @if ($transactionItems->sum('total_discount_amount') != 0.0)
                                    @if ($item->discount_type == \App\Models\SaleTransaction::DISCOUNT_TYPE_AMOUNT)
                                        <td class="py-0 custom-font-family text-center">
                                            {{ $pdfSymbol.getCurrencyFormat(round($item->discount_value ?? '0.0', getCompanyFixedDigitNumber())) }}
                                        </td>
                                    @else
                                        <td class="py-0">{{ $item->discount_value . '(%)' ?? '0.0(%)' }} </td>
                                    @endIf
                                    <td class="py-0 custom-font-family text-center">
                                        {{ $pdfSymbol.getCurrencyFormat(round($item->total_discount_amount ?? '0.0', getCompanyFixedDigitNumber())) }}
                                    </td>
                                @endif
                                @if ($isCompanyGstApplicable)
                                    <td class="custom-font-family text-center">
                                        {{ $pdfSymbol.getCurrencyFormat(round($item->taxable_value ?? '0.0', getCompanyFixedDigitNumber())) }}
                                    </td>
                                    <td class="custom-font-family text-center">{{ $item->gst_tax_percentage ?? '0.0' }}
                                    </td>
                                    <td class="custom-font-family text-center">
                                        <p class="mb-0">
                                            {{ $pdfSymbol.getCurrencyFormat(round($item->classification_cgst_tax ?? '0.0', getCompanyFixedDigitNumber())) }}/
                                        </p>
                                        <p class="mb-0">
                                            {{ $pdfSymbol.getCurrencyFormat(round($item->classification_sgst_tax ?? '0.0', getCompanyFixedDigitNumber())) }}
                                        </p>
                                    </td>
                                    <td class="custom-font-family text-center">
                                        {{ $pdfSymbol.getCurrencyFormat(round($item->classification_igst_tax ?? '0.0', getCompanyFixedDigitNumber())) }}
                                    </td>
                                @endif
                                <td class="custom-font-family text-center">
                                    @php
                                        $itemTotal =
                                            round($item->total, getCompanyFixedDigitNumber()) +
                                            round(
                                                $item->classification_cgst_tax ?? '0.0',
                                                getCompanyFixedDigitNumber(),
                                            ) +
                                            round(
                                                $item->classification_sgst_tax ?? '0.0',
                                                getCompanyFixedDigitNumber(),
                                            ) +
                                            round(
                                                $item->classification_igst_tax ?? '0.0',
                                                getCompanyFixedDigitNumber(),
                                            );
                                    @endphp
                                    {{ $pdfSymbol.getCurrencyFormat($itemTotal) }}
                                </td>
                            </tr>
                        @endforeach
                        @php
                            $shipping = $transaction->prepareShippingAmount();
                            $packing = $transaction->preparePackingAmount();
                            $shippingCharge = collect($shipping);
                            $packingCharge = collect($packing);
                        @endphp
                        @if (
                            $transaction->shipping_freight_with_gst != 0.0 ||
                                $transaction->packing_charge_with_gst != 0.0 ||
                                $transaction->shipping_freight != 0.0 ||
                                $transaction->packing_charge != 0.0)
                            @if ($transaction->shipping_freight_with_gst != 0.0 || $transaction->shipping_freight != 0.0)
                                <tr>
                                    <th scope="row" class="">{{ ++$uniqueId }}</th>
                                    <td class="text-center">Shipping / Freight</td>
                                    <td class="text-center">-</td>
                                    <td class="custom-font-family text-center">-</td>
                                    @if ($transactionItems->sum('mrp') != 0.0)
                                        <td class="py-0 custom-font-family text-center">-</td>
                                    @endif
                                    @if ($isCompanyGstApplicable)
                                    <td class="py-0 text-center custom-font-family">-</td>
                                    @endif
                                    @if ($transactionItems->sum('total_discount_amount') != 0.0)
                                        @if ($item->discount_type == \App\Models\SaleTransaction::DISCOUNT_TYPE_AMOUNT)
                                            <td class="py-0 text-center custom-font-family fs-10">
                                                -
                                            </td>
                                        @else
                                            <td class="py-0 text-center">- </td>
                                        @endIf
                                        <td class="py-0 text-center custom-font-family ">
                                            -
                                        </td>
                                    @endif
                                    @if ($isCompanyGstApplicable)
                                        <td class="custom-font-family text-center">
                                            {{ $pdfSymbol.getCurrencyFormat(round(abs($shippingCharge->sum('taxable_value' ?? '0.0')))) }}
                                        </td>
                                        <td class="custom-font-family text-center">-</td>
                                        <td class="custom-font-family text-center">
                                            <p class="mb-0">
                                                {{ $pdfSymbol.getCurrencyFormat(round(abs($shippingCharge->sum('cgst' ?? '0.0')), getCompanyFixedDigitNumber())) }}
                                                /</p>
                                            <p class="mb-0">
                                                {{ $pdfSymbol.getCurrencyFormat(round(abs($shippingCharge->sum('sgst' ?? '0.0')), getCompanyFixedDigitNumber())) }}
                                            </p>
                                        </td>

                                        <td class="custom-font-family text-center">
                                            {{ $pdfSymbol.getCurrencyFormat(round(abs($shippingCharge->sum('igst' ?? '0.0')), getCompanyFixedDigitNumber())) }}
                                        </td>
                                    @endif
                                    @php
                                        $shippingChargeTotal =
                                            abs($shippingCharge->sum('taxable_value')) +
                                            round(
                                                abs($shippingCharge->sum('cgst' ?? '0.0')),
                                                getCompanyFixedDigitNumber(),
                                            ) +
                                            round(
                                                abs($shippingCharge->sum('sgst' ?? '0.0')),
                                                getCompanyFixedDigitNumber(),
                                            ) +
                                            round(
                                                abs($shippingCharge->sum('igst' ?? '0.0')),
                                                getCompanyFixedDigitNumber(),
                                            );

                                    @endphp
                                    <td class="custom-font-family text-center">
                                        {{ $pdfSymbol.getCurrencyFormat($shippingChargeTotal, getCompanyFixedDigitNumber()) }}
                                    </td>
                                </tr>
                            @endif
                            @if ($transaction->packing_charge_with_gst != 0.0 || $transaction->packing_charge != 0.0)
                                <tr>
                                    <th scope="row" class="">{{ ++$uniqueId }}</th>
                                    <td class="text-center">Packing Charge</td>
                                    <td class="text-center">-</td>
                                    <td class="custom-font-family text-center">-</td>
                                    @if ($transactionItems->sum('mrp') != 0.0)
                                        <td class="py-0 custom-font-family text-center">-</td>
                                    @endif
                                    @if ($isCompanyGstApplicable)
                                    <td class="py-0 text-center custom-font-family">-</td>
                                    @endif
                                    @if ($transactionItems->sum('total_discount_amount') != 0.0)
                                        @if ($item->discount_type == \App\Models\SaleTransaction::DISCOUNT_TYPE_AMOUNT)
                                            <td class="py-0 text-center custom-font-family fs-10">
                                                -
                                            </td>
                                        @else
                                            <td class="py-0 text-center">- </td>
                                        @endIf
                                        <td class="py-0 text-center custom-font-family ">
                                            -
                                        </td>
                                    @endif
                                    @if ($isCompanyGstApplicable)
                                        <td class="custom-font-family text-center">
                                            {{ $pdfSymbol.getCurrencyFormat(round(abs($packingCharge->sum('taxable_value' ?? '0.0')), getCompanyFixedDigitNumber())) }}
                                        </td>
                                        <td class="custom-font-family text-center">-</td>
                                        <td class="custom-font-family text-center">
                                            <p class="mb-0">
                                                {{ $pdfSymbol.getCurrencyFormat(round(abs($packingCharge->sum('cgst' ?? '0.0')), getCompanyFixedDigitNumber())) }}
                                                /</p>
                                            <p class="mb-0">
                                                {{ $pdfSymbol.getCurrencyFormat(round(abs($packingCharge->sum('sgst' ?? '0.0')), getCompanyFixedDigitNumber())) }}
                                            </p>
                                        </td>
                                        <td class="custom-font-family text-center">
                                            {{ $pdfSymbol.getCurrencyFormat(round(abs($packingCharge->sum('igst' ?? '0.0')), getCompanyFixedDigitNumber())) }}
                                        </td>
                                    @endif
                                    @php
                                        $packingChargeTotal =
                                            round(
                                                abs($packingCharge->sum('taxable_value')),
                                                getCompanyFixedDigitNumber(),
                                            ) +
                                            round(abs($packingCharge->sum('cgst')), getCompanyFixedDigitNumber()) +
                                            round(abs($packingCharge->sum('sgst')), getCompanyFixedDigitNumber()) +
                                            round(abs($packingCharge->sum('igst')), getCompanyFixedDigitNumber());
                                    @endphp
                                    <td class="custom-font-family text-center">
                                        {{ $pdfSymbol.getCurrencyFormat(round($packingChargeTotal, getCompanyFixedDigitNumber())) }}
                                    </td>
                                </tr>
                            @endif
                        @endif
                    </tbody>
                    <tfoot class="border-top-gray border-bottom-gray">
                        <td scope="row"></td>
                        <td class="text-center fw-bold">Total</td>
                        @if (count($customFieldItemsValues) > 0)
                            @foreach ($customFieldItemsValues as $customFieldItemsValue)
                                <td class="py-0 text-center custom-font-family">
                                    {{ $cfNumberTypeFieldTotals[$customFieldItemsValue['custom_field_id']] ?? '' }}
                                </td>
                            @endforeach
                        @endif
                        <td class="text-center fw-bold">{{ getCurrencyFormat($transactionItems->sum('quantity')) }}
                        </td>
                        <td class="custom-font-family fw-bold text-center"></td>
                        @if ($transactionItems->sum('mrp') != 0.0)
                            <td class="py-0"></td>
                        @endif
                        @if ($isCompanyGstApplicable)
                            <td class="py-0 text-center custom-font-family"></td>
                        @endif
                        @if ($transactionItems->sum('total_discount_amount') != 0.0)
                            <td></td>
                            <td class="text-center custom-font-family fs-10">
                                {{ $pdfSymbol . getCurrencyFormat($transactionItems->sum('total_discount_amount' ?? 0.0)) }}
                            </td>
                        @endif
                        @if ($isCompanyGstApplicable)
                            @php
                                $taxableValue =
                                    round($transactionItems->sum('taxable_value'), getCompanyFixedDigitNumber()) +
                                    round(
                                        abs($shippingCharge->sum('taxable_value' ?? '0.0')),
                                        getCompanyFixedDigitNumber(),
                                    ) +
                                    abs($packingCharge->sum('taxable_value' ?? '0.0'));
                            @endphp
                            <td class="custom-font-family fw-bold text-center">{{ $pdfSymbol.getCurrencyFormat($taxableValue) }}
                            </td>
                            <td class="custom-font-family fw-bold text-center"></td>
                            <td class="custom-font-family fw-bold text-center">
                                <p class="mb-0"></p>
                                <p class="mb-0"></p>
                            </td>
                            <td class="custom-font-family fw-bold text-center"></td>
                        @endif
                        @php
                            $grandTotal =
                                $transactionItems->sum('total') +
                                ($transactionItems->sum('classification_cgst_tax') ?? 0) +
                                ($transactionItems->sum('classification_sgst_tax') ?? 0) +
                                ($transactionItems->sum('classification_igst_tax') ?? 0) +
                                round(abs($shippingChargeTotal ?? '0.0'), getCompanyFixedDigitNumber()) +
                                round(abs($packingChargeTotal ?? '0.0'), getCompanyFixedDigitNumber());
                        @endphp

                        <td class="custom-font-family fw-bold text-center">
                            {{ $pdfSymbol . getCurrencyFormat($grandTotal) }}</td>
                        </tr>
                    </tfoot>
                @endif
            </table>
            <table class="mb-2 pb-1 w-100">
                <tr>
                    <td class="vertical-align-top p-0" style="width:49%;padding-top:2px !important;">
                        <table class="mb-2 w-100">
                            <tbody>
                                <tr>
                                    <td class="text-start pt-0 pb-1 px-0">Terms Of Payment</td>
                                    <td class="custom-font-family text-end pt-0 pb-1 px-0">{{ $creditPeriod }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-start pt-0 pb-1 px-0 ">Due Date</td>
                                    <td class="custom-font-family text-end pt-0 pb-1 px-0 ">
                                        {{ $dueDate }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="bg-primary-light amount-text">
                            <p class="fs-12 mb-2 text-gray">{{ $changeLabel['in_words'] ?? 'In Words' }}</p>
                            <p class="fs-12 fw-bold"> {{ getAmountToWord($transaction->grand_total ?? '0.0') }}
                            </p>
                        </div>
                    </td>
                    <td style="width:2%"></td>
                    <td style="width:49%">
                        <table class="mb-2 w-100">
                            <tbody>
                                <tr>
                                    <td scope="row" class="text-start py-2">
                                        {{ $isCompanyGstApplicable ? $changeLabel['sub_total'] ?? 'Taxable Value' : $changeLabel['sub_total'] ?? 'Sub Total' }}:
                                    </td>
                                    <td class="custom-font-family text-end py-2">
                                        {{ $pdfSymbol.getCurrencyFormat($grandTotal ?? '0.0') }}</td>
                                </tr>
                                @if ($transaction->tcs_amount != 0)
                                    <tr>
                                        <td scope="row" class="text-start py-2">{{ $changeLabel['tcs'] ?? 'TCS' }}:</td>
                                        <td class="custom-font-family text-end py-2">
                                            {{ $pdfSymbol.getCurrencyFormat($transaction->tcs_amount ?? '0.0') }}</td>
                                    </tr>
                                @endif
                                @if ($transaction->cess != 0)
                                    <tr>
                                        <td scope="row" class="text-start py-2">Cess</td>
                                        <td class="custom-font-family text-end py-2">
                                            {{ $pdfSymbol.getCurrencyFormat($transaction->cess ?? '0.0') }}</td>
                                    </tr>
                                @endif
                                <tr>
                                    <td scope="row" class="text-start py-2">{{ $changeLabel['round_off'] ?? 'Round off' }}:</td>
                                    <td class="custom-font-family text-end py-2">
                                        {{ $pdfSymbol.getCurrencyFormat($transaction->rounding_amount ?? '0.0') }}</td>
                                </tr>
                                <tr class=" bg-primary-light text-primary">
                                    <td scope="row" class="text-start text-primary fw-bold fs-13 px-0"
                                        style="padding:4px 0;">
                                        {{ $changeLabel['total'] ?? 'Total' }}:</td>
                                    <td class="custom-font-family text-end text-primary fs-13 fw-bold px-0"
                                        style="padding:5px 0;">
                                        {{ $pdfSymbol.getCurrencyFormat($transaction->grand_total ?? '0.0') }}</td>
                                </tr>
                                <tr>
                                    <td scope="row" class="text-start py-2 px-0">Paid Amt.</td>
                                    <td class="custom-font-family text-end py-2 px-0">(-)
                                        {{ $pdfSymbol.getCurrencyFormat($transaction->grand_total - $transaction->due_amount ?? '0.0') }}
                                    </td>
                                </tr>
                                <tr>
                                    <td scope="row" class="text-start py-2 ">Due Amt.</td>
                                    <td class="custom-font-family text-end py-2 ">
                                        {{ $pdfSymbol.getCurrencyFormat($transaction->due_amount ?? '0.0') }}</td>
                                </tr>
                            </tbody>

                        </table>
                    </td>
                </tr>
            </table>
            @if ($isCompanyGstApplicable && !empty($checkHsnCodeExist))
                <table cellpadding="0" style="flex-grow: 1" class="item-table table-collapsed mb-20">
                    <thead class="border-top-gray border-bottom-gray text-white  bg-primary">
                        <tr>
                            <th scope="col" class="text-center fw-bold">S.N</th>
                            <th scope="col" class="text-center fw-bold">HSN/SAC</th>
                            <th scope="col" class="text-center fw-bold">Taxable Amount</th>
                            <th scope="col" class="text-center fw-bold">GST(%)</th>
                            @if ($cgst != 0.0)
                                <th scope="col" class="text-center fw-bold">CGST</th>
                            @endif
                            @if ($sgst != 0.0)
                                <th scope="col" class="text-center fw-bold">SGST</th>
                            @endif
                            @if ($igst != 0.0)
                                <th scope="col" class="text-center fw-bold">IGST</th>
                            @endif
                            <th scope="col" class="text-center fw-bold">Total Tax</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            $uniquekey = 1;
                        @endphp
                        @foreach ($checkHsnCodeExist as $key => $item)
                            @foreach ($item as $hsnCode => $data)
                                <tr>
                                    <td scope="col" class="text-center fw-bold"> {{ $uniquekey++ }}</td>
                                    <td scope="col" class="text-center fw-bold">
                                        {{ !empty($hsnCode) ? $hsnCode : '-' }}</td>
                                    <td scope="col" class="text-center">
                                        {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['taxableValue'][$hsnCode], getCompanyFixedDigitNumber()) ?? 0) }}
                                    </td>
                                    <td scope="col" class="text-center">{{ !empty($key) ? $key : '-' }}</td>
                                    @if ($cgst != 0.0)
                                        <td scope="col" class="text-center">
                                            {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['cgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                        </td>
                                    @endif
                                    @if ($sgst != 0.0)
                                        <td scope="col" class="text-center">
                                            {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['sgst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                        </td>
                                    @endif
                                    @if ($igst != 0.0)
                                        <td scope="col" class="text-center">
                                            {{ $pdfSymbol.getCurrencyFormat(round($checkTAXtExist[$key]['igst'][$hsnCode] ?? 0, getCompanyFixedDigitNumber())) }}
                                        </td>
                                    @endif
                                    @php
                                        $totalTax =
                                            round(
                                                $checkTAXtExist[$key]['cgst'][$hsnCode],
                                                getCompanyFixedDigitNumber(),
                                            ) +
                                            round(
                                                $checkTAXtExist[$key]['sgst'][$hsnCode],
                                                getCompanyFixedDigitNumber(),
                                            ) +
                                            round(
                                                $checkTAXtExist[$key]['igst'][$hsnCode],
                                                getCompanyFixedDigitNumber(),
                                            );
                                    @endphp
                                    <td scope="col" class="text-center">
                                        {{ $pdfSymbol.getCurrencyFormat(round($totalTax ?? 0, getCompanyFixedDigitNumber())) }}
                                    </td>
                                </tr>
                            @endforeach
                        @endforeach

                    </tbody>
                    <tfoot class="border-top-gray border-bottom-gray">
                        <tr>
                            @php
                                $taxableValue = 0;
                                $cgst = 0;
                                $sgst = 0;
                                $igst = 0;
                                foreach ($checkTAXtExist as $key => $item) {
                                    if ($key != 0) {
                                        $taxableValue += array_sum($item['taxableValue']);
                                        $cgst += array_sum($item['cgst']);
                                        $sgst += array_sum($item['sgst']);
                                        $igst += array_sum($item['igst']);
                                    }
                                }
                            @endphp
                            <td scope="col" class="text-center fw-bold" style="padding:3px 10px;"></td>
                            <td scope="col" class="text-center fw-bold" style="padding:3px 10px;">Total</td>

                            <td scope="col" class="text-center fw-bold" style="padding:3px 10px;">
                                {{ $pdfSymbol.getCurrencyFormat(round($taxableValue, getCompanyFixedDigitNumber())) }}</td>
                            <td scope="col" class="text-center fw-bold" style="padding:3px 10px;"></td>
                            @if ($cgst != 0.0)
                                <td scope="col" class="text-center fw-bold" style="padding:3px 10px;">
                                    {{ $pdfSymbol.getCurrencyFormat(round($cgst, getCompanyFixedDigitNumber())) }}</td>
                            @endif
                            @if ($sgst != 0.0)
                                <td scope="col" class="text-center fw-bold" style="padding:3px 10px;">
                                    {{ $pdfSymbol.getCurrencyFormat(round($sgst, getCompanyFixedDigitNumber())) }}</td>
                            @endif
                            @if ($igst != 0.0)
                                <td scope="col" class="text-center fw-bold" style="padding:3px 10px;">
                                    {{ $pdfSymbol.getCurrencyFormat(round($igst, getCompanyFixedDigitNumber())) }}</td>
                            @endif
                            @php
                                $grandTotalTax = $cgst + $sgst + $igst;
                            @endphp
                            <td scope="col" class="text-center fw-bold" style="padding:3px 10px;">
                                {{ $pdfSymbol.getCurrencyFormat(round($grandTotalTax, getCompanyFixedDigitNumber())) }}</td>
                        </tr>
                    </tfoot>
                </table>

            @endif
        </div>
        <div class="bg-primary-light" style="margin:0 20px; padding:8px 20px 8px 20px;">
            <div style="">
                <h2 class="fs-13 fw-bold mb-1">Bank & Payment Details :</h2>
                <table class="w-50 mb-3">
                    <tr>
                        <td class="w-50 p-0 ">
                            <table class=" w-100 mb-0">
                                <tr>
                                    <td class="text-start p-0 pb-1 text-gray whitespace-nowrap ">Bank
                                        Name:
                                    </td>
                                    <td class="p-0 pb-1 fw-bold whitespace-nowrap ">{{ !empty($bankDetail) ? $bankDetail->bank_name : null }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-start p-0 pb-1 text-gray whitespace-nowrap ">IFSC
                                        Code:
                                    </td>
                                    <td class="p-0 pb-1 fw-bold">{{ !empty($bankDetail) ? $bankDetail->ifsc_code : null }}
                                    </td>
                                </tr>
                                @if(!empty($bankDetail) && $bankDetail->swift_code != null)
                                    <tr>
                                        <td class="text-start p-0 pb-1 text-gray whitespace-nowrap ">SWIFT Code:
                                        </td>
                                        <th class="p-0 pb-1 fw-bold whitespace-nowrap ">
                                            {{ $bankDetail->swift_code }}</th>
                                    </tr>
                                @endif
                                @if (isset($bankDetail->account_holder_name))
                                    <tr>
                                        <td class="text-start p-0 pb-1 text-gray whitespace-nowrap">
                                            A/C Name:
                                        </td>
                                        <td class="p-0 pb-1 fw-bold whitespace-nowrap">
                                            {{ $bankDetail->account_holder_name }}
                                        </td>
                                    </tr>
                                @endif
                            </table>
                        </td>
                        <td class="w-50 p-0">
                            <table class=" w-100 mb-0">
                                <tr>
                                    <td class="text-start p-0 pb-1  text-gray  whitespace-nowrap ">Bank
                                        A/C:
                                    </td>
                                    <td class=" p-0 pb-1 fw-bold">{{ $accountNumber }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-start p-0 pb-1  text-gray   whitespace-nowrap ">
                                        Branch:
                                    </td>
                                    <td class=" p-0 pb-1 fw-bold">{{ $branchName }}
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </div>

            <table class="w-100">
                <td class="w-50 p-0">
                    <h2 class="fs-13 mb-2 fw-bold">{{ $changeLabel['terms_and_conditions'] ?? 'Terms and conditions' }}:</h2>
                    <p class="fs-12 text-gray mb-1">
                        {!! nl2br($transaction->term_and_condition) !!}
                    </p>
                </td>
                <td style="vertical-align:top;">
                    <h2 class="fs-13 mb-2 fw-bold">{{ $changeLabel['narration'] ?? 'Note' }}:</h2>
                    <p class="fs-12 text-gray">
                        {!! nl2br($transaction->narration) !!}</p>
                </td>
                <td class="p-0">
                    @if (getCustomInvoiceSetting('qr_code') && isset($bankDetail->upi_id))
                        <div class="">
                            <img src="data:image/svg+xml;base64,{{ base64_encode(generateUPIQRCode($bankDetail->upi_id, $transaction->due_amount)) }}"
                                width="130" height="130" />
                            <h2 class=" pt-2" style="font-size: 16px">Payment QR Code</h2>
                        </div>
                    @endif
                </td>
            </table>
            @if(($showPrintSettings['show_sale_authorized_signatory'] ?? true) || ($invoiceSetting['signature'] ?? false))
                <div class="vertical-align-bottom ms-auto">
                    <div class="signature mb-1 ms-auto">
                        @if (($invoiceSetting['signature'] ?? false) && ($currentCompany->company_signature != asset('assets/images/signature.png')))
                            <img src="{{ $currentCompany->company_signature ?? null }}" alt="company-img" style="max-width:100%; height:100%;  object-fit:contain; margin-left:auto;">
                        @endif
                    </div>
                    @if ($showPrintSettings['show_sale_authorized_signatory'] ?? true)
                        <h2 class="fs-13 fw-bold mb-2 text-end ms-auto">For {{ strtoupper($currentCompany->trade_name) }}:</h2>
                    @endif
                </div>
            @endif
        </div>
</body>

</html>
