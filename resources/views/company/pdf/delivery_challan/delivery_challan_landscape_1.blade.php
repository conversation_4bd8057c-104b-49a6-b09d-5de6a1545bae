<!DOCTYPE html>
<html lang="en">
@php
    $pdfSymbol = getPrintPdfCurrencySymbol($currentCompany->id);
    $dockereEnabled = dockerEnabled() && !isset($preview_enabled);
    $fontStyleName = $dockereEnabled ? 'Arial Unicode MS' : 'Arial-unicode-ms';
    $fontCssName = $dockereEnabled ? 'font-docker.css' : 'font.css';
@endphp

<head>
    <meta charset="UTF-8" />
    <title>{{ isset($fileName) ? $fileName : $transaction->challan_number }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/' . $fontCssName) }}">
    @include('company.pdf.sale_pdf.print_custom_font_css_variables')
    <style>

        * {
            margin: 0;
            padding: 0;
            text-indent: 0;
            font-family: "{{ $fontStyleName }}";
            font-size: 13px;
            font-weight: 400;
            box-sizing: border-box;
        }

        @page {
            margin: 20px !important;
        }

        .me-4 {
            margin-right: 24px;
        }

        .mt-1 {
            margin-top: 4px !important;
        }

        .mt-2 {
            margin-top: 8px !important;
        }

        .mb-2 {
            margin-bottom: 8px !important;
        }

        .mb-0 {
            margin-bottom: 0px !important;
        }

        .mb-1 {
            margin-bottom: 4px;
        }

        .mb-3 {
            margin-bottom: 16px !important;
        }

        .mb-4 {
            margin-bottom: 24px !important;
        }

        .p-2 {
            padding: 8px;
        }

        .px-2 {
            padding-left: 8px !important;
            padding-right: 8px !important;
        }

        .px-1 {
            padding-left: 4px !important;
            padding-right: 4px !important;
        }

        .px-3 {
            padding-left: 16px !important;
            padding-right: 16px !important;
        }

        .py-1 {
            padding: 4px 0;
        }

        .p-1 {
            padding: 4px;
        }

        .py-2 {
            padding: 8px 0;
        }

        .ps-1 {
            padding-left: 4px;
        }

        .ps-3 {
            padding-left: 16px;
        }

        .pe-0 {
            padding-right: 0;
        }

        .pe-3 {
            padding-right: 16px;
        }

        .pe-4 {
            padding-right: 24px;
        }

        .pe-5 {
            padding-right: 48px;
        }

        .w-20 {
            width: 20% !important;
        }

        .w-25 {
            width: 25% !important;
        }

        .w-50 {
            width: 50% !important;
        }

        .w-75 {
            width: 75% !important;
        }

        .w-80 {
            width: 80% !important;
        }

        .w-100 {
            width: 100% !important;
        }

        .mb-30px {
            margin-bottom: 30px;
        }

        .fw-medium {
            font-weight: 500;
        }

        .fw-semibold {
            font-weight: 600;
        }

        .fw-bold {
            font-weight: 700;
        }

        .h-100 {
            height: 100% !important;
        }

        .w-100 {
            width: 100%;
        }

        .w-25 {
            width: 25%;
        }

        .w-50 {
            width: 50%;
        }

        .w-60 {
            width: 60%;
        }

        .w-300px {
            width: 250px;
        }

        .w-40 {
            width: 40%;
        }

        .justify-content-end {
            justify-content: flex-end;
        }

        .justify-content-between {
            justify-content: space-between;
        }

        .align-items-center {
            align-items: center !important;
        }

        .align-items-end {
            align-items: end !important;
        }

        .fs-42 {
            font-size: 42px !important;
        }

        .fs-14 {
            font-size: 14px !important;
        }

        .fs-12 {
            font-size: 12px !important;
        }

        .fs-15 {
            font-size: 15px !important;
        }

        .fs-13 {
            font-size: 13px !important;
        }

        .fs-18 {
            font-size: 18px !important;
        }

        .fs-20 {
            font-size: 20px !important;
        }

        .fs-24 {
            font-size: 24px !important;
        }

        .gap-3 {
            column-gap: 24px;
        }

        .word-break {
            word-break: break-all;
        }

        .vertical-align-baseline {
            vertical-align: baseline;
        }

        .vertical-align-top {
            vertical-align: top;
        }

        .d-flex {
            display: flex !important;
        }

        .text-start {
            text-align: start !important;
        }

        .text-center {
            text-align: center !important;
        }

        .text-end {
            text-align: end;
        }

        .border-bottom {
            border-bottom: 1px solid black;
        }

        .border-top {
            border-top: 1px solid black !important;
        }

        .my-3 {
            margin: 24px 0px;
        }

        .mx-2 {
            margin: 0px 8px !important;
        }

        .my-2 {
            margin: 8px 0px !important;
        }

        .pb-2 {
            padding-bottom: 8px;
        }

        .gap-15 {
            gap: 15px;
        }

        .text-nowrap {
            white-space: nowrap;
        }

        .flex-column {
            flex-direction: column !important;
        }

        @page {
            margin: 20px !important;
        }

        .table-container {
            display: flex;
            flex-direction: column;
        }

        .main-table {
            border: 2px solid black;
            border-collapse: collapse;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .item-table-height {
            display: flex;
            flex-grow: 1;
        }

        .content-table tbody tr:last-child td {
            height: 100%;
        }

        .content-table tbody td {
            border-bottom: none !important;
            border-top: none !important;
        }

        .head-table {
            vertical-align: top;
            padding: 15px 10px;
        }

        .logo-hisab {
            letter-spacing: -2px;
        }

        .hisab {
            color:var(--color-theme);
        }

        .kitab {
            margin-left: -5px;
        }

        .font-italic {
            font-style: italic;
        }

        .contact-link {
            text-decoration: none;
            color: black;
            outline: 0 !important;
        }

        .text-primary {
            color: #ffffff !important;
        }

        .bg-primary {
            background-color: #4f158c !important;
        }

        .lh-sm {
            /* line-height: 15px; */
        }

        .pe-1 {
            padding-right: 4px;
        }

        .w-5 {
            /* max-width: 30px;
            width: 30px; */
            min-width: 30px;
        }

        .text-primary {
            color: #4f158c !important;
        }

        table {
            border-collapse: collapse;
        }

        .bg-light {
            background-color: #ede8f3 !important;
        }

        .tax {
            border-bottom: 1px solid black;
        }

        .money {
            font-style: italic;
            color: #4f158c;
        }

        .pdf-img {
            height: 110px;
            min-width: 120px;
        }

        .qr-blur-section {
            width: 465px;
        }

        .qr-blur-img {
            max-height: 130px;
            height: 100%;
            min-width: 100px;
            max-width: 100px;
            width: 130px;
        }

        .qr-img {
            width: 90px;
            height: 90px;
        }

        .sign {
            margin: 0 auto;
            height: 135px;
            width: 165px;
            justify-content: center;
            overflow: hidden;
        }

        .ms-auto {
            margin-left: auto;
        }

        .bill-table {
            border: 1px;
            border-style: solid none none none;
            border-color: black;
        }

        .content-table {
            border-style: none;
        }

        @media print {
            .qr-table {
                /* page-break-before: always; */
                page-break-inside: avoid;
            }
        }

        .content-table th,
        .tax-table thead th {
            border-left: 1px solid black;
            border-bottom: 1px solid black;
            border-right: 1px solid black;
        }

        .tax-table {
            border-top: none !important;
        }

        .tax-table td {
            border-top: none !important;
            border-bottom: none !important;
        }

        .tax-table tfoot td {
            border: 1px solid black !important;
        }

        .content-table td:nth-child(2) {
            width: 100%
        }

        .tax-table {
            border: 1px;
            border-style: none none solid none;
            border-color: black;
        }

        .qr-table td :last-child {
            border-right: 0 !important;
        }

        .qr-table td {
            border-right: 1px solid black;
        }

        .total-table td {
            border-right: 0 !important;
        }

        .content-table td {
            border: 1px solid black;
        }

        .pdf-intro {
            width: 450px;
        }

        .bill-table td,
        .bill-table th {
            border: 1px solid black;
        }


        .bill-table th:first-child,
        .bill-table td:first-child,
        .content-table th:first-child,
        .content-table td:first-child,
        .tax-table td:first-child,
        .tax-table th:first-child {
            border-left: 0 !important;
        }

        .bill-table th:last-child,
        .bill-table td:last-child,
        .content-table th:last-child,
        .content-table td:last-child,
        .tax-table td:last-child,
        .tax-table th:last-child,
        .qr-table td:last-child {
            border-right: 0 !important;
        }

        .total-content {
            border-right: 1px solid black;
        }

        .table-intro,
        .table-intro td {
            border: none !important;
        }

    </style>
    @include('company.pdf.sale_pdf.print_custom_font_css')
</head>

<body>
    <div>
        <div wire:loading.class="page-loader">
        </div>
        <div class="main-table w-100">

            {{-- slogan --}}
            @if (($invoiceSetting['show_slogan'] ?? false) && isset($invoiceSetting['slogan']))
                <div class="text-center">
                    <p class="text-center company-address-font-size  border-bottom py-1">
                        {{ $invoiceSetting['slogan'] }}
                    </p>
                </div>
            @endif

            {{-- invoice Type --}}
            <h2 class="text-center fs-14 py-1 border-bottom fw-medium">
                {{ $deliveryChallanTransactionMaster->title_of_print }}
            </h2>

            {{-- company details --}}
            <table class="w-100 ">
                <tbody>
                    <tr>
                        <td class="head-table vertical-align-baseline w-80">
                            <div class="d-flex align-items-center">
                                @if (isset($currentCompany->company_logo) &&
                                        ($invoiceSetting['delivery_challan_logo'] ?? true) &&
                                        $currentCompany->company_logo != asset('images/preview-img.png'))
                                    <img src={{ $currentCompany->company_logo ?? '' }} alt="Logo"
                                        style="object-fit: contain;margin: 0px 3px;" width="100">
                                @endif
                                <div class="pdf-intro">
                                    <h1 class="mt-2 company-name-font-size company-font-customization">{{ strtoupper($currentCompany->trade_name) }}</h1>
                                    @if(($invoiceSetting['prop_details'] ?? true) && isset($customProp))
                                        <p class="mt-1 fw-6 company-address-font-size">{{ $customProp->label_name.' : '.$customProp->label_value }}</p>
                                    @endif

                                    @if ($currentCompany->is_gst_applicable)
                                        <p class="company-address-font-size lh-sm mb-0">
                                            {{ $changeLabel['delivery_challan_gstin_label'] ?? 'GSTIN' }}:
                                            {{ '  ' . $currentCompany->companyTax->gstin ?? null }}
                                        </p>
                                    @endif
                                    <p class="mb-0 company-address-font-size">
                                        {{ strtoupper($companyBillingAddress->address_1 ?? null) }},
                                        {{ strtoupper($companyBillingAddress->address_2 ?? null) }},
                                        {{ strtoupper(getCityName($companyBillingAddress->city_id ?? null)) }},
                                        {{ strtoupper(getStateName($companyBillingAddress->state_id ?? null)) }},
                                        {{ strtoupper(getCountryName($companyBillingAddress->country_id ?? null)) }},
                                        {{ $companyBillingAddress->pin_code ?? null }}
                                    </p>
                                    @if(($invoiceSetting['delivery_challan_mobile_number'] ?? true) || ($invoiceSetting['delivery_challan_email'] ?? true))
                                        <p class="company-address-font-size lh-sm mb-0">
                                            @if ($invoiceSetting['delivery_challan_mobile_number'] ?? true)
                                                {{ $changeLabel['delivery_challan_tel_label'] ?? 'Tel' }} :
                                                {{ (isset($invoiceSetting['region_code']) ? '+' . $invoiceSetting['region_code'] . ' ' : '') . ($invoiceSetting['alternate_phone'] ?? ($currentCompany->phone ?? null)) }}
                                                {{ isset($invoiceSetting['region_code_2']) && isset($invoiceSetting['alternate_phone_2']) ? ', ' . '+' . $invoiceSetting['region_code_2'] . ' ' . $invoiceSetting['alternate_phone_2'] : (isset($invoiceSetting['alternate_phone_2']) ? ', ' . $invoiceSetting['alternate_phone_2'] : '') }}
                                            @endif
                                            @if (
                                                ($invoiceSetting['mobile_number'] ?? true) &&
                                                    ($invoiceSetting['email'] ?? true) &&
                                                    ($invoiceSetting['alternate_email'] ?? ($currentCompany->user->email ?? null)) != null)
                                                |
                                            @endif
                                            @if (isset($invoiceSetting['delivery_challan_email']) ? $invoiceSetting['delivery_challan_email'] : true)
                                                {{ $alternate_email ?? ($currentCompany->user->email ?? null) }}
                                            @endif
                                        </p>
                                    @endif
                                    @foreach (printCustomPDFLabelsForDeliveryChallan() as $key => $customLabel)
                                        <p class="company-address-font-size lh-sm mb-0">
                                            {{ $key ?? null }}: {{ $customLabel ?? null }}
                                        </p>
                                    @endforeach
                                </div>
                            </div>
                        </td>
                        <td class="head-table vertical-align-baseline w-20">
                            <div class="ms-auto qr-blur-section">
                                <div class="me-4 d-flex align-items-center justify-content-between gap-3">
                                    <table class="w-100 ">
                                        <tbody>
                                            <tr>
                                                <td class="header-contents-font-size fw-semibold pe-1 lh-sm vertical-align-top">
                                                    {{ $changeLabel['delivery_challan_number_label'] ?? 'Challan No' }}:
                                                </td>
                                                <td class="header-contents-font-size fw-medium lh-sm text-nowrap vertical-align-top">
                                                    {{ $transaction->challan_number }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="header-contents-font-size fw-semibold pe-1 lh-sm text-nowrap vertical-align-top">
                                                    {{ $changeLabel['delivery_challan_date_label'] ?? 'Challan Date' }}:
                                                </td>
                                                <td class="header-contents-font-size fw-medium lh-sm text-nowrap vertical-align-top">
                                                    {{ Carbon\Carbon::parse($transaction->challan_date)->format('d-m-Y') }}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>

            {{-- customer Detail --}}
            <table class="w-100">
                <tbody>
                    <tr>
                        <td class="w-100">
                            <div>
                                <table class="bill-table w-100">
                                    <thead>
                                        <tr>
                                            <th class="px-2">
                                                <p class="fw-bold header-labels-font-size text-primary text-start fw-6">
                                                    {{ $changeLabel['delivery_challan_bill_to_label'] ?? 'Billing Address' }}:
                                                </p>
                                            </th>
                                            @if ($invoiceSetting['delivery_challan_ship_to_details'] ?? true)
                                                <th class="px-2">
                                                    <p class="fw-bold header-labels-font-size  text-start text-primary py-1">
                                                        {{ $changeLabel['delivery_challan_ship_to_label'] ?? 'Shipping Address' }}:
                                                    </p>
                                                </th>
                                            @endif
                                            @if (($invoiceSetting['delivery_challan_transport_details'] ?? true) || !empty($transaction->transporter_vehicle_number))
                                                <th class="px-2">
                                                    <p class="fw-bold header-labels-font-size mx-2 text-start text-primary py-1">
                                                        Transport Details:
                                                    </p>
                                                </th>
                                            @endif
                                            @if (($invoiceSetting['delivery_challan_po_number'] ?? true) || ($invoiceSetting['delivery_challan_broker_details'] ?? true))
                                                <th class="px-2">
                                                    <p class="fw-bold header-labels-font-size mx-2 text-start text-primary py-1">
                                                        Other Details:
                                                    </p>
                                                </th>
                                            @endif
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="w-25 px-2 vertical-align-top">
                                                <div>
                                                    <p class="header-contents-font-size mb-1 lh-sm lh-sm fw-semibold">
                                                        {{ strtoupper($customerDetail->name) }}</p>
                                                    <p class="header-contents-font-size mb-1">
                                                        @if ($showGst)
                                                            <p class="header-contents-font-size">
                                                                <span class="fw-medium header-contents-font-size">GSTIN</span>
                                                                : {{ $transaction->gstin ?? $customerDetail->model->gstin ?? null }}
                                                            </p>
                                                        @endif
                                                        @if (!empty($panNumber) && $showPanNumber)
                                                            <p class="header-contents-font-size">
                                                                PAN: {{ $panNumber ?? null }}
                                                            </p>
                                                        @endif
                                                    </p>
                                                    @if (isset($billingAddress))
                                                        <p class="header-contents-font-size mb-1  lh-sm">
                                                            @if ($billingAddress->address_1 != null)
                                                                {{ strtoupper($billingAddress->address_1) }}
                                                            @endif
                                                            @if ($billingAddress->address_2 != null)
                                                                {{ strtoupper($billingAddress->address_2) }},
                                                            @endif
                                                            {{ $billingAddress->city_id != null ? strtoupper(getCityName($billingAddress->city_id)) . ',' : null }}
                                                            {{ $billingAddress->state_id != null ? strtoupper(getStateName($billingAddress->state_id)) . ',' : null }}
                                                            {{ strtoupper(getCountryName($billingAddress->country_id ?? null)) }},
                                                            {{ $billingAddress->pin_code ?? null }}
                                                        </p>
                                                    @endif
                                                    <div class="header-contents-font-size d-flex lh-sm">
                                                        <p class="header-contents-font-size">
                                                            @if (!empty($transaction->party_phone_number))
                                                                Contact No:
                                                                    +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                                                            @elseif (!empty($customerDetail->model->phone_1))
                                                                Contact No:
                                                                    +{{ $customerDetail->model->region_code_1 ?? '' }} {{ $customerDetail->model->phone_1 ?? null }}
                                                            @endif
                                                            @if (!empty($customerDetail->model->phone_2) && ($customerDetail->model->phone_1 !== $customerDetail->model->phone_2))
                                                                {{ empty($customerDetail->model->phone_1) && empty($transaction->party_phone_number) ? 'Contact No:' : "|" }}
                                                                <span class="text-nowrap header-contents-font-size">
                                                                    +{{ $customerDetail->model->region_code_2 ?? '' }} {{ $customerDetail->model->phone_2 ?? null }}
                                                                </span>
                                                            @endif
                                                        </p>
                                                    </div>
                                                    @if (!empty($customerDetail->model->person_email))
                                                        <p class="header-contents-font-size">
                                                            Email: {{ $customerDetail->model->person_email ?? null }}
                                                        </p>
                                                    @endif
                                                </div>
                                            </td>
                                            @if ($invoiceSetting['delivery_challan_ship_to_details'] ?? true)
                                                <td class="w-25 px-2 vertical-align-top">
                                                    <div>
                                                        <p class="header-contents-font-size mb-1 lh-sm lh-sm fw-semibold">
                                                            {{ strtoupper($customerDetail->name) }}</p>
                                                        <p class="fs-13 mb-1">
                                                            @if ($isCompanyGstApplicable && !empty($transaction->shipping_gstin))
                                                                <p class="header-contents-font-size">
                                                                    <span class="fw-medium header-contents-font-size">GSTIN</span>
                                                                    : {{ $transaction->shipping_gstin ?? null }}
                                                                </p>
                                                            @endif
                                                            @if (!empty($panNumber) && $showPanNumber)
                                                                <p class="header-contents-font-size">
                                                                    PAN: {{ $panNumber ?? null }}
                                                                </p>
                                                            @endif
                                                        </p>

                                                        <p class="header-contents-font-size mb-1  lh-sm">
                                                            @if (isset($shippingAddress->address_1))
                                                                {{ strtoupper($shippingAddress->address_1) }},
                                                            @endif
                                                            @if (isset($shippingAddress->address_2))
                                                                {{ strtoupper($shippingAddress->address_2) }},
                                                            @endif
                                                            {{ isset($shippingAddress->city_id) ? strtoupper(getCityName($shippingAddress->city_id)) . ',' : '' }}
                                                            {{ isset($shippingAddress->state_id) ? strtoupper(getStateName($shippingAddress->state_id)) . ',' : '' }}
                                                            {{ isset($shippingAddress->state_id) ? strtoupper(getCountryName($shippingAddress->country_id)) : '' }},
                                                            {{ $shippingAddress->pin_code ?? null }}
                                                        </p>
                                                        <div class="header-contents-font-size d-flex lh-sm">
                                                            <p class="header-contents-font-size">
                                                                @if (!empty($transaction->party_phone_number))
                                                                    Contact No:
                                                                        +{{ $transaction->region_code ?? '' }} {{ $transaction->party_phone_number ?? null }}
                                                                @endif
                                                            </p>
                                                        </div>
                                                    </div>
                                                </td>
                                            @endif
                                            @if (($invoiceSetting['delivery_challan_transport_details'] ?? true) || !empty($transaction->transporter_vehicle_number))
                                                <td class="w-25 px-2 vertical-align-top">
                                                    <div class="">
                                                        <table class="table-intro">
                                                            <tbody>
                                                                @if ($invoiceSetting['delivery_challan_transport_details'] ?? true)
                                                                    <tr>
                                                                        <td class="vertical-align-baseline">
                                                                            <p
                                                                                class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                {{ $changeLabel['delivery_challan_transport_name_label'] ?? 'Transport Name' }}
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <div
                                                                                class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                <span class="header-contents-font-size">:</span>
                                                                                <p class="ps-1 header-contents-font-size">
                                                                                    {{ $transaction->transportDetails->transporter_name ?? '' }}
                                                                                </p>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>
                                                                            <p class="header-contents-font-size mb-1 pe-3 lh-sm lh-sm">
                                                                                {{ $changeLabel['document_no'] ?? 'Document No' }}
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <div
                                                                                class="header-contents-font-size d-flex word-break lh-sm lh-sm">
                                                                                <span class="header-contents-font-size">:</span>
                                                                                <p class="ps-1 header-contents-font-size">
                                                                                    {{ $transaction->transporter_document_number ?? '' }}
                                                                                </p>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>
                                                                            <p class="header-contents-font-size mb-1 pe-3 lh-sm lh-sm">
                                                                                {{ $changeLabel['document_date'] ?? 'Document Date' }}
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <div
                                                                                class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                <span class="header-contents-font-size">:</span>
                                                                                <p class="ps-1 header-contents-font-size">
                                                                                    {{ !empty($transaction->transporter_document_date) ? \Carbon\Carbon::parse($transaction->transporter_document_date)->format('d-m-Y') : '' }}
                                                                                </p>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                @endif
                                                                @if (!empty($transaction->transporter_vehicle_number))
                                                                    <tr>
                                                                        <td>
                                                                            <p class="header-contents-font-size pe-3 lh-sm lh-sm">
                                                                                {{ $changeLabel['transport_vehicle_number'] ?? 'Vehicle No' }}
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <div
                                                                                class="header-contents-font-size d-flex word-break lh-sm lh-sm">
                                                                                <span class="header-contents-font-size">:</span>
                                                                                <p class="ps-1 header-contents-font-size">
                                                                                    {{ $transaction->transporter_vehicle_number ?? '' }}
                                                                                </p>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                @endif
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </td>
                                            @endif
                                            @if (($invoiceSetting['delivery_challan_po_number'] ?? true) || ($invoiceSetting['delivery_challan_broker_details'] ?? true))
                                                <td class="w-25 px-2 pe-0 vertical-align-top">
                                                    <div class="">
                                                        <table class="table-intro">
                                                            <tbody>
                                                                @if ($invoiceSetting['delivery_challan_broker_details'] ?? true)
                                                                    <tr>
                                                                        <td>
                                                                            <p
                                                                                class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                {{ $changeLabel['broker'] ?? 'Broker' }}
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <div
                                                                                class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                <span class="header-contents-font-size">:</span>
                                                                                <p class="ps-1 header-contents-font-size">
                                                                                    {{ $transaction->brokerDetails->broker_name ?? '' }}
                                                                                </p>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    @if($isCompanyGstApplicable)
                                                                    <tr>
                                                                        <td>
                                                                            <p
                                                                                class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                {{ $changeLabel['broker_gstin'] ?? 'GSTIN' }}
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <div
                                                                                class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                <span class="header-contents-font-size">:</span>
                                                                                <p class="ps-1 header-contents-font-size">
                                                                                    {{ $transaction->brokerDetails->gstin ?? '' }}
                                                                                </p>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    @endif
                                                                @endif
                                                                @if ($invoiceSetting['delivery_challan_po_number'] ?? true)
                                                                    <tr>
                                                                        <td>
                                                                            <p
                                                                                class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                {{ $changeLabel['delivery_challan_po_number_label'] ?? 'PO No' }}
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <div
                                                                                class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                <span class="header-contents-font-size">:</span>
                                                                                <p class="ps-1 header-contents-font-size">
                                                                                    {{ $transaction->po_no }}
                                                                                </p>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                @endif
                                                                @if ($invoiceSetting['show_delivery_challan_po_date'] ?? true)
                                                                    <tr>
                                                                        <td>
                                                                            <p
                                                                                class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                {{ $changeLabel['delivery_challan_po_date_label'] ?? 'PO Date' }}
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <div
                                                                                class="header-contents-font-size d-flex mb-1 word-break lh-sm lh-sm">
                                                                                <span class="header-contents-font-size">:</span>
                                                                                <p class="ps-1 header-contents-font-size">
                                                                                    {{ isset($transaction->po_date) ? \Carbon\Carbon::parse($transaction->po_date)->format('d-m-Y') : null }}
                                                                                </p>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                @endif
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </td>
                                            @endif
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        @php
            if (empty($addLess)) {
                $total = $transaction->grand_total;
            } else {
                $addLessSum =  array_sum(array_column($addLess, 'amount'));
                $total = $transaction->grand_total - $addLessSum;
                $addLessSumTotal = collect($addLess)->where('is_show_in_print',1)->sum('amount');
                $total = $total + $addLessSumTotal;
            }
        @endphp
            {{-- Custom Fields Section Start --}}
            @if (count($customFieldValues) > 0)
                @php
                    $customFields = collect($customFieldValues)->where('is_show_in_print', true)->values();
                @endphp
                <table cellpadding="0" class="item-table">
                    @foreach ($customFields->chunk(3) as $chunk)
                        <tr class="border-bottom">
                            @foreach ($chunk as $customField)
                                <td style="padding: 6px 8px; width:150px; {{ $loop->last ? '' : 'border-right: 1px solid black;' }}">
                                    <span style="font-weight: bold;">{{ $customField['label_name'] ?? '' }}</span> : {{ $customField['value'] ?? '' }}
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                </table>
            @endif
            {{-- Custom Fields Section End --}}

            @php
                $customFieldItemsValues = collect($transactionItems[0]['customItemsValues'])->where('is_show_in_print', true)->values();
                $customFieldItemsHeaders = collect($customFieldItemsValues)->pluck('label_name')->toArray();
                $cfNumberTypeFieldTotals = [];
            @endphp
            <div class="item-table-height">
                {{-- Item Table Section Start --}}
                @if ($itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT)
                    <table class="content-table w-100">
                        <thead>
                            <tr class="vertical-align-baseline">
                                <th class="w-5">
                                    <div class="py-1 px-1 w-5">
                                        <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                            {{ $changeLabel['delivery_challan_sn_label'] ?? 'SN' }}
                                        </p>
                                    </div>
                                </th>
                                <th>
                                    <div class="py-1 px-1 w-300px">
                                        <p class="table-headings-font-size fw-medium text-start lh-sm fw-bold text-nowrap">
                                            {{ $changeLabel['delivery_challan_item_name_label'] ?? 'Item Name' }}
                                        </p>
                                    </div>
                                </th>
                                @if (count($customFieldItemsHeaders) > 0)
                                    @foreach ($customFieldItemsHeaders as $customFieldItemHeader)
                                        <th>
                                            <div class="py-1 px-1 w-300px">
                                                <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                                    {{ $customFieldItemHeader ?? '' }}
                                                </p>
                                            </div>
                                        </th>
                                    @endforeach
                                @endif
                                @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_hsn_sac'] ?? true))
                                    <th>
                                        <div class="py-1 px-3">
                                            <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                                {{ $changeLabel['delivery_challan_hsn_sac_label'] ?? 'HSN/SAC' }} </p>
                                        </div>
                                    </th>
                                @endif
                                @if ($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                    <th>
                                        <div class="py-1 px-3">
                                            <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                                {{ $changeLabel['delivery_challan_qty_label'] ?? 'Qty' }}
                                            </p>
                                        </div>
                                    </th>
                                @endif
                                @if ($showPrintSettings['delivery_challan_uom_enable'] ?? false)
                                    @if ($transactionItems->sum('secondary_quantity') != 0.0)
                                        <th>
                                            <div class="py-1 px-3">
                                                <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                                    {{ $changeLabel['delivery_challan_uom_label'] ?? 'UOM' }}
                                                </p>
                                            </div>
                                        </th>
                                    @endif
                                @endif
                                @if ($showPrintSettings['show_delivery_challan_mrp'] ?? true)
                                    @if ($transactionItems->sum('mrp') != 0.0)
                                        <th>
                                            <div class="py-1 px-3">
                                                <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                                    {{ $changeLabel['delivery_challan_mrp_label'] ?? 'MRP' }}
                                                </p>
                                            </div>
                                        </th>
                                    @endif
                                @endif
                                @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_rate_with_gst'] ?? true))
                                    <th>
                                        <div class="py-1 px-3">
                                            <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                                {{ $changeLabel['delivery_challan_rate_with_gst_label'] ?? 'Rate With GST' }}
                                            </p>
                                        </div>
                                    </th>
                                @endif
                                @if ($showPrintSettings['show_delivery_challan_rate'] ?? true)
                                    <th>
                                        <div class="py-1 px-3">
                                            <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                                {{ $changeLabel['delivery_challan_rate_label'] ?? 'Rate' }}
                                            </p>
                                        </div>
                                    </th>
                                @endif
                                @if (($showPrintSettings['show_delivery_challan_discount'] ?? true) && $transactionItems->sum('discount_value') != 0.0)
                                    <th>
                                        <div class="py-1 px-3">
                                            <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                                {{ $changeLabel['delivery_challan_discount_label'] ?? 'Dis.' }}
                                            </p>
                                        </div>
                                    </th>
                                @endif
                                @if (($showPrintSettings['delivery_challan_dis_2_enable'] ?? true) && $transactionItems->sum('discount_value_2') != 0.0)
                                    <th>
                                        <div class="py-1 px-3">
                                            <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                                {{ $changeLabel['delivery_challan_dis_2_label'] ?? 'Dis. 2' }}
                                            </p>
                                        </div>
                                    </th>
                                @endif
                                @if (($showPrintSettings['show_delivery_challan_total_discount'] ?? true) && $transactionItems->sum('total_discount_amount') != 0.0)
                                    <th>
                                        <div class="py-1 px-3">
                                            <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                                {{ $changeLabel['delivery_challan_total_discount_label'] ?? 'Total Dis.' }}
                                            </p>
                                        </div>
                                    </th>
                                @endif
                                @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_gst'] ?? true))
                                    <th>
                                        <div class="py-1 px-3">
                                            <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                                {{ $changeLabel['delivery_challan_gst_label'] ?? 'GST (%)' }}
                                            </p>
                                        </div>
                                    </th>
                                @endif
                                <th>
                                    <div class="py-1 px-3">
                                        <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                            {{ $changeLabel['delivery_challan_taxable_value_label'] ?? ($isCompanyGstApplicable ? 'Taxable Value' : 'Amount') }}
                                        </p>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($transactionItems as $key => $item)
                                @php
                                    $uniqueId = ++$key;
                                    $ids = collect($customFieldItemsValues)->pluck('custom_field_item_id')->values();
                                    $printCustomFields = collect($item['customItemsValues'])->where('is_show_in_print', true)->whereIn('custom_field_item_id', $ids)->values();
                                    foreach ($item['customItemsValues'] as $customField) {
                                        $cfId = $customField['custom_field_id'];
                                        if ($customField['show_total'] && $customField['custom_field_type'] == \App\Models\ItemCustomField::CF_TYPE_NUMBER) {
                                            $value = (float) $customField['value'] ?? 0;
                                            if (! isset($cfNumberTypeFieldTotals[$cfId])) {
                                                $cfNumberTypeFieldTotals[$cfId] = 0;
                                            }
                                            $cfNumberTypeFieldTotals[$cfId] += $value;
                                        } else {
                                            $cfNumberTypeFieldTotals[$cfId] = null;
                                        }
                                    }
                                @endphp
                                <tr class="data1 vertical-align-baseline">
                                    <td class="table-contents-font-size text-center p-1 word-break lh-sm fw-medium w-5">
                                        {{ $uniqueId }}
                                    </td>
                                    <td class="table-contents-font-size p-1 w-300px">
                                        <div>
                                            <h2 class="table-contents-font-size fw-medium lh-sm">
                                                {{ $item->items->item_name ?? null }}</h2>
                                            @if ($item->items->sku != null && ($invoiceSetting['show_delivery_challan_item_sku'] ?? true))
                                                <p class="description-font-size">
                                                    Item Code: {{ $item->items->sku ?? null }}
                                                </p>
                                            @endif
                                            <p class="description-font-size lh-sm font-italic">{!! !empty($item->additional_description) ? nl2br('(' . $item->additional_description . ')') : null !!}</p>
                                            @if (!empty($item->items->item_image) && ($invoiceSetting['show_item_image'] ?? true))
                                                <img src="{{ $item->items->item_image }}" width="60" height="60"
                                                    style="margin-top: 4px">
                                            @endif
                                        </div>
                                    </td>
                                    @if (count($printCustomFields) > 0)
                                        @foreach ($printCustomFields as $customFieldItemsValue)
                                            <td class="table-contents-font-size text-center p-1 lh-sm"
                                                style="padding: 4px 8px 0 8px; ">
                                                {{ $customFieldItemsValue['value'] ?? '' }}
                                            </td>
                                        @endforeach
                                    @endif
                                    @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_hsn_sac'] ?? true))
                                        <td class="table-contents-font-size text-center p-1 lh-sm text-nowrap">
                                            {{ $item->hsn_code ?? $item->items->model->hsn_sac_code ?? null }}
                                        </td>
                                    @endif
                                    @if ($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                        <td class="table-contents-font-size text-center p-1 lh-sm fw-medium w-20">
                                            <div>
                                                <p class="text-nowrap table-contents-font-size">
                                                    {{ $item->primary_quantity }}
                                                    @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                                                    {{ $item->primary_unit_name }}
                                                    @endif
                                                </p>
                                            </div>
                                        </td>
                                    @endif
                                    @if ($showPrintSettings['delivery_challan_uom_enable'] ?? false)
                                        @if ($transactionItems->sum('secondary_quantity') != 0.0)
                                            <td class="table-contents-font-size text-center p-1 lh-sm">
                                                <div>
                                                    <p class="text-nowrap table-contents-font-size">
                                                        {{ round($item->secondary_quantity, 2) }}
                                                        @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                                                        {{ $item->secondary_unit_name }}
                                                        @endif
                                                    </p>
                                                </div>
                                            </td>
                                        @endif
                                    @endif
                                    @if ($showPrintSettings['show_delivery_challan_mrp'] ?? true)
                                        @if ($transactionItems->sum('mrp') != 0.0)
                                            <td class="table-contents-font-size text-center p-1 lh-sm fw-medium text-nowrap">
                                                {{ !empty($item->mrp) ? $pdfSymbol . getCurrencyFormatFor3digit($item->mrp) : '-' }}
                                            </td>
                                        @endif
                                    @endif
                                    @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_rate_with_gst'] ?? true))
                                        <td class="table-contents-font-size text-center p-1 lh-sm fw-medium text-nowrap">
                                            {{ $pdfSymbol . getCurrencyFormatFor3digit($item->rpu_with_gst) }}
                                        </td>
                                    @endif
                                    @if ($showPrintSettings['show_delivery_challan_rate'] ?? true)
                                        <td class="table-contents-font-size text-center p-1 lh-sm fw-medium text-nowrap">
                                            {{ $pdfSymbol . getCurrencyFormatFor3digit($item->rpu_without_gst) }}
                                        </td>
                                    @endif
                                    @if (($showPrintSettings['show_delivery_challan_discount'] ?? true) && $transactionItems->sum('discount_value') != 0.0)
                                        <td class="table-contents-font-size text-center p-1 lh-sm fw-medium text-nowrap">
                                            @if ($item->discount_type == \App\Models\DeliveryChallanTransaction::DISCOUNT_TYPE_AMOUNT)
                                                {{ $pdfSymbol . getCurrencyFormatFor3digit($item->discount_value) ?? '0.0' }}
                                            @else
                                                {{ $item->discount_value . '(%)' ?? '0.0(%)' }}
                                            @endif
                                        </td>
                                    @endif
                                    @if (($showPrintSettings['delivery_challan_dis_2_enable'] ?? true) && $transactionItems->sum('discount_value_2') != 0.0)
                                        <td class="table-contents-font-size text-center p-1 lh-sm fw-medium text-nowrap">
                                            @if ($item->discount_type_2 == \App\Models\DeliveryChallanTransaction::DISCOUNT_TYPE_AMOUNT)
                                                {{ $pdfSymbol . getCurrencyFormatFor3digit($item->discount_value_2) ?? '0.0' }}
                                            @else
                                                {{ $item->discount_value_2 . '(%)' ?? '0.0(%)' }}
                                            @endif
                                        </td>
                                    @endif
                                    @if (($showPrintSettings['show_delivery_challan_total_discount'] ?? true) && $transactionItems->sum('total_discount_amount') != 0.0)
                                        <td class="table-contents-font-size text-center p-1 lh-sm fw-medium text-nowrap">
                                            @if ($transactionItems->sum('total_discount_amount') != 0.0)
                                                {{ $pdfSymbol . getCurrencyFormatFor3digit($item->total_discount_amount ?? '0.0') }}
                                            @endif
                                        </td>
                                    @endif
                                    @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_gst'] ?? true))
                                        <td class="table-contents-font-size text-center p-1 lh-sm fw-medium text-nowrap">
                                            {{ $item->gst_tax_percentage ?? '0.0' }}
                                        </td>
                                    @endif
                                    <td class="table-contents-font-size text-end p-1 lh-sm fw-medium text-nowrap">
                                        {{ $pdfSymbol . getCurrencyFormat(round($item->total ?? '0.0', getCompanyFixedDigitNumber())) }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                        <tfoot>
                            <tr>
                                <td></td>
                                <td class="table-headings-font-size fw-medium lh-sm fw-bold text-nowrap py-1 px-2">Total</td>
                                @if (count($customFieldItemsValues) > 0)
                                    @foreach ($customFieldItemsValues as $customFieldItemsValue)
                                        <td class="table-headings-font-size fw-medium lh-sm fw-bold text-nowrap py-1 px-2 text-center">
                                            {{ $cfNumberTypeFieldTotals[$customFieldItemsValue['custom_field_id']] ?? '' }}
                                        </td>
                                    @endforeach
                                @endif
                                @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_hsn_sac'] ?? true))
                                    <td></td>
                                @endif
                                @if ($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                    <td class="table-headings-font-size fw-medium lh-sm fw-bold text-nowrap py-1 px-2 text-center">
                                        {{ getCurrencyFormat($transactionItems->sum('primary_quantity')) }}
                                    </td>
                                @endif
                                @if ($showPrintSettings['delivery_challan_uom_enable'] ?? false)
                                    @if ($transactionItems->sum('secondary_quantity') != 0.0)
                                        <td class="table-headings-font-size fw-medium lh-sm fw-bold text-nowrap py-1 px-2 text-center">
                                            {{ getCurrencyFormat($transactionItems->sum('secondary_quantity')) }}
                                        </td>
                                    @endif
                                @endif
                                @if ($showPrintSettings['show_delivery_challan_mrp'] ?? true)
                                    @if ($transactionItems->sum('mrp') != 0.0)
                                        <td></td>
                                    @endif
                                @endif
                                @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_rate_with_gst'] ?? true))
                                    <td></td>
                                @endif
                                @if ($showPrintSettings['show_delivery_challan_rate'] ?? true)
                                    <td></td>
                                @endif
                                @if (($showPrintSettings['show_delivery_challan_discount'] ?? true) && $transactionItems->sum('discount_value') != 0.0)
                                    <td></td>
                                @endif
                                @if (($showPrintSettings['delivery_challan_dis_2_enable'] ?? true) && $transactionItems->sum('discount_value_2') != 0.0)
                                    <td></td>
                                @endif
                                @if (($showPrintSettings['show_delivery_challan_total_discount'] ?? true) && $transactionItems->sum('total_discount_amount') != 0.0)
                                    <td class="table-headings-font-size fw-medium lh-sm fw-bold text-nowrap py-1 px-2 text-center">
                                        {{ $pdfSymbol . getCurrencyFormat($transactionItems->sum('total_discount_amount' ?? 0.0)) }}
                                    </td>
                                @endif
                                @if ($isCompanyGstApplicable && ($showPrintSettings['show_delivery_challan_gst'] ?? true))
                                    <td></td>
                                @endif
                                <td class="table-headings-font-size fw-medium lh-sm fw-bold text-nowrap p-1 text-end">
                                    {{ $pdfSymbol . getCurrencyFormat(round($transaction->gross_value, getCompanyFixedDigitNumber())) }}
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                @else
                    <table class="content-table w-100">
                        <thead>
                            <tr class="vertical-align-baseline">
                                <th class="w-5">
                                    <div class="py-1 px-1 w-5">
                                        <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                            {{ $changeLabel['delivery_challan_sn_label'] ?? 'SN' }}
                                        </p>
                                    </div>
                                </th>
                                <th>
                                    <div class="py-1 px-1 w-300px">
                                        <p class="table-headings-font-size fw-medium text-start lh-sm fw-bold text-nowrap">
                                            {{ $changeLabel['delivery_challan_item_name_label'] ?? 'Item Name' }}
                                        </p>
                                    </div>
                                </th>
                                @if (count($customFieldItemsHeaders) > 0)
                                    @foreach ($customFieldItemsHeaders as $customFieldItemHeader)
                                        <th>
                                            <div class="py-1 px-1 w-300px">
                                                <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                                    {{ $customFieldItemHeader ?? '' }}
                                                </p>
                                            </div>
                                        </th>
                                    @endforeach
                                @endif
                                @if ($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                    <th>
                                        <div class="py-1 px-3">
                                            <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                                {{ $changeLabel['delivery_challan_qty_label'] ?? 'Qty' }}
                                            </p>
                                        </div>
                                    </th>
                                @endif
                                @if (($showPrintSettings['delivery_challan_uom_enable'] ?? false) && $transactionItems->sum('secondary_quantity') != 0.0)
                                    <th>
                                        <div class="py-1 px-3">
                                            <p class="table-headings-font-size fw-medium text-center lh-sm fw-bold text-nowrap">
                                                {{ $changeLabel['delivery_challan_uom_label'] ?? 'UOM' }}
                                            </p>
                                        </div>
                                    </th>
                                @endif
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($transactionItems as $key => $item)
                                @php
                                    $uniqueId = ++$key;
                                    $ids = collect($customFieldItemsValues)->pluck('custom_field_item_id')->values();
                                    $printCustomFields = collect($item['customItemsValues'])->where('is_show_in_print', true)->whereIn('custom_field_item_id', $ids)->values();
                                    foreach ($item['customItemsValues'] as $customField) {
                                        $cfId = $customField['custom_field_id'];
                                        if ($customField['show_total'] && $customField['custom_field_type'] == \App\Models\ItemCustomField::CF_TYPE_NUMBER) {
                                            $value = (float) $customField['value'] ?? 0;
                                            if (! isset($cfNumberTypeFieldTotals[$cfId])) {
                                                $cfNumberTypeFieldTotals[$cfId] = 0;
                                            }
                                            $cfNumberTypeFieldTotals[$cfId] += $value;
                                        } else {
                                            $cfNumberTypeFieldTotals[$cfId] = null;
                                        }
                                    }
                                @endphp
                                <tr class="data1 vertical-align-baseline">
                                    <td class="table-contents-font-size text-center p-1 word-break lh-sm fw-medium w-5">
                                        {{ $uniqueId }}
                                    </td>
                                    <td class="table-contents-font-size p-1 w-300px">
                                        <div>
                                            <h2 class="table-contents-font-size fw-medium lh-sm">
                                                {{ $item->items->item_name ?? null }}</h2>
                                                @if ($item->items->sku != null && ($invoiceSetting['show_delivery_challan_item_sku'] ?? true))
                                                    <p class="description-font-size">
                                                        Item Code: {{ $item->items->sku ?? null }}
                                                    </p>
                                                @endif
                                                <p class="text-black description-font-size text-start vertical-align-top" style="word-break: break-word;">
                                                    {!! !empty($item->consolidating_items_to_invoice)
                                                        ? '(' . consolidatingItemsToInvoice($item->consolidating_items_to_invoice) . ')' : null !!}
                                                </p>
                                            <p class="description-font-size lh-sm font-italic" style="word-break: break-word;">{!! !empty($item->additional_description) ? nl2br('(' . $item->additional_description . ')') : null !!}</p>
                                            @if (!empty($item->items->item_image) && ($invoiceSetting['show_delivery_challan_item_image'] ?? true))
                                                <img src="{{ $item->items->item_image }}" width="60"  height="60" style="margin-top: 4px">
                                            @endif
                                        </div>
                                    </td>
                                    @if (count($printCustomFields) > 0)
                                        @foreach ($printCustomFields as $customFieldItemsValue)
                                            <td class="table-contents-font-size text-center p-1 lh-sm"
                                                style="padding: 4px 8px 0 8px; ">
                                                {{ $customFieldItemsValue['value'] ?? '' }}
                                            </td>
                                        @endforeach
                                    @endif
                                    @if ($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                        <td class="table-contents-font-size text-center p-1 lh-sm fw-medium w-20">
                                            <div>
                                                <p class="text-nowrap table-contents-font-size">
                                                    {{ $item->primary_quantity }}
                                                    @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                                                    {{ $item->primary_unit_name }}
                                                    @endif
                                                </p>
                                            </div>
                                        </td>
                                    @endif
                                    @if (($showPrintSettings['delivery_challan_uom_enable'] ?? false) && $transactionItems->sum('secondary_quantity') != 0.0)
                                        <td class="table-contents-font-size text-center p-1 lh-sm">
                                            <div>
                                                <p class="text-nowrap table-contents-font-size">
                                                    {{ round($item->secondary_quantity,2) }}
                                                    @if($showPrintSettings['show_delivery_challan_unit'] ?? true)
                                                    {{ $item->secondary_unit_name }}
                                                    @endif
                                                </p>
                                            </div>
                                        </td>
                                    @endif
                                </tr>
                            @endforeach
                        </tbody>
                        <tfoot>
                            <tr>
                                <td></td>
                                <td class="table-headings-font-size fw-medium lh-sm fw-bold text-nowrap py-1 px-2">Total</td>
                                @if (count($customFieldItemsValues) > 0)
                                    @foreach ($customFieldItemsValues as $customFieldItemsValue)
                                        <td class="table-headings-font-size fw-medium lh-sm fw-bold text-nowrap py-1 px-2 text-center">
                                            {{ $cfNumberTypeFieldTotals[$customFieldItemsValue['custom_field_id']] ?? '' }}
                                        </td>
                                    @endforeach
                                @endif
                                @if ($showPrintSettings['show_delivery_challan_qty'] ?? true)
                                    <td class="table-headings-font-size fw-medium lh-sm fw-bold text-nowrap py-1 px-2 text-center">
                                        {{ getCurrencyFormat($transactionItems->sum('primary_quantity')) }}
                                    </td>
                                @endif
                                @if (($showPrintSettings['delivery_challan_uom_enable'] ?? false) && $transactionItems->sum('secondary_quantity') != 0.0)
                                    <td class="table-headings-font-size fw-medium lh-sm fw-bold text-nowrap py-1 px-2 text-center">
                                        {{ getCurrencyFormat($transactionItems->sum('secondary_quantity')) }}
                                    </td>
                                @endif
                            </tr>
                        </tfoot>
                    </table>
                @endif
                {{-- Item Table Section End --}}
            </div>

            <table class="w-100 qr-table">
                <tbody>
                    <tr>
                        @if (
                            ($invoiceSetting['delivery_challan_received_by'] ?? true) || ($invoiceSetting['delivery_challan_delivered_by'] ?? true) ||
                                $invoiceSetting['delivery_challan_signature'] || $showPrintSettings['show_delivery_challan_authorized_signatory'])
                            <td class="w-50  vertical-align-baseline">
                                <div class="d-flex px-2 my-3">
                                    <div class="w-100 bank-code d-flex align-items-end">
                                        @if ($invoiceSetting['delivery_challan_received_by'] ?? true)
                                            <div class="w-100">
                                                <p class="footer-headings-font-size mb-1 fw-semibold lh-sm">
                                                    {{ $changeLabel['delivery_challan_recived_by'] ?? 'Received By' }}
                                                </p>
                                                <p class="footer-contents-font-size mb-1 lh-sm">
                                                    Name:
                                                </p>
                                                <p class="footer-contents-font-size mb-1 lh-sm">
                                                    Comment:
                                                </p>
                                                <p class="footer-contents-font-size mb-1 lh-sm">
                                                    Date:
                                                </p>
                                                <p class="footer-contents-font-size mb-1 lh-sm">
                                                    Signature:
                                                </p>
                                            </div>
                                        @endif
                                        @if ($invoiceSetting['delivery_challan_delivered_by'] ?? true)
                                            <div class="w-100">
                                                <p class="footer-headings-font-size mb-1 fw-semibold lh-sm">
                                                    {{ $changeLabel['delivery_challan_delivered_by_label'] ?? 'Delivered By' }}
                                                </p>
                                                <p class="footer-contents-font-size mb-1 lh-sm">
                                                    Name:
                                                </p>
                                                <p class="footer-contents-font-size mb-1 lh-sm">
                                                    Comment:
                                                </p>
                                                <p class="footer-contents-font-size mb-1 lh-sm">
                                                    Date:
                                                </p>
                                                <p class="footer-contents-font-size mb-1 lh-sm">
                                                    Signature:
                                                </p>
                                            </div>
                                        @endif
                                    </div>
                                        <div class="sign-section ms-auto">
                                            @if ($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true)
                                                <p class="footer-contents-font-size fw-medium text-center lh-sm mb-1">
                                                    For, {{ strtoupper($currentCompany->trade_name) }}
                                                </p>
                                            @endif
                                            <div class="sign d-flex align-items-center p-2">
                                                <div class="d-flex align-items-center">
                                                    @if (($invoiceSetting['delivery_challan_signature'] ?? false) && $currentCompany->company_signature != asset('images/preview-img.png'))
                                                    <img src="{{ $currentCompany->company_signature ?? null }}"
                                                        alt="company_signature"
                                                        style="max-width:100%; height:100%;  object-fit:contain; margin-left:auto;">
                                                    @endif
                                                </div>
                                            </div>
                                            @if ($showPrintSettings['show_delivery_challan_authorized_signatory'] ?? true)
                                                <p class="footer-contents-font-size fw-medium text-center lh-sm">
                                                    {{ $changeLabel['authorized_signatory'] ?? 'Authorized Signatory' }}
                                                </p>
                                            @endif
                                        </div>
                                </div>
                            </td>
                        @endif
                        @if ((($itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT) && ($showPrintSettings['show_delivery_challan_in_words'] ?? true)) ||
                                (($showPrintSettings['show_estimate_narration'] ?? true) && $transaction->narration) ||
                                (($showPrintSettings['show_estimate_terms_and_conditions'] ?? true) && $transaction->term_and_condition))
                            <td class="w-25 vertical-align-top">
                                <div class="w-60 p-2 total-content total-head">
                                    @if (($itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT) && ($showPrintSettings['show_delivery_challan_in_words'] ?? true))
                                        <p class="table-contents-font-size mb-2 lh-sm fw-medium fw-bold">
                                            {{ $changeLabel['delivery_challan_in_words'] ?? 'In Words' }}:
                                        </p>
                                        <p class="table-contents-font-size mb-3 money fw-bold text-primary lh-sm">
                                            {{ getAmountToWord($total ?? '0.0') }} Only
                                        </p>
                                    @endif
                                    @if (($showPrintSettings['show_delivery_challan_narration'] ?? true) && $transaction->narration)
                                        <p class="note-font-size fw-medium mb-2 lh-sm">
                                            {{ $changeLabel['narration'] ?? 'Note' }}:
                                        </p>
                                        <p class="note-font-size mb-2 lh-sm">
                                            {!! nl2br($transaction->narration) !!}
                                        </p>
                                    @endif
                                    @if (($showPrintSettings['show_delivery_challan_terms_and_conditions'] ?? true) && $transaction->term_and_condition)
                                        <p class="fw-medium terms-and-conditions-font-size mb-2 lh-sm">
                                            {{ $changeLabel['terms_and_conditions'] ?? 'Terms and Conditions' }}:
                                        </p>
                                        <p class="terms-and-conditions-font-size mb-2 lh-sm">
                                            {!! nl2br($transaction->term_and_condition) !!}
                                        </p>
                                    @endif
                                </div>
                            </td>
                        @endif
                        @if ($itemType == \App\Models\DeliveryChallanTransaction::WITH_AMOUNT)
                            <td class="vertical-align-top h-100 max-width-275px overflow-hidden">
                                <div class="total-head py-2 max-width-275px ms-auto h-100 after-border-content">
                                    <table class="w-100 total-table">
                                        <tbody>
                                            @foreach ($additionalCharges as $additionalCharge)
                                                <tr class="{{ $loop->last ? 'border-bottom' : '' }}">
                                                    <td>
                                                        <p class="table-contents-font-size mb-1 fw-medium text-start lh-sm px-2">
                                                            {{ $additionalCharge['ledger_name'] }}:
                                                        </p>
                                                    </td>
                                                    <td>
                                                        <p class="table-contents-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                            {{ $pdfSymbol . getCurrencyFormat($additionalCharge['amount'] ?? '0.0') }}
                                                        </p>
                                                    </td>
                                                </tr>
                                            @endforeach
                                            <tr>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                        {{ $changeLabel['delivery_challan_sub_total'] ?? ($isCompanyGstApplicable ? 'Taxable Value' : 'Sub Total') }}:
                                                    </p>
                                                </td>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                        {{ $pdfSymbol . getCurrencyFormat(round($transaction->taxable_value, getCompanyFixedDigitNumber())) }}
                                                    </p>
                                                </td>
                                            </tr>
                                            @if ($isCompanyGstApplicable)
                                                @if ($transaction->cgst != 0)
                                                    <tr>
                                                        <td>
                                                            <p
                                                                class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                                {{ $changeLabel['delivery_challan_cgst'] ?? 'CGST' }}:
                                                            </p>
                                                        </td>
                                                        <td>
                                                            <p
                                                                class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                                {{ $pdfSymbol . getCurrencyFormat(round($transaction->cgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                            </p>
                                                        </td>
                                                    </tr>
                                                @endif
                                                @if ($transaction->sgst != 0)
                                                    <tr>
                                                        <td>
                                                            <p
                                                                class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                                {{ $changeLabel['delivery_challan_sgst'] ?? 'SGST' }}:
                                                            </p>
                                                        </td>
                                                        <td>
                                                            <p
                                                                class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                                {{ $pdfSymbol . getCurrencyFormat(round($transaction->sgst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                            </p>
                                                        </td>
                                                    </tr>
                                                @endif
                                                @if ($transaction->igst != 0)
                                                    <tr>
                                                        <td>
                                                            <p
                                                                class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                                {{ $changeLabel['delivery_challan_igst'] ?? 'IGST' }}:
                                                            </p>
                                                        </td>
                                                        <td>
                                                            <p
                                                                class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                                {{ $pdfSymbol . getCurrencyFormat(round($transaction->igst, getCompanyFixedDigitNumber()) ?? '0.0') }}
                                                            </p>
                                                        </td>
                                                    </tr>
                                                @endif
                                            @endif
                                            @if ($transaction->tcs_amount != 0)
                                                <tr>
                                                    <td>
                                                        <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                            {{ $changeLabel['delivery_challan_tcs'] ?? 'TCS' }}:
                                                        </p>
                                                    </td>
                                                    <td>
                                                        <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                            {{ $pdfSymbol . getCurrencyFormat($transaction->tcs_amount ?? '0.0') }}
                                                        </p>
                                                    </td>
                                                </tr>
                                            @endif
                                            @if ($transaction->cess != 0)
                                                <tr>
                                                    <td>
                                                        <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                            {{ $changeLabel['delivery_challan_cess'] ?? 'Cess' }}:
                                                        </p>
                                                    </td>
                                                    <td>
                                                        <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                            {{ $pdfSymbol . getCurrencyFormat($transaction->cess ?? '0.0') }}
                                                        </p>
                                                    </td>
                                                </tr>
                                            @endif
                                            <tr>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-start lh-sm px-2">
                                                        {{ $changeLabel['delivery_challan_round_off'] ?? 'Round off' }}:
                                                    </p>
                                                </td>
                                                <td>
                                                    <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                        {{ $pdfSymbol . getCurrencyFormat($transaction->rounding_amount ?? '0.0') }}
                                                    </p>
                                                </td>
                                            </tr>

                                        @foreach (collect($addLess)->where('is_show_in_print',1) as $addLessItem)
                                                <tr class="{{ $loop->first ? 'border-top' : '' }}">
                                                    <td>
                                                        <p class="table-headings-font-size mb-1 fw-medium text-start lh-sm px-2">
                                                            {{ $addLessItem['ledger_name'] }}
                                                        </p>
                                                    </td>
                                                    <td>
                                                        <p class="table-headings-font-size mb-1 text-nowrap fw-medium text-end lh-sm px-2">
                                                            {{ $pdfSymbol . getCurrencyFormat($addLessItem['amount'] ?? '0.0') }}
                                                        </p>
                                                    </td>
                                                </tr>
                                            @endforeach
                                            <tr class="border-top">
                                                <td>
                                                    <p
                                                        class="total-font-size text-primary mb-2 text-nowrap fw-medium text-start lh-sm px-2 mt-1">
                                                        {{ $changeLabel['total'] ?? 'Total' }}:
                                                    </p>
                                                </td>
                                                <td>
                                                    <p
                                                        class="total-font-size mb-2 text-nowrap text-end text-primary fw-bold lh-sm px-2 mt-1">
                                                        {{ $pdfSymbol . getCurrencyFormat($total ?? '0.0') }}
                                                    </p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        @endif
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>

</html>
