<?php

use Illuminate\Support\Facades\Facade;
use Illuminate\Support\ServiceProvider;

return [

    /*
    |--------------------------------------------------------------------------
    | Application Name
    |--------------------------------------------------------------------------
    |
    | This value is the name of your application. This value is used when the
    | framework needs to place the application's name in a notification or
    | any other location as required by the application or its packages.
    |
    */

    'name' => env('APP_NAME', 'Laravel'),
    'email' => env('APP_EMAIL', '<EMAIL>'),

    /*
    |--------------------------------------------------------------------------
    | Google Sheet Variables
    |--------------------------------------------------------------------------
    |
    | This value is the name of your sheet and Id.
    */

    'sheet_id' => env('GOOGLE_SHEET_ID', '10a_7k5x-UWBFiKgJ2V_rNipGY0lvwDz2Ij__R7t8gYw'),
    'sheet_name' => env('GOOGLE_SHEET_NAME', 'Sheet1'),

    /*
    |--------------------------------------------------------------------------
    | Application Environment
    |--------------------------------------------------------------------------
    |
    | This value determines the "environment" your application is currently
    | running in. This may determine how you prefer to configure various
    | services the application utilizes. Set this in your ".env" file.
    |
    */

    'env' => env('APP_ENV', 'production'),

    /*
    |--------------------------------------------------------------------------
    | Application Debug Mode
    |--------------------------------------------------------------------------
    |
    | When your application is in debug mode, detailed error messages with
    | stack traces will be shown on every error that occurs within your
    | application. If disabled, a simple generic error page is shown.
    |
    */

    'debug' => (bool) env('APP_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Application URL
    |--------------------------------------------------------------------------
    |
    | This URL is used by the console to properly generate URLs when using
    | the Artisan command line tool. You should set this to the root of
    | your application so that it is used when running Artisan tasks.
    |
    */

    'url' => env('APP_URL', 'http://localhost'),

    'py_ocr_token' => env('PY_OCR_TOKEN', 'Test'),

    'ocr_service_url_for_bank_statement' => env('OCR_SERVICE_URL_FOR_BANK_STATEMENT', 'http://************:8000/hk_ocr_extract_bank_statement'),

    'ocr_service_url_for_transaction' => env('OCR_SERVICE_URL_FOR_TRANSACTION', 'http://************:8000/hk_ocr_extract'),

    'ocr_service_url_for_feedback' => env('OCR_SERVICE_URL_FOR_FEEDBACK', 'http://************:8000/hk_ocr_extract_feedback'),

    'asset_url' => env('ASSET_URL'),

    'media_disc' => env('MEDIA_DISK', 'public'),

    'google_tracking_id' => env('GOOGLE_TRACKING_ID', null),

    'backup_disc' => env('BACKUP_DISK', 'public'),

    'google_tracking_id' => env('GOOGLE_TRACKING_ID', null),
    'clarity_id' => env('CLARITY_ID', null),

    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. We have gone
    | ahead and set this to a sensible default for you out of the box.
    |
    */

    'timezone' => 'Asia/Kolkata',

    /*
    |--------------------------------------------------------------------------
    | Application Locale Configuration
    |--------------------------------------------------------------------------
    |
    | The application locale determines the default locale that will be used
    | by the translation service provider. You are free to set this value
    | to any of the locales which will be supported by the application.
    |
    */

    'locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Application Fallback Locale
    |--------------------------------------------------------------------------
    |
    | The fallback locale determines the locale to use when the current one
    | is not available. You may change the value to correspond to any of
    | the language folders that are provided through your application.
    |
    */

    'fallback_locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Faker Locale
    |--------------------------------------------------------------------------
    |
    | This locale will be used by the Faker PHP library when generating fake
    | data for your database seeds. For example, this will be used to get
    | localized telephone numbers, street address information and more.
    |
    */

    'faker_locale' => 'en_US',

    /*
    |--------------------------------------------------------------------------
    | Encryption Key
    |--------------------------------------------------------------------------
    |
    | This key is used by the Illuminate encrypter service and should be set
    | to a random, 32 character string, otherwise these encrypted strings
    | will not be safe. Please do this before deploying an application!
    |
    */

    'key' => env('APP_KEY'),

    'otp_testing' => env('OTP_TESTING', false),

    'cipher' => 'AES-256-CBC',

    'wp_authtoken' => env('WP_AUTHTOKEN', ''),

    'test_wp_number' => env('TEST_WP_NUMBER', ''),

    'test_wp_company' => env('TEST_WP_COMPANY', ''),

    'test_wp_daily_report' => env('TEST_WP_DAILY_REPORT', false),

    'send_mail_when_company_register' => env('SEND_MAIL_WHEN_COMPANY_REGISTER', ''),

    'wp_phone' => env('WP_PHONE', '917285878601'),

    'wp_password' => env('WP_PASSWORD', '917285878601'),

    'wp_user_id' => env('WP_USER_ID', ''),

    'wp_quota' => env('WP_QUOTA', '3'),

    'wp_testing' => env('WP_TESTING', false),

    'wp_webhook_username' => env('WP_WEBHOOK_USERNAME', ''),
    'wp_webhook_password' => env('WP_WEBHOOK_PASSWORD', ''),

    'onesignal_app_id' => env('ONESIGNAL_APP_ID', ''),

    'onesignal_rest_api_key' => env('ONESIGNAL_REST_API_KEY', ''),

    'is_check_validation' => env('IS_CHECK_VALIDATION', false),

    'is_validation_testing' => env('IS_VALIDATION_TESTING', false),

    'is_update_value_with_validation' => env('IS_UPDATE_VALUE_WITH_VALIDATION', false),

    'is_show_error' => env('IS_SHOW_ERROR', false),

    'vastra_url' => env('VASTRA_URL', "http://webstaging.vastraapp.com:3004/api/v1/tally/"),

    /*
    |--------------------------------------------------------------------------
    | Maintenance Mode Driver
    |--------------------------------------------------------------------------
    |
    | These configuration options determine the driver used to determine and
    | manage Laravel's "maintenance mode" status. The "cache" driver will
    | allow maintenance mode to be controlled across multiple machines.
    |
    | Supported drivers: "file", "cache"
    |
    */

    'maintenance' => [
        'driver' => 'file',
        // 'store'  => 'redis',
    ],

    /*
    |--------------------------------------------------------------------------
    | Autoloaded Service Providers
    |--------------------------------------------------------------------------
    |
    | The service providers listed here will be automatically loaded on the
    | request to your application. Feel free to add your own services to
    | this array to grant expanded functionality to your applications.
    |
    */

    'providers' => ServiceProvider::defaultProviders()->merge([
        Spatie\Permission\PermissionServiceProvider::class,
        SimpleSoftwareIO\QrCode\QrCodeServiceProvider::class,

        /*
         * Package Service Providers...
         */
        Barryvdh\Debugbar\ServiceProvider::class,
        Barryvdh\LaravelIdeHelper\IdeHelperServiceProvider::class,
        Prettus\Repository\Providers\RepositoryServiceProvider::class,
        Yajra\DataTables\DataTablesServiceProvider::class,
        Barryvdh\DomPDF\ServiceProvider::class,
        Lab404\Impersonate\ImpersonateServiceProvider::class,
        Anhskohbo\NoCaptcha\NoCaptchaServiceProvider::class,

        /*
         * Application Service Providers...
         */
        App\Providers\AppServiceProvider::class,
        App\Providers\AuthServiceProvider::class,
        // App\Providers\BroadcastServiceProvider::class,
        App\Providers\EventServiceProvider::class,
        App\Providers\RouteServiceProvider::class,
        App\Providers\TelescopeServiceProvider::class,
        Jenssegers\Agent\AgentServiceProvider::class,
        App\Providers\TelescopeServiceProvider::class,
        App\Providers\MailServiceProvider::class,
        Berkayk\OneSignal\OneSignalServiceProvider::class,
        Laravel\Socialite\SocialiteServiceProvider::class,
        OwenIt\Auditing\AuditingServiceProvider::class,
    ])->toArray(),

    /*
    |--------------------------------------------------------------------------
    | Class Aliases
    |--------------------------------------------------------------------------
    |
    | This array of class aliases will be registered when this application
    | is started. However, feel free to register as many as you wish as
    | the aliases are "lazy" loaded so they don't hinder performance.
    |
    */

    'aliases' => Facade::defaultAliases()->merge([
        // 'ExampleClass' => App\Example\ExampleClass::class,
        'Debugger' => Barryvdh\Debugbar\Facades\Debugbar::class,
        'DataTables' => Yajra\DataTables\Facades\DataTables::class,
        'PDF' => Barryvdh\DomPDF\Facade\Pdf::class,
        'NoCaptcha' => Anhskohbo\NoCaptcha\Facades\NoCaptcha::class,
        'QrCode' => SimpleSoftwareIO\QrCode\Facades\QrCode::class,
        'Agent' => Jenssegers\Agent\Facades\Agent::class,
        'OneSignal' => Berkayk\OneSignal\OneSignalFacade::class,
        'Socialite' => Laravel\Socialite\Facades\Socialite::class,
    ])->toArray(),

];
